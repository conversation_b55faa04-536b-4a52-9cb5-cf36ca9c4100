# =====================================================
# ENVIRONMENT VARIABLES TEMPLATE
# Copy this file to .env and fill in your actual values
# =====================================================

# Application Environment
ENVIRONMENT=development
API_BASE_URL=https://api.deliveryapp.africa
APP_VERSION=1.0.0

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Google Services
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
GOOGLE_PLACES_API_KEY=your-google-places-api-key
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_MESSAGING_SENDER_ID=your-sender-id
FIREBASE_APP_ID=your-firebase-app-id

# Push Notifications
FCM_SERVER_KEY=your-fcm-server-key
ONESIGNAL_APP_ID=your-onesignal-app-id
ONESIGNAL_REST_API_KEY=your-onesignal-rest-api-key

# SMS Services
AFRICAS_TALKING_API_KEY=your-africas-talking-api-key
AFRICAS_TALKING_USERNAME=your-africas-talking-username
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# Payment Providers - Mobile Money
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
MPESA_SHORTCODE=your-mpesa-shortcode
MPESA_PASSKEY=your-mpesa-passkey
MPESA_WEBHOOK_SECRET=your-mpesa-webhook-secret

ORANGE_MONEY_CLIENT_ID=your-orange-money-client-id
ORANGE_MONEY_CLIENT_SECRET=your-orange-money-client-secret
ORANGE_MONEY_MERCHANT_KEY=your-orange-money-merchant-key
ORANGE_MONEY_API_KEY=your-orange-money-api-key
ORANGE_MONEY_WEBHOOK_SECRET=your-orange-money-webhook-secret

WAVE_API_KEY=your-wave-api-key
WAVE_SECRET_KEY=your-wave-secret-key
WAVE_WEBHOOK_SECRET=your-wave-webhook-secret

MTN_MOMO_SUBSCRIPTION_KEY=your-mtn-momo-subscription-key
MTN_MOMO_USER_ID=your-mtn-momo-user-id
MTN_MOMO_API_KEY=your-mtn-momo-api-key
MTN_MOMO_WEBHOOK_SECRET=your-mtn-momo-webhook-secret

# Payment Providers - Cards
PAYSTACK_PUBLIC_KEY=your-paystack-public-key
PAYSTACK_SECRET_KEY=your-paystack-secret-key
PAYSTACK_WEBHOOK_SECRET=your-paystack-webhook-secret

FLUTTERWAVE_PUBLIC_KEY=your-flutterwave-public-key
FLUTTERWAVE_SECRET_KEY=your-flutterwave-secret-key
FLUTTERWAVE_ENCRYPTION_KEY=your-flutterwave-encryption-key
FLUTTERWAVE_WEBHOOK_SECRET=your-flutterwave-webhook-secret

# Analytics and Monitoring
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=delivery-app-africa
SENTRY_PROJECT=mobile-app
MIXPANEL_TOKEN=your-mixpanel-token
AMPLITUDE_API_KEY=your-amplitude-api-key

# Social Media Integration
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
GOOGLE_OAUTH_CLIENT_ID=your-google-oauth-client-id
APPLE_TEAM_ID=your-apple-team-id

# Email Services
SENDGRID_API_KEY=your-sendgrid-api-key
MAILGUN_API_KEY=your-mailgun-api-key
MAILGUN_DOMAIN=your-mailgun-domain

# African Market Specific
DEFAULT_COUNTRY=SEN
DEFAULT_CURRENCY=XOF
DEFAULT_LANGUAGE=fr
SUPPORTED_COUNTRIES=SEN,KEN,GHA,NGA,CIV,MLI,BFA,UGA,TZA,RWA

# Performance Optimizations
OFFLINE_SUPPORT=true
LOW_BANDWIDTH_MODE=true
COMPRESSION_ENABLED=true
CACHE_SIZE=50MB
SYNC_INTERVAL=300000
MAX_RETRIES=3
REQUEST_TIMEOUT=30000

# Development Settings
ENABLE_LOGGING=true
ENABLE_FLIPPER=true
DEBUG_MODE=true
MOCK_PAYMENTS=false
MOCK_LOCATION=false

# Build Configuration
EAS_PROJECT_ID=your-eas-project-id
APPLE_TEAM_ID=your-apple-team-id
GOOGLE_SERVICES_JSON_PATH=./google-services.json
GOOGLE_PLAY_SERVICE_ACCOUNT_PATH=./google-play-service-account.json

# Deep Linking
DEEP_LINK_SCHEME=deliveryapp
DEEP_LINK_DOMAIN=deliveryapp.africa
UNIVERSAL_LINK_DOMAIN=deliveryapp.africa

# Security
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key
API_RATE_LIMIT=100
SESSION_TIMEOUT=3600000

# Feature Flags
ENABLE_BIOMETRIC_AUTH=true
ENABLE_DARK_MODE=true
ENABLE_MULTI_LANGUAGE=true
ENABLE_OFFLINE_MAPS=true
ENABLE_VOICE_NOTES=true
ENABLE_VIDEO_CALLS=false
ENABLE_LIVE_CHAT=true
ENABLE_REFERRAL_PROGRAM=true
ENABLE_LOYALTY_POINTS=true

# Regional Settings
TIMEZONE_AFRICA_DAKAR=Africa/Dakar
TIMEZONE_AFRICA_NAIROBI=Africa/Nairobi
TIMEZONE_AFRICA_LAGOS=Africa/Lagos
TIMEZONE_AFRICA_ACCRA=Africa/Accra
TIMEZONE_AFRICA_CASABLANCA=Africa/Casablanca

# Currency Exchange
EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key
BASE_CURRENCY=USD
UPDATE_RATES_INTERVAL=3600000

# Delivery Optimization
MAX_DELIVERY_RADIUS_KM=50
DEFAULT_DELIVERY_FEE=500
URGENT_DELIVERY_MULTIPLIER=1.5
BULK_ORDER_DISCOUNT=0.1
FREE_DELIVERY_THRESHOLD=5000

# Notification Settings
NOTIFICATION_SOUND_ENABLED=true
VIBRATION_ENABLED=true
QUIET_HOURS_START=22:00
QUIET_HOURS_END=07:00
MAX_NOTIFICATIONS_PER_DAY=20

# Content Delivery
CDN_BASE_URL=https://cdn.deliveryapp.africa
IMAGE_COMPRESSION_QUALITY=0.8
VIDEO_COMPRESSION_ENABLED=true
LAZY_LOADING_ENABLED=true

# Backup and Sync
BACKUP_INTERVAL=86400000
SYNC_ON_WIFI_ONLY=false
AUTO_BACKUP_ENABLED=true
CLOUD_STORAGE_PROVIDER=supabase
