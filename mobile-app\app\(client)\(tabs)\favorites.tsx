// =====================================================
// ÉCRAN FAVORIS CLIENT
// =====================================================

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Merchant } from '../../../services/homeServiceFallback';

// Mock favorites data - will be replaced with real data from store
const mockFavorites: Merchant[] = [
  {
    id: '1',
    name: 'Chez Fatou Restaurant',
    image_url: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400',
    rating: 4.8,
    deliveryTime: '25-35 min',
    deliveryFee: 500,
    category: 'Restaurants',
    isPromoted: true,
    description: 'Cuisine sénégalaise authentique avec les meilleurs thieboudienne et yassa de Dakar',
    address: 'Plateau, Dakar, Sénégal',
    is_open: true,
  },
  {
    id: '3',
    name: 'Pharmacie du Plateau',
    image_url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400',
    rating: 4.7,
    deliveryTime: '15-25 min',
    deliveryFee: 300,
    category: 'Pharmacie',
    isPromoted: false,
    description: 'Pharmacie moderne avec service de livraison rapide 24h/24',
    address: 'Avenue Léopold Sédar Senghor, Dakar',
    is_open: true,
  },
];

export default function FavoritesScreen() {
  const [favorites, setFavorites] = useState<Merchant[]>(mockFavorites);
  const [selectedTab, setSelectedTab] = useState<'merchants' | 'products'>('merchants');

  const handleMerchantPress = (merchant: Merchant) => {
    router.push(`/(client)/restaurant/${merchant.id}` as any);
  };

  const handleRemoveFavorite = (merchantId: string) => {
    setFavorites(prev => prev.filter(item => item.id !== merchantId));
  };

  const renderMerchant = ({ item }: { item: Merchant }) => (
    <TouchableOpacity
      style={styles.merchantCard}
      onPress={() => handleMerchantPress(item)}
    >
      <Image
        source={{
          uri: item.image_url || 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400'
        }}
        style={styles.merchantImage}
      />
      <View style={styles.merchantInfo}>
        <View style={styles.merchantHeader}>
          <Text style={styles.merchantName}>{item.name}</Text>
          <TouchableOpacity
            style={styles.favoriteButton}
            onPress={() => handleRemoveFavorite(item.id)}
          >
            <Ionicons name="heart" size={20} color="#FF3B30" />
          </TouchableOpacity>
        </View>
        <Text style={styles.merchantDescription} numberOfLines={2}>
          {item.description}
        </Text>
        <View style={styles.merchantMeta}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color="#FFD700" />
            <Text style={styles.rating}>{item.rating}</Text>
          </View>
          <Text style={styles.deliveryTime}>{item.deliveryTime}</Text>
          <Text style={styles.deliveryFee}>{item.deliveryFee} FCFA</Text>
        </View>
        <Text style={styles.merchantCategory}>{item.category}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="heart-outline" size={64} color="#8E8E93" />
      <Text style={styles.emptyTitle}>Aucun favori</Text>
      <Text style={styles.emptyDescription}>
        {selectedTab === 'merchants'
          ? 'Vos restaurants favoris apparaîtront ici'
          : 'Vos produits favoris apparaîtront ici'
        }
      </Text>
      <TouchableOpacity
        style={styles.exploreButton}
        onPress={() => router.push('/(client)/(tabs)/search')}
      >
        <Text style={styles.exploreButtonText}>Explorer</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Mes Favoris</Text>
        <TouchableOpacity>
          <Ionicons name="search" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'merchants' && styles.activeTab]}
          onPress={() => setSelectedTab('merchants')}
        >
          <Text style={[styles.tabText, selectedTab === 'merchants' && styles.activeTabText]}>
            Restaurants ({favorites.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'products' && styles.activeTab]}
          onPress={() => setSelectedTab('products')}
        >
          <Text style={[styles.tabText, selectedTab === 'products' && styles.activeTabText]}>
            Produits (0)
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <FlatList
        data={selectedTab === 'merchants' ? favorites : []}
        renderItem={renderMerchant}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.favoritesList}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1C1C1E',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8E8E93',
  },
  activeTabText: {
    color: '#007AFF',
  },
  favoritesList: {
    padding: 24,
  },
  merchantCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  merchantImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
    marginRight: 16,
  },
  merchantInfo: {
    flex: 1,
  },
  merchantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  merchantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    flex: 1,
    marginRight: 8,
  },
  favoriteButton: {
    padding: 4,
  },
  merchantDescription: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 8,
    lineHeight: 20,
  },
  merchantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  rating: {
    fontSize: 12,
    color: '#8E8E93',
  },
  deliveryTime: {
    fontSize: 12,
    color: '#8E8E93',
  },
  deliveryFee: {
    fontSize: 12,
    color: '#34C759',
    fontWeight: '500',
  },
  merchantCategory: {
    fontSize: 12,
    color: '#8E8E93',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 24,
  },
  exploreButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  exploreButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
