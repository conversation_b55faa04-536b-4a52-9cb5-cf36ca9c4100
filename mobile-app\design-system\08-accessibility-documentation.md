# DOCUMENTATION ACCESSIBILITÉ - DESIGN SYSTEM AFRIQUE

## PHILOSOPHIE D'ACCESSIBILITÉ

### Inclusion Universelle
Notre approche d'accessibilité s'inspire du principe Ubuntu : "Je suis parce que nous sommes". Chaque utilisateur, quelles que soient ses capacités, doit pouvoir utiliser notre application avec dignité et autonomie.

#### Principes Fondamentaux
1. **PERCEPTIBLE** - Information et composants UI présentés de façon perceptible
2. **UTILISABLE** - Composants UI et navigation utilisables par tous
3. **COMPRÉHENSIBLE** - Information et fonctionnement UI compréhensibles
4. **ROBUSTE** - Contenu interprétable par diverses technologies d'assistance

## STANDARDS ET CONFORMITÉ

### Niveaux de Conformité WCAG 2.1
- **Niveau A** : Conformité minimale (requis)
- **Niveau AA** : Conformité standard (objectif)
- **Niveau AAA** : Conformité maximale (idéal)

#### Notre Engagement
- ✅ **WCAG 2.1 AA** : Conformité complète
- 🎯 **WCAG 2.1 AAA** : Objectif pour éléments critiques
- 📱 **Mobile Accessibility** : Guidelines iOS et Android
- 🌍 **EN 301 549** : Standard européen d'accessibilité

## ACCESSIBILITÉ VISUELLE

### Contrastes de Couleurs

#### Ratios de Contraste Requis
```css
/* Texte normal (< 18px ou < 14px gras) */
--contrast-aa-normal: 4.5:1;    /* Minimum requis */
--contrast-aaa-normal: 7:1;     /* Idéal */

/* Texte large (≥ 18px ou ≥ 14px gras) */
--contrast-aa-large: 3:1;       /* Minimum requis */
--contrast-aaa-large: 4.5:1;    /* Idéal */

/* Éléments non-textuels */
--contrast-ui-elements: 3:1;    /* Bordures, icônes, états focus */
```

#### Validation des Contrastes
```typescript
// utils/contrast-checker.ts
export const checkContrast = (foreground: string, background: string): {
  ratio: number;
  aa: boolean;
  aaa: boolean;
  aaLarge: boolean;
  aaaLarge: boolean;
} => {
  const ratio = calculateContrastRatio(foreground, background);
  
  return {
    ratio,
    aa: ratio >= 4.5,
    aaa: ratio >= 7,
    aaLarge: ratio >= 3,
    aaaLarge: ratio >= 4.5,
  };
};

// Exemple d'utilisation
const textContrast = checkContrast('#000000', '#FFFFFF');
console.log(textContrast); // { ratio: 21, aa: true, aaa: true, ... }
```

#### Palette Accessible
```css
/* Combinaisons validées AA/AAA */
.text-primary-on-light {
  color: #171717;           /* Ratio: 16.1:1 (AAA) */
  background: #FFFFFF;
}

.text-secondary-on-light {
  color: #525252;           /* Ratio: 7.1:1 (AAA) */
  background: #FFFFFF;
}

.text-on-primary {
  color: #FFFFFF;           /* Ratio: 4.8:1 (AA) */
  background: #F97316;
}

.text-on-dark {
  color: #FFFFFF;           /* Ratio: 21:1 (AAA) */
  background: #000000;
}
```

### Support Daltonisme

#### Types de Daltonisme Supportés
- **Protanopie** (rouge) : 1% hommes, 0.01% femmes
- **Deutéranopie** (vert) : 1% hommes, 0.01% femmes
- **Tritanopie** (bleu) : 0.003% population
- **Achromatopsie** : Vision en niveaux de gris

#### Stratégies d'Adaptation
```typescript
// Patterns visuels en plus des couleurs
const StatusIndicators = {
  success: {
    color: '#22C55E',
    icon: 'check-circle',
    pattern: 'solid',
  },
  warning: {
    color: '#F59E0B',
    icon: 'alert-triangle',
    pattern: 'diagonal-stripes',
  },
  error: {
    color: '#EF4444',
    icon: 'x-circle',
    pattern: 'dots',
  },
};
```

### Visibilité en Conditions Difficiles

#### Optimisations Solaires
- **Contrastes élevés** : Privilégier noir/blanc
- **Éviter couleurs problématiques** : Bleus clairs, jaunes pâles
- **Ombres subtiles** : Améliorer définition des éléments
- **Bordures marquées** : Délimiter zones interactives

#### Mode Haute Visibilité
```css
/* Mode haute visibilité */
@media (prefers-contrast: high) {
  :root {
    --color-text: #000000;
    --color-background: #FFFFFF;
    --color-border: #000000;
    --border-width: 2px;
    --font-weight-base: 600;
  }
}
```

## ACCESSIBILITÉ MOTRICE

### Zones de Touch Optimisées

#### Tailles Minimales
```css
/* Cibles tactiles - Guidelines Apple/Google */
--touch-target-minimum: 44px;      /* iOS minimum */
--touch-target-android: 48px;      /* Android minimum */
--touch-target-comfortable: 56px;  /* Recommandé Afrique */
--touch-target-large: 64px;        /* Accessibilité étendue */
```

#### Espacement Entre Cibles
```css
/* Espacement minimum entre éléments interactifs */
--touch-spacing-minimum: 8px;      /* Minimum absolu */
--touch-spacing-comfortable: 16px; /* Recommandé */
--touch-spacing-generous: 24px;    /* Optimal */
```

### Usage Une Main

#### Zones d'Accessibilité
```typescript
// Zones de confort pour usage une main
export const ThumbZones = {
  // Zone facile (pouce droit)
  easy: {
    x: [60, 100], // % de largeur écran
    y: [60, 100], // % de hauteur écran
  },
  
  // Zone moyenne
  medium: {
    x: [20, 80],
    y: [40, 80],
  },
  
  // Zone difficile
  hard: {
    x: [0, 40],
    y: [0, 40],
  },
};

// Placement optimal des éléments
export const OptimalPlacement = {
  primaryActions: ThumbZones.easy,
  navigation: ThumbZones.easy,
  secondaryActions: ThumbZones.medium,
  information: ThumbZones.hard, // Lecture seule
};
```

### Gestes Alternatifs

#### Support Multi-Modal
```typescript
// Gestes supportés avec alternatives
export const GestureAlternatives = {
  swipe: {
    primary: 'swipe',
    alternative: 'button',
    description: 'Boutons fléchés pour navigation',
  },
  
  pinchZoom: {
    primary: 'pinch',
    alternative: 'double-tap',
    description: 'Double-tap pour zoom, boutons +/- disponibles',
  },
  
  longPress: {
    primary: 'long-press',
    alternative: 'menu-button',
    description: 'Bouton menu contextuel visible',
  },
};
```

## ACCESSIBILITÉ COGNITIVE

### Réduction Charge Cognitive

#### Principes de Simplicité
- **Une action par écran** : Focus sur tâche principale
- **Progression claire** : Étapes numérotées et visuelles
- **Feedback immédiat** : Confirmation de chaque action
- **Récupération d'erreur** : Correction facile et guidée

#### Patterns Familiers
```typescript
// Utilisation de patterns reconnus
export const FamiliarPatterns = {
  navigation: 'bottom-tabs',      // Pattern mobile universel
  search: 'top-search-bar',       // Pattern Google/Facebook
  cart: 'floating-action-button', // Pattern e-commerce
  back: 'top-left-arrow',         // Pattern iOS/Android
};
```

### Support Mémoire

#### Persistance d'État
```typescript
// Sauvegarde automatique du contexte
export const MemorySupport = {
  formData: 'auto-save-every-30s',
  navigation: 'remember-last-position',
  preferences: 'persist-user-choices',
  progress: 'save-completion-state',
};
```

#### Rappels Contextuels
```typescript
// Aide contextuelle
export const ContextualHelp = {
  tooltips: 'on-focus-or-hover',
  hints: 'progressive-disclosure',
  examples: 'inline-demonstrations',
  recovery: 'undo-redo-available',
};
```

## ACCESSIBILITÉ AUDITIVE

### Support Malentendants

#### Alternatives Visuelles
```typescript
// Feedback visuel pour audio
export const VisualFeedback = {
  notifications: {
    audio: 'notification-sound',
    visual: 'flash-animation + banner',
    haptic: 'vibration-pattern',
  },
  
  alerts: {
    audio: 'alert-beep',
    visual: 'color-change + icon',
    haptic: 'double-vibration',
  },
  
  confirmations: {
    audio: 'success-chime',
    visual: 'checkmark-animation',
    haptic: 'single-vibration',
  },
};
```

#### Sous-titres et Transcriptions
```typescript
// Support contenu audio/vidéo
export const AudioSupport = {
  videos: {
    captions: 'always-available',
    transcripts: 'full-text-provided',
    controls: 'accessible-media-player',
  },
  
  voiceMessages: {
    transcription: 'automatic-speech-to-text',
    playback: 'visual-waveform',
    controls: 'play-pause-accessible',
  },
};
```

## TECHNOLOGIES D'ASSISTANCE

### Screen Readers

#### Support React Native
```typescript
// Configuration pour lecteurs d'écran
export const ScreenReaderSupport = {
  // Labels descriptifs
  accessibilityLabel: 'Description claire et concise',
  
  // Rôles sémantiques
  accessibilityRole: 'button | link | text | image | header',
  
  // États dynamiques
  accessibilityState: {
    disabled: boolean,
    selected: boolean,
    checked: boolean,
    busy: boolean,
  },
  
  // Hints d'utilisation
  accessibilityHint: 'Action qui sera effectuée',
  
  // Valeurs pour contrôles
  accessibilityValue: {
    min: number,
    max: number,
    now: number,
    text: string,
  },
};
```

#### Exemples d'Implémentation
```typescript
// Bouton accessible
<Pressable
  accessibilityRole="button"
  accessibilityLabel="Ajouter au panier"
  accessibilityHint="Ajoute cet article à votre panier d'achat"
  accessibilityState={{ disabled: isOutOfStock }}
  onPress={addToCart}
>
  <Text>Ajouter au panier</Text>
</Pressable>

// Input accessible
<TextInput
  accessibilityLabel="Adresse de livraison"
  accessibilityHint="Saisissez votre adresse complète de livraison"
  placeholder="123 Rue Example, Dakar"
  value={address}
  onChangeText={setAddress}
/>

// Navigation accessible
<View accessibilityRole="tablist">
  <Pressable
    accessibilityRole="tab"
    accessibilityState={{ selected: activeTab === 'home' }}
    onPress={() => setActiveTab('home')}
  >
    <Text>Accueil</Text>
  </Pressable>
</View>
```

### Navigation Clavier

#### Support Focus
```typescript
// Gestion du focus clavier
export const KeyboardNavigation = {
  // Ordre de tabulation logique
  tabIndex: 'sequential-logical-order',
  
  // Indicateurs de focus visibles
  focusIndicator: {
    outline: '3px solid #F97316',
    outlineOffset: '2px',
    borderRadius: '4px',
  },
  
  // Raccourcis clavier
  shortcuts: {
    'Tab': 'next-element',
    'Shift+Tab': 'previous-element',
    'Enter': 'activate-element',
    'Space': 'activate-button',
    'Escape': 'close-modal',
  },
};
```

## TESTS D'ACCESSIBILITÉ

### Tests Automatisés

#### Configuration Jest + Testing Library
```typescript
// __tests__/accessibility.test.tsx
import { render } from '@testing-library/react-native';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Accessibility Tests', () => {
  test('Button has proper accessibility attributes', () => {
    const { getByRole } = render(
      <Button title="Submit" onPress={() => {}} />
    );
    
    const button = getByRole('button');
    expect(button).toHaveAccessibilityRole('button');
    expect(button).toHaveAccessibilityLabel('Submit');
  });

  test('Form inputs have labels', () => {
    const { getByLabelText } = render(
      <Input 
        label="Email" 
        value="" 
        onChangeText={() => {}} 
      />
    );
    
    expect(getByLabelText('Email')).toBeTruthy();
  });
});
```

### Tests Manuels

#### Checklist de Validation
```markdown
## Checklist Accessibilité

### Navigation
- [ ] Navigation possible au clavier uniquement
- [ ] Ordre de tabulation logique
- [ ] Focus visible sur tous les éléments
- [ ] Pas de piège de focus

### Contenu
- [ ] Tous les textes ont un contraste suffisant (4.5:1 minimum)
- [ ] Images ont des textes alternatifs descriptifs
- [ ] Vidéos ont des sous-titres
- [ ] Contenu lisible sans CSS

### Interaction
- [ ] Tous les boutons ont des labels clairs
- [ ] États des éléments communiqués (sélectionné, désactivé)
- [ ] Messages d'erreur associés aux champs
- [ ] Timeouts peuvent être étendus

### Screen Reader
- [ ] Contenu lu dans l'ordre logique
- [ ] Landmarks et headings structurent le contenu
- [ ] Changements dynamiques annoncés
- [ ] Instructions d'utilisation fournies
```

## DOCUMENTATION UTILISATEUR

### Guide d'Accessibilité

#### Pour Utilisateurs
```markdown
# Guide d'Accessibilité - Application Livraison

## Fonctionnalités d'Accessibilité

### Navigation
- **Lecteur d'écran** : Compatible TalkBack (Android) et VoiceOver (iOS)
- **Navigation clavier** : Tous les éléments accessibles au clavier
- **Zoom** : Support zoom jusqu'à 200% sans perte de fonctionnalité

### Personnalisation
- **Taille de texte** : Ajustable dans Paramètres > Accessibilité
- **Contraste élevé** : Mode haute visibilité disponible
- **Réduction mouvement** : Respect des préférences système

### Support
- **Aide vocale** : Instructions audio disponibles
- **Support technique** : <EMAIL>
- **Hotline accessibilité** : +221 XX XXX XXXX
```

#### Pour Développeurs
```markdown
# Guide Développeur - Accessibilité

## Bonnes Pratiques

### Composants
- Toujours fournir `accessibilityLabel`
- Utiliser `accessibilityRole` approprié
- Implémenter `accessibilityState` pour états dynamiques

### Tests
- Tester avec lecteur d'écran activé
- Valider navigation clavier
- Vérifier contrastes avec outils automatisés

### Ressources
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [React Native Accessibility](https://reactnative.dev/docs/accessibility)
- [Testing Library Accessibility](https://testing-library.com/docs/guide-accessibility/)
```

Cette documentation garantit que notre application respecte les plus hauts standards d'accessibilité, permettant à tous les utilisateurs africains, quelles que soient leurs capacités, d'utiliser notre service avec autonomie et dignité.
