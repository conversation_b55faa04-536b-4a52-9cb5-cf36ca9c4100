/**
 * Component Library - Optimized for African Markets
 * Touch-friendly, accessible, and performance-optimized components
 */

import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  TextInput, 
  Image, 
  ActivityIndicator,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ImageStyle,
  Pressable,
  Alert,
} from 'react-native';
import { Colors } from '../constants/Colors';
import { Typography, getTypographyStyle } from './02-typography-system';

// Design System Spacing
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
} as const;

// Touch Target Sizes
export const TouchTargets = {
  small: 44,
  medium: 48,
  large: 56,
  xlarge: 64,
} as const;

// Border Radius
export const BorderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
} as const;

// =============================================================================
// BUTTON COMPONENT
// =============================================================================

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  fullWidth?: boolean;
  style?: ViewStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  fullWidth = false,
  style,
}) => {
  const buttonStyles = getButtonStyles(variant, size, disabled, fullWidth);
  const textStyles = getButtonTextStyles(variant, size, disabled);

  return (
    <Pressable
      style={({ pressed }) => [
        buttonStyles.container,
        pressed && buttonStyles.pressed,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      accessibilityRole="button"
      accessibilityState={{ disabled: disabled || loading }}
    >
      <View style={buttonStyles.content}>
        {loading ? (
          <ActivityIndicator 
            size="small" 
            color={textStyles.color} 
            style={buttonStyles.loader}
          />
        ) : (
          <>
            {icon && <View style={buttonStyles.icon}>{icon}</View>}
            <Text style={textStyles}>{title}</Text>
          </>
        )}
      </View>
    </Pressable>
  );
};

const getButtonStyles = (
  variant: ButtonProps['variant'],
  size: ButtonProps['size'],
  disabled: boolean,
  fullWidth: boolean
) => {
  const baseHeight = {
    small: TouchTargets.small,
    medium: TouchTargets.medium,
    large: TouchTargets.large,
  }[size!];

  const basePadding = {
    small: Spacing.md,
    medium: Spacing.lg,
    large: Spacing.xl,
  }[size!];

  const backgroundColor = {
    primary: disabled ? Colors.light.border : Colors.light.primary,
    secondary: disabled ? Colors.light.border : Colors.light.secondary,
    outline: 'transparent',
    ghost: 'transparent',
  }[variant!];

  const borderColor = {
    primary: backgroundColor,
    secondary: backgroundColor,
    outline: disabled ? Colors.light.border : Colors.light.primary,
    ghost: 'transparent',
  }[variant!];

  return StyleSheet.create({
    container: {
      height: baseHeight,
      backgroundColor,
      borderWidth: variant === 'outline' ? 2 : 0,
      borderColor,
      borderRadius: BorderRadius.md,
      width: fullWidth ? '100%' : 'auto',
      minWidth: baseHeight * 2,
    },
    content: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: basePadding,
    },
    pressed: {
      opacity: 0.8,
      transform: [{ scale: 0.98 }],
    },
    icon: {
      marginRight: Spacing.sm,
    },
    loader: {
      marginRight: Spacing.sm,
    },
  });
};

const getButtonTextStyles = (
  variant: ButtonProps['variant'],
  size: ButtonProps['size'],
  disabled: boolean
): TextStyle => {
  const typography = getTypographyStyle('button', size!);
  
  const color = {
    primary: disabled ? Colors.light.textTertiary : Colors.light.textInverse,
    secondary: disabled ? Colors.light.textTertiary : Colors.light.textInverse,
    outline: disabled ? Colors.light.textTertiary : Colors.light.primary,
    ghost: disabled ? Colors.light.textTertiary : Colors.light.primary,
  }[variant!];

  return {
    fontSize: typography.fontSize,
    fontWeight: typography.fontWeight,
    letterSpacing: typography.letterSpacing,
    color,
  };
};

// =============================================================================
// INPUT COMPONENT
// =============================================================================

interface InputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  disabled?: boolean;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'numeric' | 'email-address' | 'phone-pad';
  multiline?: boolean;
  numberOfLines?: number;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  style?: ViewStyle;
}

export const Input: React.FC<InputProps> = ({
  value,
  onChangeText,
  placeholder,
  label,
  error,
  disabled = false,
  secureTextEntry = false,
  keyboardType = 'default',
  multiline = false,
  numberOfLines = 1,
  leftIcon,
  rightIcon,
  style,
}) => {
  const inputStyles = getInputStyles(error, disabled);
  const labelStyles = getInputLabelStyles(error);

  return (
    <View style={[inputStyles.container, style]}>
      {label && <Text style={labelStyles}>{label}</Text>}
      <View style={inputStyles.inputContainer}>
        {leftIcon && <View style={inputStyles.leftIcon}>{leftIcon}</View>}
        <TextInput
          style={inputStyles.input}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={Colors.light.textTertiary}
          editable={!disabled}
          secureTextEntry={secureTextEntry}
          keyboardType={keyboardType}
          multiline={multiline}
          numberOfLines={numberOfLines}
          accessibilityLabel={label || placeholder}
          accessibilityState={{ disabled }}
        />
        {rightIcon && <View style={inputStyles.rightIcon}>{rightIcon}</View>}
      </View>
      {error && <Text style={inputStyles.error}>{error}</Text>}
    </View>
  );
};

const getInputStyles = (error?: string, disabled?: boolean) => {
  const borderColor = error 
    ? Colors.light.error 
    : disabled 
    ? Colors.light.border 
    : Colors.light.borderSecondary;

  return StyleSheet.create({
    container: {
      marginBottom: Spacing.md,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 2,
      borderColor,
      borderRadius: BorderRadius.md,
      backgroundColor: disabled ? Colors.light.backgroundTertiary : Colors.light.background,
      minHeight: TouchTargets.medium,
    },
    input: {
      flex: 1,
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      fontSize: Typography.body.medium.fontSize,
      color: disabled ? Colors.light.textTertiary : Colors.light.text,
    },
    leftIcon: {
      paddingLeft: Spacing.md,
    },
    rightIcon: {
      paddingRight: Spacing.md,
    },
    error: {
      marginTop: Spacing.xs,
      fontSize: Typography.caption.medium.fontSize,
      color: Colors.light.error,
    },
  });
};

const getInputLabelStyles = (error?: string): TextStyle => ({
  marginBottom: Spacing.xs,
  fontSize: Typography.label.medium.fontSize,
  fontWeight: Typography.label.medium.fontWeight,
  color: error ? Colors.light.error : Colors.light.text,
});

// =============================================================================
// CARD COMPONENT
// =============================================================================

interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: keyof typeof Spacing;
  style?: ViewStyle;
}

export const Card: React.FC<CardProps> = ({
  children,
  onPress,
  variant = 'default',
  padding = 'md',
  style,
}) => {
  const cardStyles = getCardStyles(variant, padding);

  const CardContent = (
    <View style={[cardStyles.container, style]}>
      {children}
    </View>
  );

  if (onPress) {
    return (
      <Pressable
        onPress={onPress}
        style={({ pressed }) => [
          pressed && cardStyles.pressed,
        ]}
        accessibilityRole="button"
      >
        {CardContent}
      </Pressable>
    );
  }

  return CardContent;
};

const getCardStyles = (variant: CardProps['variant'], padding: keyof typeof Spacing) => {
  const shadowStyle = {
    default: {},
    elevated: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    outlined: {
      borderWidth: 1,
      borderColor: Colors.light.border,
    },
  }[variant!];

  return StyleSheet.create({
    container: {
      backgroundColor: Colors.light.background,
      borderRadius: BorderRadius.lg,
      padding: Spacing[padding],
      ...shadowStyle,
    },
    pressed: {
      opacity: 0.95,
      transform: [{ scale: 0.98 }],
    },
  });
};

// =============================================================================
// AVATAR COMPONENT
// =============================================================================

interface AvatarProps {
  source?: { uri: string };
  name?: string;
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  style?: ViewStyle;
}

export const Avatar: React.FC<AvatarProps> = ({
  source,
  name,
  size = 'medium',
  style,
}) => {
  const avatarStyles = getAvatarStyles(size);
  const initials = getInitials(name);

  return (
    <View style={[avatarStyles.container, style]}>
      {source ? (
        <Image 
          source={source} 
          style={avatarStyles.image}
          accessibilityLabel={`Avatar for ${name}`}
        />
      ) : (
        <Text style={avatarStyles.initials}>{initials}</Text>
      )}
    </View>
  );
};

const getAvatarStyles = (size: AvatarProps['size']) => {
  const dimensions = {
    small: 32,
    medium: 48,
    large: 64,
    xlarge: 96,
  }[size!];

  const fontSize = {
    small: Typography.caption.medium.fontSize,
    medium: Typography.body.medium.fontSize,
    large: Typography.heading.h4.fontSize,
    xlarge: Typography.heading.h3.fontSize,
  }[size!];

  return StyleSheet.create({
    container: {
      width: dimensions,
      height: dimensions,
      borderRadius: dimensions / 2,
      backgroundColor: Colors.light.primary,
      alignItems: 'center',
      justifyContent: 'center',
    },
    image: {
      width: dimensions,
      height: dimensions,
      borderRadius: dimensions / 2,
    } as ImageStyle,
    initials: {
      fontSize,
      fontWeight: Typography.label.medium.fontWeight,
      color: Colors.light.textInverse,
    },
  });
};

const getInitials = (name?: string): string => {
  if (!name) return '?';
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

// =============================================================================
// BADGE COMPONENT
// =============================================================================

interface BadgeProps {
  text: string;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  size?: 'small' | 'medium';
  style?: ViewStyle;
}

export const Badge: React.FC<BadgeProps> = ({
  text,
  variant = 'primary',
  size = 'medium',
  style,
}) => {
  const badgeStyles = getBadgeStyles(variant, size);

  return (
    <View style={[badgeStyles.container, style]}>
      <Text style={badgeStyles.text}>{text}</Text>
    </View>
  );
};

const getBadgeStyles = (variant: BadgeProps['variant'], size: BadgeProps['size']) => {
  const backgroundColor = {
    primary: Colors.light.primary,
    secondary: Colors.light.secondary,
    success: Colors.light.success,
    warning: Colors.light.warning,
    error: Colors.light.error,
  }[variant!];

  const padding = {
    small: { paddingHorizontal: Spacing.sm, paddingVertical: Spacing.xs },
    medium: { paddingHorizontal: Spacing.md, paddingVertical: Spacing.sm },
  }[size!];

  const fontSize = {
    small: Typography.caption.medium.fontSize,
    medium: Typography.label.small.fontSize,
  }[size!];

  return StyleSheet.create({
    container: {
      backgroundColor,
      borderRadius: BorderRadius.full,
      ...padding,
      alignSelf: 'flex-start',
    },
    text: {
      fontSize,
      fontWeight: Typography.label.medium.fontWeight,
      color: Colors.light.textInverse,
      textAlign: 'center',
    },
  });
};

// =============================================================================
// LOADING COMPONENT
// =============================================================================

interface LoadingProps {
  size?: 'small' | 'large';
  color?: string;
  text?: string;
  style?: ViewStyle;
}

export const Loading: React.FC<LoadingProps> = ({
  size = 'large',
  color = Colors.light.primary,
  text,
  style,
}) => {
  return (
    <View style={[styles.loadingContainer, style]}>
      <ActivityIndicator size={size} color={color} />
      {text && (
        <Text style={styles.loadingText}>{text}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.lg,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: Typography.body.medium.fontSize,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
});

// Export all components
export {
  Spacing,
  TouchTargets,
  BorderRadius,
};
