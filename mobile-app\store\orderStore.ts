// =====================================================
// STORE COMMANDES - ZUSTAND
// =====================================================

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { OrderStore, Order, Cart, CartItem, OrderStatus, DeliveryTracking } from '../types/orders';

export const useOrderStore = create<OrderStore>()(
  persist(
    (set, get) => ({
      // État initial
      orders: [],
      currentOrder: null,
      cart: null,
      tracking: null,
      isLoading: false,
      error: null,

      // Actions du panier
      addToCart: (merchantId: string, item: CartItem) => {
        const currentCart = get().cart;
        
        // Si le panier est vide ou d'un autre marchand, créer un nouveau panier
        if (!currentCart || currentCart.merchantId !== merchantId) {
          const newCart: Cart = {
            merchantId,
            items: [item],
            subtotal: item.price * item.quantity,
            deliveryFee: 500, // 500 CFA par défaut
            serviceFee: 100,
            tax: 0,
            discount: 0,
            total: 0,
            estimatedDeliveryTime: 30,
          };
          newCart.total = newCart.subtotal + newCart.deliveryFee + newCart.serviceFee;
          set({ cart: newCart });
          return;
        }

        // Vérifier si l'item existe déjà
        const existingItemIndex = currentCart.items.findIndex(
          cartItem => cartItem.productId === item.productId
        );

        let updatedItems;
        if (existingItemIndex >= 0) {
          // Mettre à jour la quantité
          updatedItems = currentCart.items.map((cartItem, index) =>
            index === existingItemIndex
              ? { ...cartItem, quantity: cartItem.quantity + item.quantity }
              : cartItem
          );
        } else {
          // Ajouter le nouvel item
          updatedItems = [...currentCart.items, item];
        }

        const subtotal = updatedItems.reduce(
          (sum, cartItem) => sum + cartItem.price * cartItem.quantity,
          0
        );

        const updatedCart: Cart = {
          ...currentCart,
          items: updatedItems,
          subtotal,
          total: subtotal + currentCart.deliveryFee + currentCart.serviceFee,
        };

        set({ cart: updatedCart });
      },

      removeFromCart: (productId: string) => {
        const currentCart = get().cart;
        if (!currentCart) return;

        const updatedItems = currentCart.items.filter(
          item => item.productId !== productId
        );

        if (updatedItems.length === 0) {
          set({ cart: null });
          return;
        }

        const subtotal = updatedItems.reduce(
          (sum, item) => sum + item.price * item.quantity,
          0
        );

        const updatedCart: Cart = {
          ...currentCart,
          items: updatedItems,
          subtotal,
          total: subtotal + currentCart.deliveryFee + currentCart.serviceFee,
        };

        set({ cart: updatedCart });
      },

      updateCartItem: (productId: string, updates: Partial<CartItem>) => {
        const currentCart = get().cart;
        if (!currentCart) return;

        const updatedItems = currentCart.items.map(item =>
          item.productId === productId ? { ...item, ...updates } : item
        );

        const subtotal = updatedItems.reduce(
          (sum, item) => sum + item.price * item.quantity,
          0
        );

        const updatedCart: Cart = {
          ...currentCart,
          items: updatedItems,
          subtotal,
          total: subtotal + currentCart.deliveryFee + currentCart.serviceFee,
        };

        set({ cart: updatedCart });
      },

      clearCart: () => {
        set({ cart: null });
      },

      // Actions des commandes
      placeOrder: async (deliveryAddress, paymentMethod) => {
        try {
          set({ isLoading: true, error: null });
          
          const currentCart = get().cart;
          if (!currentCart) {
            throw new Error('Panier vide');
          }

          // Créer la commande
          const newOrder: Order = {
            id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            clientId: 'current-user-id', // À remplacer par l'ID utilisateur réel
            merchantId: currentCart.merchantId,
            status: 'pending',
            type: 'standard',
            items: currentCart.items.map(item => ({
              id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              productId: item.productId,
              name: item.name,
              price: item.price,
              quantity: item.quantity,
              options: item.options,
              notes: item.notes,
            })),
            subtotal: currentCart.subtotal,
            deliveryFee: currentCart.deliveryFee,
            serviceFee: currentCart.serviceFee,
            tax: currentCart.tax,
            discount: currentCart.discount,
            total: currentCart.total,
            pickupAddress: {
              address: 'Adresse du marchand',
              coordinates: { latitude: 14.6928, longitude: -17.4467 },
            },
            deliveryAddress,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            estimatedDeliveryTime: currentCart.estimatedDeliveryTime,
            paymentMethod: {
              ...paymentMethod,
              status: 'pending' as const,
            },
            trackingCode: `TRK${Date.now()}`,
          };

          // Ajouter à la liste des commandes
          const currentOrders = get().orders;
          set({
            orders: [newOrder, ...currentOrders],
            currentOrder: newOrder,
            cart: null, // Vider le panier
            isLoading: false,
          });

          console.log('✅ Commande créée:', newOrder.id);
          return newOrder;
        } catch (error) {
          console.error('❌ Erreur création commande:', error);
          set({
            error: 'Erreur lors de la création de la commande',
            isLoading: false,
          });
          throw error;
        }
      },

      confirmOrder: async (orderId: string) => {
        try {
          set({ isLoading: true, error: null });
          
          const updatedOrders = get().orders.map(order =>
            order.id === orderId
              ? {
                  ...order,
                  status: 'confirmed' as OrderStatus,
                  confirmedAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                }
              : order
          );

          set({ orders: updatedOrders, isLoading: false });
          console.log('✅ Commande confirmée:', orderId);
        } catch (error) {
          console.error('❌ Erreur confirmation commande:', error);
          set({
            error: 'Erreur lors de la confirmation',
            isLoading: false,
          });
        }
      },

      updateOrderStatus: async (orderId: string, status: OrderStatus) => {
        try {
          const updatedOrders = get().orders.map(order =>
            order.id === orderId
              ? {
                  ...order,
                  status,
                  updatedAt: new Date().toISOString(),
                  ...(status === 'ready' && { readyAt: new Date().toISOString() }),
                  ...(status === 'picked_up' && { pickedUpAt: new Date().toISOString() }),
                  ...(status === 'delivered' && { deliveredAt: new Date().toISOString() }),
                }
              : order
          );

          set({ orders: updatedOrders });
          console.log('✅ Statut mis à jour:', orderId, status);
        } catch (error) {
          console.error('❌ Erreur mise à jour statut:', error);
          set({ error: 'Erreur lors de la mise à jour' });
        }
      },

      acceptOrder: async (orderId: string) => {
        await get().updateOrderStatus(orderId, 'assigned');
      },

      pickupOrder: async (orderId: string) => {
        await get().updateOrderStatus(orderId, 'picked_up');
      },

      deliverOrder: async (orderId: string, proof: any) => {
        try {
          const updatedOrders = get().orders.map(order =>
            order.id === orderId
              ? {
                  ...order,
                  status: 'delivered' as OrderStatus,
                  deliveredAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  deliveryProof: {
                    ...proof,
                    timestamp: new Date().toISOString(),
                  },
                }
              : order
          );

          set({ orders: updatedOrders });
          console.log('✅ Commande livrée:', orderId);
        } catch (error) {
          console.error('❌ Erreur livraison:', error);
          set({ error: 'Erreur lors de la livraison' });
        }
      },

      getOrders: async (filters) => {
        try {
          set({ isLoading: true, error: null });
          
          let filteredOrders = get().orders;
          
          if (filters?.status) {
            filteredOrders = filteredOrders.filter(order =>
              filters.status!.includes(order.status)
            );
          }

          set({ isLoading: false });
          return filteredOrders;
        } catch (error) {
          console.error('❌ Erreur récupération commandes:', error);
          set({
            error: 'Erreur lors de la récupération',
            isLoading: false,
          });
          return [];
        }
      },

      getOrder: async (orderId: string) => {
        const order = get().orders.find(o => o.id === orderId);
        if (order) {
          set({ currentOrder: order });
        }
        return order || null;
      },

      trackOrder: async (orderId: string) => {
        try {
          const order = get().orders.find(o => o.id === orderId);
          if (!order) return null;

          const tracking: DeliveryTracking = {
            orderId,
            status: order.status,
            currentLocation: {
              latitude: 14.6928,
              longitude: -17.4467,
            },
            estimatedArrival: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
            updates: [
              {
                id: '1',
                status: order.status,
                message: `Commande ${order.status}`,
                timestamp: order.updatedAt,
              },
            ],
          };

          set({ tracking });
          return tracking;
        } catch (error) {
          console.error('❌ Erreur tracking:', error);
          return null;
        }
      },

      rateOrder: async (orderId: string, rating: any) => {
        try {
          const updatedOrders = get().orders.map(order =>
            order.id === orderId
              ? { ...order, clientRating: rating }
              : order
          );

          set({ orders: updatedOrders });
          console.log('✅ Commande évaluée:', orderId);
        } catch (error) {
          console.error('❌ Erreur évaluation:', error);
          set({ error: 'Erreur lors de l\'évaluation' });
        }
      },

      cancelOrder: async (orderId: string, reason: string) => {
        try {
          const updatedOrders = get().orders.map(order =>
            order.id === orderId
              ? {
                  ...order,
                  status: 'cancelled' as OrderStatus,
                  updatedAt: new Date().toISOString(),
                  notes: reason,
                }
              : order
          );

          set({ orders: updatedOrders });
          console.log('✅ Commande annulée:', orderId, reason);
        } catch (error) {
          console.error('❌ Erreur annulation:', error);
          set({ error: 'Erreur lors de l\'annulation' });
        }
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'order-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        orders: state.orders,
        cart: state.cart,
      }),
    }
  )
);

// Sélecteurs optimisés
export const useCart = () => useOrderStore((state) => state.cart);
export const useOrders = () => useOrderStore((state) => state.orders);
export const useCurrentOrder = () => useOrderStore((state) => state.currentOrder);
export const useOrderLoading = () => useOrderStore((state) => state.isLoading);

export default useOrderStore;
