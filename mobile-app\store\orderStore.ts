// =====================================================
// STORE COMMANDES - ZUSTAND
// =====================================================

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { OrderStore, Order, Cart, CartItem, OrderStatus, DeliveryTracking } from '../types/orders';
import { supabaseService } from '../services/supabaseService';

export const useOrderStore = create<OrderStore>()(
  persist(
    (set, get) => ({
      // État initial
      orders: [],
      currentOrder: null,
      cart: null,
      tracking: null,
      isLoading: false,
      error: null,

      // Actions du panier
      addToCart: (merchantId: string, item: CartItem) => {
        const currentCart = get().cart;

        // Si le panier est vide ou d'un autre marchand, créer un nouveau panier
        if (!currentCart || currentCart.merchantId !== merchantId) {
          const newCart: Cart = {
            merchantId,
            items: [item],
            subtotal: item.price * item.quantity,
            deliveryFee: 500, // 500 CFA par défaut
            serviceFee: 100,
            tax: 0,
            discount: 0,
            total: 0,
            estimatedDeliveryTime: 30,
          };
          newCart.total = newCart.subtotal + newCart.deliveryFee + newCart.serviceFee;
          set({ cart: newCart });
          return;
        }

        // Vérifier si l'item existe déjà
        const existingItemIndex = currentCart.items.findIndex(
          cartItem => cartItem.productId === item.productId
        );

        let updatedItems;
        if (existingItemIndex >= 0) {
          // Mettre à jour la quantité
          updatedItems = currentCart.items.map((cartItem, index) =>
            index === existingItemIndex
              ? { ...cartItem, quantity: cartItem.quantity + item.quantity }
              : cartItem
          );
        } else {
          // Ajouter le nouvel item
          updatedItems = [...currentCart.items, item];
        }

        const subtotal = updatedItems.reduce(
          (sum, cartItem) => sum + cartItem.price * cartItem.quantity,
          0
        );

        const updatedCart: Cart = {
          ...currentCart,
          items: updatedItems,
          subtotal,
          total: subtotal + currentCart.deliveryFee + currentCart.serviceFee,
        };

        set({ cart: updatedCart });
      },

      removeFromCart: (productId: string) => {
        const currentCart = get().cart;
        if (!currentCart) return;

        const updatedItems = currentCart.items.filter(
          item => item.productId !== productId
        );

        if (updatedItems.length === 0) {
          set({ cart: null });
          return;
        }

        const subtotal = updatedItems.reduce(
          (sum, item) => sum + item.price * item.quantity,
          0
        );

        const updatedCart: Cart = {
          ...currentCart,
          items: updatedItems,
          subtotal,
          total: subtotal + currentCart.deliveryFee + currentCart.serviceFee,
        };

        set({ cart: updatedCart });
      },

      updateCartItem: (productId: string, updates: Partial<CartItem>) => {
        const currentCart = get().cart;
        if (!currentCart) return;

        const updatedItems = currentCart.items.map(item =>
          item.productId === productId ? { ...item, ...updates } : item
        );

        const subtotal = updatedItems.reduce(
          (sum, item) => sum + item.price * item.quantity,
          0
        );

        const updatedCart: Cart = {
          ...currentCart,
          items: updatedItems,
          subtotal,
          total: subtotal + currentCart.deliveryFee + currentCart.serviceFee,
        };

        set({ cart: updatedCart });
      },

      clearCart: () => {
        set({ cart: null });
      },

      // Actions des commandes
      placeOrder: async (deliveryAddress, paymentMethod) => {
        try {
          set({ isLoading: true, error: null });

          const currentCart = get().cart;
          if (!currentCart) {
            throw new Error('Panier vide');
          }

          // Créer la commande via Supabase
          const orderData = {
            merchant_id: currentCart.merchantId,
            type: 'standard',
            subtotal: currentCart.subtotal,
            delivery_fee: currentCart.deliveryFee,
            service_fee: currentCart.serviceFee,
            tax_amount: currentCart.tax,
            discount_amount: currentCart.discount,
            total_amount: currentCart.total,
            pickup_address: {
              address: 'Adresse du marchand',
              coordinates: { latitude: 14.6928, longitude: -17.4467 },
            },
            delivery_address: deliveryAddress,
            estimated_delivery_time: currentCart.estimatedDeliveryTime,
            payment_method: paymentMethod.type,
            payment_status: 'pending',
            notes: '',
          };

          const newOrder = await supabaseService.createOrder(orderData);

          if (!newOrder) {
            throw new Error('Erreur lors de la création de la commande');
          }

          // Convertir l'ordre Supabase au format local
          const localOrder: Order = {
            id: newOrder.id,
            clientId: newOrder.client_id,
            merchantId: newOrder.merchant_id,
            status: newOrder.status as OrderStatus,
            type: newOrder.type,
            items: currentCart.items.map(item => ({
              id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              productId: item.productId,
              name: item.name,
              price: item.price,
              quantity: item.quantity,
              options: item.options,
              notes: item.notes,
            })),
            subtotal: newOrder.subtotal,
            deliveryFee: newOrder.delivery_fee,
            serviceFee: newOrder.service_fee,
            tax: newOrder.tax_amount,
            discount: newOrder.discount_amount,
            total: newOrder.total_amount,
            pickupAddress: newOrder.pickup_address,
            deliveryAddress: newOrder.delivery_address,
            createdAt: newOrder.created_at,
            updatedAt: newOrder.updated_at,
            estimatedDeliveryTime: newOrder.estimated_delivery_time,
            paymentMethod: {
              type: newOrder.payment_method,
              status: newOrder.payment_status as any,
            },
            trackingCode: newOrder.tracking_code,
          };

          // Ajouter à la liste des commandes
          const currentOrders = get().orders;
          set({
            orders: [localOrder, ...currentOrders],
            currentOrder: localOrder,
            cart: null, // Vider le panier
            isLoading: false,
          });

          console.log('✅ Commande créée:', localOrder.id);
          return localOrder;
        } catch (error) {
          console.error('❌ Erreur création commande:', error);
          set({
            error: 'Erreur lors de la création de la commande',
            isLoading: false,
          });
          throw error;
        }
      },

      confirmOrder: async (orderId: string) => {
        try {
          set({ isLoading: true, error: null });

          const updatedOrders = get().orders.map(order =>
            order.id === orderId
              ? {
                  ...order,
                  status: 'confirmed' as OrderStatus,
                  confirmedAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                }
              : order
          );

          set({ orders: updatedOrders, isLoading: false });
          console.log('✅ Commande confirmée:', orderId);
        } catch (error) {
          console.error('❌ Erreur confirmation commande:', error);
          set({
            error: 'Erreur lors de la confirmation',
            isLoading: false,
          });
        }
      },

      updateOrderStatus: async (orderId: string, status: OrderStatus) => {
        try {
          // Mettre à jour dans Supabase
          const success = await supabaseService.updateOrderStatus(orderId, status);

          if (success) {
            // Mettre à jour localement
            const updatedOrders = get().orders.map(order =>
              order.id === orderId
                ? {
                    ...order,
                    status,
                    updatedAt: new Date().toISOString(),
                    ...(status === 'ready' && { readyAt: new Date().toISOString() }),
                    ...(status === 'picked_up' && { pickedUpAt: new Date().toISOString() }),
                    ...(status === 'delivered' && { deliveredAt: new Date().toISOString() }),
                  }
                : order
            );

            set({ orders: updatedOrders });
            console.log('✅ Statut mis à jour:', orderId, status);
          } else {
            throw new Error('Échec de la mise à jour du statut');
          }
        } catch (error) {
          console.error('❌ Erreur mise à jour statut:', error);
          set({ error: 'Erreur lors de la mise à jour' });
        }
      },

      acceptOrder: async (orderId: string) => {
        await get().updateOrderStatus(orderId, 'assigned');
      },

      pickupOrder: async (orderId: string) => {
        await get().updateOrderStatus(orderId, 'picked_up');
      },

      deliverOrder: async (orderId: string, proof: any) => {
        try {
          const updatedOrders = get().orders.map(order =>
            order.id === orderId
              ? {
                  ...order,
                  status: 'delivered' as OrderStatus,
                  deliveredAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  deliveryProof: {
                    ...proof,
                    timestamp: new Date().toISOString(),
                  },
                }
              : order
          );

          set({ orders: updatedOrders });
          console.log('✅ Commande livrée:', orderId);
        } catch (error) {
          console.error('❌ Erreur livraison:', error);
          set({ error: 'Erreur lors de la livraison' });
        }
      },

      getOrders: async (filters) => {
        try {
          set({ isLoading: true, error: null });

          // Récupérer les commandes depuis Supabase
          const supabaseOrders = await supabaseService.getOrdersByUser();

          // Convertir au format local
          const localOrders: Order[] = supabaseOrders.map(order => ({
            id: order.id,
            clientId: order.client_id,
            merchantId: order.merchant_id,
            status: order.status as OrderStatus,
            type: order.type,
            items: [], // TODO: Récupérer les items depuis order_items
            subtotal: order.subtotal,
            deliveryFee: order.delivery_fee,
            serviceFee: order.service_fee,
            tax: order.tax_amount,
            discount: order.discount_amount,
            total: order.total_amount,
            pickupAddress: order.pickup_address,
            deliveryAddress: order.delivery_address,
            createdAt: order.created_at,
            updatedAt: order.updated_at,
            estimatedDeliveryTime: order.estimated_delivery_time,
            paymentMethod: {
              type: order.payment_method,
              status: order.payment_status as any,
            },
            trackingCode: order.tracking_code,
          }));

          // Appliquer les filtres
          let filteredOrders = localOrders;
          if (filters?.status) {
            filteredOrders = filteredOrders.filter(order =>
              filters.status!.includes(order.status)
            );
          }

          // Mettre à jour le store
          set({ orders: localOrders, isLoading: false });
          return filteredOrders;
        } catch (error) {
          console.error('❌ Erreur récupération commandes:', error);
          set({
            error: 'Erreur lors de la récupération',
            isLoading: false,
          });
          return [];
        }
      },

      getOrder: async (orderId: string) => {
        try {
          // Essayer de trouver dans le cache local d'abord
          let order = get().orders.find(o => o.id === orderId);

          if (!order) {
            // Si pas trouvé localement, récupérer depuis Supabase
            const supabaseOrder = await supabaseService.getOrderById(orderId);
            if (supabaseOrder) {
              order = {
                id: supabaseOrder.id,
                clientId: supabaseOrder.client_id,
                merchantId: supabaseOrder.merchant_id,
                status: supabaseOrder.status as OrderStatus,
                type: supabaseOrder.type,
                items: [], // TODO: Récupérer les items depuis order_items
                subtotal: supabaseOrder.subtotal,
                deliveryFee: supabaseOrder.delivery_fee,
                serviceFee: supabaseOrder.service_fee,
                tax: supabaseOrder.tax_amount,
                discount: supabaseOrder.discount_amount,
                total: supabaseOrder.total_amount,
                pickupAddress: supabaseOrder.pickup_address,
                deliveryAddress: supabaseOrder.delivery_address,
                createdAt: supabaseOrder.created_at,
                updatedAt: supabaseOrder.updated_at,
                estimatedDeliveryTime: supabaseOrder.estimated_delivery_time,
                paymentMethod: {
                  type: supabaseOrder.payment_method,
                  status: supabaseOrder.payment_status as any,
                },
                trackingCode: supabaseOrder.tracking_code,
              };
            }
          }

          if (order) {
            set({ currentOrder: order });
          }
          return order || null;
        } catch (error) {
          console.error('❌ Erreur récupération commande:', error);
          return null;
        }
      },

      trackOrder: async (orderId: string) => {
        try {
          const order = get().orders.find(o => o.id === orderId);
          if (!order) return null;

          const tracking: DeliveryTracking = {
            orderId,
            status: order.status,
            currentLocation: {
              latitude: 14.6928,
              longitude: -17.4467,
            },
            estimatedArrival: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
            updates: [
              {
                id: '1',
                status: order.status,
                message: `Commande ${order.status}`,
                timestamp: order.updatedAt,
              },
            ],
          };

          set({ tracking });
          return tracking;
        } catch (error) {
          console.error('❌ Erreur tracking:', error);
          return null;
        }
      },

      rateOrder: async (orderId: string, rating: any) => {
        try {
          const updatedOrders = get().orders.map(order =>
            order.id === orderId
              ? { ...order, clientRating: rating }
              : order
          );

          set({ orders: updatedOrders });
          console.log('✅ Commande évaluée:', orderId);
        } catch (error) {
          console.error('❌ Erreur évaluation:', error);
          set({ error: 'Erreur lors de l\'évaluation' });
        }
      },

      cancelOrder: async (orderId: string, reason: string) => {
        try {
          // Annuler dans Supabase
          const success = await supabaseService.cancelOrder(orderId, reason);

          if (success) {
            // Mettre à jour localement
            const updatedOrders = get().orders.map(order =>
              order.id === orderId
                ? {
                    ...order,
                    status: 'cancelled' as OrderStatus,
                    updatedAt: new Date().toISOString(),
                    notes: reason,
                  }
                : order
            );

            set({ orders: updatedOrders });
            console.log('✅ Commande annulée:', orderId, reason);
          } else {
            throw new Error('Échec de l\'annulation de la commande');
          }
        } catch (error) {
          console.error('❌ Erreur annulation:', error);
          set({ error: 'Erreur lors de l\'annulation' });
        }
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'order-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        orders: state.orders,
        cart: state.cart,
      }),
    }
  )
);

// Sélecteurs optimisés
export const useCart = () => useOrderStore((state) => state.cart);
export const useOrders = () => useOrderStore((state) => state.orders);
export const useCurrentOrder = () => useOrderStore((state) => state.currentOrder);
export const useOrderLoading = () => useOrderStore((state) => state.isLoading);

export default useOrderStore;
