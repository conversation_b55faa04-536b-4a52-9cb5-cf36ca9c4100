{"version": 3, "file": "getExpoPushTokenAsync.js", "sourceRoot": "", "sources": ["../src/getExpoPushTokenAsync.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,WAAW,MAAM,kBAAkB,CAAC;AAChD,OAAO,SAAS,MAAM,gBAAgB,CAAC;AACvC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAE9E,OAAO,EAAE,qCAAqC,EAAE,MAAM,sCAAsC,CAAC;AAC7F,OAAO,wBAAwB,MAAM,4BAA4B,CAAC;AAElE,OAAO,uBAAuB,MAAM,2BAA2B,CAAC;AAEhE,MAAM,iBAAiB,GAAG,6BAA6B,CAAC;AAExD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,qBAAqB,CACjD,UAAgC,EAAE;IAElC,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,CAAC,MAAM,uBAAuB,EAAE,CAAC,CAAC;IAErF,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,MAAM,gBAAgB,EAAE,CAAC,CAAC;IAChE,sFAAsF;IACtF,MAAM,SAAS,GACb,OAAO,CAAC,SAAS;QACjB,SAAS,CAAC,SAAS,EAAE,SAAS;QAC9B,SAAS,CAAC,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC;IAE9C,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,IAAI,UAAU,CAClB,oCAAoC,EACpC,6IAA6I,CAC9I,CAAC;IACJ,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,WAAW,CAAC,aAAa,CAAC;IACzE,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,MAAM,IAAI,UAAU,CAClB,qCAAqC,EACrC,mIAAmI,CACpI,CAAC;IACJ,CAAC;IACD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,cAAc,CAAC,eAAe,CAAC,CAAC;IAC7D,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,CAAC,MAAM,uCAAuC,EAAE,CAAC,CAAC;IAE7F,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,iBAAiB,CAAC;IACrD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO,uBAAuB,CAAC;IAE7D,MAAM,IAAI,GAAG;QACX,IAAI;QACJ,QAAQ,EAAE,QAAQ,CAAC,WAAW,EAAE;QAChC,WAAW;QACX,KAAK,EAAE,aAAa;QACpB,WAAW,EAAE,cAAc,CAAC,eAAe,CAAC;QAC5C,SAAS;KACV,CAAC;IAEF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;QAChC,MAAM,EAAE,MAAM;QACd,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;SACnC;QACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC3B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACjB,MAAM,IAAI,UAAU,CAClB,iCAAiC,EACjC,gDAAgD,KAAK,GAAG,CACzD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACjB,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,MAAM,CAAC;QAC1D,IAAI,IAAI,GAAuB,SAAS,CAAC;QACzC,IAAI,CAAC;YACH,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC/B,CAAC;QAAC,MAAM,CAAC;YACP,aAAa;QACf,CAAC;QACD,MAAM,IAAI,UAAU,CAClB,gCAAgC,EAChC,mFAAmF,UAAU,YAAY,IAAI,KAAK,CACnH,CAAC;IACJ,CAAC;IAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEtE,IAAI,CAAC;QACH,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACnC,OAAO,CAAC,KAAK,CACX,iLAAiL,CAClL,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,qCAAqC,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CACV,sHAAsH,EACtH,CAAC,CACF,CAAC;IACJ,CAAC;IAED,OAAO;QACL,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,aAAa;KACpB,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,QAAkB;IAC7C,IAAI,CAAC;QACH,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;IAAC,MAAM,CAAC;QACP,IAAI,CAAC;YACH,MAAM,IAAI,UAAU,CAClB,gCAAgC,EAChC,iFAAiF,IAAI,CAAC,SAAS,CAC7F,MAAM,QAAQ,CAAC,IAAI,EAAE,CACtB,GAAG,CACL,CAAC;QACJ,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,IAAI,UAAU,CAClB,gCAAgC,EAChC,qFAAqF,IAAI,CAAC,SAAS,CACjG,QAAQ,CACT,GAAG,CACL,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAS;IACjC,IACE,CAAC,IAAI;QACL,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC;QAC3B,CAAC,IAAI,CAAC,IAAI;QACV,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC;QAChC,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa;QACxB,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,QAAQ,CAAC,EAC9C,CAAC;QACD,MAAM,IAAI,UAAU,CAClB,gCAAgC,EAChC,6FAA6F,IAAI,CAAC,SAAS,CACzG,IAAI,EACJ,IAAI,EACJ,CAAC,CACF,GAAG,CACL,CAAC;IACJ,CAAC;IAED,OAAO,IAAI,CAAC,IAAI,CAAC,aAAuB,CAAC;AAC3C,CAAC;AAED,6CAA6C;AAC7C,KAAK,UAAU,gBAAgB;IAC7B,IAAI,CAAC;QACH,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,EAAE,CAAC;YACrD,MAAM,IAAI,mBAAmB,CAAC,8BAA8B,EAAE,wBAAwB,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,MAAM,wBAAwB,CAAC,sBAAsB,EAAE,CAAC;IACjE,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,IAAI,UAAU,CAClB,qBAAqB,EACrB,8DAA8D,CAAC,GAAG,CACnE,CAAC;IACJ,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,eAAgC;IACtD,IAAI,OAAO,eAAe,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7C,OAAO,eAAe,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAC9C,CAAC;AAED,6CAA6C;AAC7C,KAAK,UAAU,uCAAuC;IACpD,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC;YACH,MAAM,8BAA8B,GAClC,MAAM,WAAW,CAAC,6CAA6C,EAAE,CAAC;YACpE,IAAI,8BAA8B,KAAK,aAAa,EAAE,CAAC;gBACrD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,2DAA2D;QAC7D,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,6CAA6C;AAC7C,SAAS,cAAc,CAAC,eAAgC;IACtD,QAAQ,eAAe,CAAC,IAAI,EAAE,CAAC;QAC7B,KAAK,KAAK;YACR,OAAO,MAAM,CAAC;QAChB,KAAK,SAAS;YACZ,OAAO,KAAK,CAAC;QACf,gFAAgF;QAChF;YACE,OAAO,eAAe,CAAC,IAAI,CAAC;IAChC,CAAC;AACH,CAAC", "sourcesContent": ["import * as Application from 'expo-application';\nimport Constants from 'expo-constants';\nimport { Platform, CodedError, UnavailabilityError } from 'expo-modules-core';\n\nimport { setAutoServerRegistrationEnabledAsync } from './DevicePushTokenAutoRegistration.fx';\nimport ServerRegistrationModule from './ServerRegistrationModule';\nimport { DevicePushToken, ExpoPushToken, ExpoPushTokenOptions } from './Tokens.types';\nimport getDevicePushTokenAsync from './getDevicePushTokenAsync';\n\nconst productionBaseUrl = 'https://exp.host/--/api/v2/';\n\n/**\n * Returns an Expo token that can be used to send a push notification to the device using Expo's push notifications service.\n *\n * This method makes requests to the Expo's servers. It can get rejected in cases where the request itself fails\n * (for example, due to the device being offline, experiencing a network timeout, or other HTTPS request failures).\n * To provide offline support to your users, you should `try/catch` this method and implement retry logic to attempt\n * to get the push token later, once the device is back online.\n *\n * > For Expo's backend to be able to send notifications to your app, you will need to provide it with push notification keys.\n * For more information, see [credentials](/push-notifications/push-notifications-setup/#get-credentials-for-development-builds) in the push notifications setup.\n *\n * @param options Object allowing you to pass in push notification configuration.\n * @return Returns a `Promise` that resolves to an object representing acquired push token.\n * @header fetch\n *\n * @example\n * ```ts\n * import * as Notifications from 'expo-notifications';\n *\n * export async function registerForPushNotificationsAsync(userId: string) {\n *   const expoPushToken = await Notifications.getExpoPushTokenAsync({\n *    projectId: 'your-project-id',\n *   });\n *\n *   await fetch('https://example.com/', {\n *     method: 'POST',\n *     headers: {\n *       'Content-Type': 'application/json',\n *     },\n *     body: JSON.stringify({\n *       userId,\n *       expoPushToken,\n *     }),\n *   });\n * }\n * ```\n */\nexport default async function getExpoPushTokenAsync(\n  options: ExpoPushTokenOptions = {}\n): Promise<ExpoPushToken> {\n  const devicePushToken = options.devicePushToken || (await getDevicePushTokenAsync());\n\n  const deviceId = options.deviceId || (await getDeviceIdAsync());\n  // Depending on the runtime environment, the default may be located in various places.\n  const projectId =\n    options.projectId ||\n    Constants.easConfig?.projectId ||\n    Constants.expoConfig?.extra?.eas?.projectId;\n\n  if (!projectId) {\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_NO_EXPERIENCE_ID',\n      `No \"projectId\" found. If \"projectId\" can't be inferred from the manifest (for instance, in bare workflow), you have to pass it in yourself.`\n    );\n  }\n\n  const applicationId = options.applicationId || Application.applicationId;\n  if (!applicationId) {\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_NO_APPLICATION_ID',\n      `No \"applicationId\" found. If it can't be inferred from native configuration by expo-application, you have to pass it in yourself.`\n    );\n  }\n  const type = options.type || getTypeOfToken(devicePushToken);\n  const development = options.development || (await shouldUseDevelopmentNotificationService());\n\n  const baseUrl = options.baseUrl ?? productionBaseUrl;\n  const url = options.url ?? `${baseUrl}push/getExpoPushToken`;\n\n  const body = {\n    type,\n    deviceId: deviceId.toLowerCase(),\n    development,\n    appId: applicationId,\n    deviceToken: getDeviceToken(devicePushToken),\n    projectId,\n  };\n\n  const response = await fetch(url, {\n    method: 'POST',\n    headers: {\n      'content-type': 'application/json',\n    },\n    body: JSON.stringify(body),\n  }).catch((error) => {\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_NETWORK_ERROR',\n      `Error encountered while fetching Expo token: ${error}.`\n    );\n  });\n\n  if (!response.ok) {\n    const statusInfo = response.statusText || response.status;\n    let body: string | undefined = undefined;\n    try {\n      body = await response.text();\n    } catch {\n      // do nothing\n    }\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_SERVER_ERROR',\n      `Error encountered while fetching Expo token, expected an OK response, received: ${statusInfo} (body: \"${body}\").`\n    );\n  }\n\n  const expoPushToken = getExpoPushToken(await parseResponse(response));\n\n  try {\n    if (options.url || options.baseUrl) {\n      console.debug(\n        `[expo-notifications] Since the URL endpoint to register in has been customized in the options, expo-notifications won't try to auto-update the device push token on the server.`\n      );\n    } else {\n      await setAutoServerRegistrationEnabledAsync(true);\n    }\n  } catch (e) {\n    console.warn(\n      '[expo-notifications] Could not enable automatically registering new device tokens with the Expo notification service',\n      e\n    );\n  }\n\n  return {\n    type: 'expo',\n    data: expoPushToken,\n  };\n}\n\nasync function parseResponse(response: Response) {\n  try {\n    return await response.json();\n  } catch {\n    try {\n      throw new CodedError(\n        'ERR_NOTIFICATIONS_SERVER_ERROR',\n        `Expected a JSON response from server when fetching Expo token, received body: ${JSON.stringify(\n          await response.text()\n        )}.`\n      );\n    } catch {\n      throw new CodedError(\n        'ERR_NOTIFICATIONS_SERVER_ERROR',\n        `Expected a JSON response from server when fetching Expo token, received response: ${JSON.stringify(\n          response\n        )}.`\n      );\n    }\n  }\n}\n\nfunction getExpoPushToken(data: any) {\n  if (\n    !data ||\n    !(typeof data === 'object') ||\n    !data.data ||\n    !(typeof data.data === 'object') ||\n    !data.data.expoPushToken ||\n    !(typeof data.data.expoPushToken === 'string')\n  ) {\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_SERVER_ERROR',\n      `Malformed response from server, expected \"{ data: { expoPushToken: string } }\", received: ${JSON.stringify(\n        data,\n        null,\n        2\n      )}.`\n    );\n  }\n\n  return data.data.expoPushToken as string;\n}\n\n// Same as in DevicePushTokenAutoRegistration\nasync function getDeviceIdAsync() {\n  try {\n    if (!ServerRegistrationModule.getInstallationIdAsync) {\n      throw new UnavailabilityError('ExpoServerRegistrationModule', 'getInstallationIdAsync');\n    }\n\n    return await ServerRegistrationModule.getInstallationIdAsync();\n  } catch (e) {\n    throw new CodedError(\n      'ERR_NOTIF_DEVICE_ID',\n      `Could not have fetched installation ID of the application: ${e}.`\n    );\n  }\n}\n\nfunction getDeviceToken(devicePushToken: DevicePushToken) {\n  if (typeof devicePushToken.data === 'string') {\n    return devicePushToken.data;\n  }\n\n  return JSON.stringify(devicePushToken.data);\n}\n\n// Same as in DevicePushTokenAutoRegistration\nasync function shouldUseDevelopmentNotificationService() {\n  if (Platform.OS === 'ios') {\n    try {\n      const notificationServiceEnvironment =\n        await Application.getIosPushNotificationServiceEnvironmentAsync();\n      if (notificationServiceEnvironment === 'development') {\n        return true;\n      }\n    } catch {\n      // We can't do anything here, we'll fallback to false then.\n    }\n  }\n\n  return false;\n}\n\n// Same as in DevicePushTokenAutoRegistration\nfunction getTypeOfToken(devicePushToken: DevicePushToken) {\n  switch (devicePushToken.type) {\n    case 'ios':\n      return 'apns';\n    case 'android':\n      return 'fcm';\n    // This probably will error on server, but let's make this function future-safe.\n    default:\n      return devicePushToken.type;\n  }\n}\n"]}