{"version": 3, "file": "deleteNotificationCategoryAsync.js", "sourceRoot": "", "sources": ["../src/deleteNotificationCategoryAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,4BAA4B,MAAM,gCAAgC,CAAC;AAE1E;;;;;;;;GAQG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,+BAA+B,CAC3D,UAAkB;IAElB,IAAI,CAAC,4BAA4B,CAAC,+BAA+B,EAAE,CAAC;QAClE,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,iCAAiC,CAAC,CAAC;IACpF,CAAC;IAED,OAAO,MAAM,4BAA4B,CAAC,+BAA+B,CAAC,UAAU,CAAC,CAAC;AACxF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationCategoriesModule from './NotificationCategoriesModule';\n\n/**\n * Deletes the category associated with the provided identifier.\n * @param identifier Identifier initially provided to `setNotificationCategoryAsync` when creating the category.\n * @return A Promise which resolves to `true` if the category was successfully deleted, or `false` if it was not.\n * An example of when this method would return `false` is if you try to delete a category that doesn't exist.\n * @platform android\n * @platform ios\n * @header categories\n */\nexport default async function deleteNotificationCategoryAsync(\n  identifier: string\n): Promise<boolean> {\n  if (!NotificationCategoriesModule.deleteNotificationCategoryAsync) {\n    throw new UnavailabilityError('Notifications', 'deleteNotificationCategoryAsync');\n  }\n\n  return await NotificationCategoriesModule.deleteNotificationCategoryAsync(identifier);\n}\n"]}