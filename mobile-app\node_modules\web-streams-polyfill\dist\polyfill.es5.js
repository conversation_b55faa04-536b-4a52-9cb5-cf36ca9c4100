/**
 * @license
 * web-streams-polyfill v4.1.0
 * Copyright 2024 <PERSON>, <PERSON><PERSON><PERSON> and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */
!function(){"use strict";var e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:function(e){return"Symbol(".concat(e,")")};function r(){}function t(e){return"object"==typeof e&&null!==e||"function"==typeof e}"function"==typeof SuppressedError&&SuppressedError;var n=r;function o(e,r){try{Object.defineProperty(e,"name",{value:r,configurable:!0})}catch(e){}}var a=Promise,i=Promise.resolve.bind(a),l=Promise.prototype.then,u=Promise.reject.bind(a),s=i;function c(e){return new a(e)}function f(e){return c((function(r){return r(e)}))}function d(e){return u(e)}function b(e,r,t){return l.call(e,r,t)}function p(e,r,t){b(b(e,r,t),void 0,n)}function h(e,r){p(e,r)}function _(e,r){p(e,void 0,r)}function m(e,r,t){return b(e,r,t)}function v(e){b(e,void 0,n)}var y=function(e){if("function"==typeof queueMicrotask)y=queueMicrotask;else{var r=f(void 0);y=function(e){return b(r,e)}}return y(e)};function g(e,r,t){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,r,t)}function S(e,r,t){try{return f(g(e,r,t))}catch(e){return d(e)}}var w=function(){function e(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.push=function(e){var r=this._back,t=r;16383===r._elements.length&&(t={_elements:[],_next:void 0}),r._elements.push(e),t!==r&&(this._back=t,r._next=t),++this._size},e.prototype.shift=function(){var e=this._front,r=e,t=this._cursor,n=t+1,o=e._elements,a=o[t];return 16384===n&&(r=e._next,n=0),--this._size,this._cursor=n,e!==r&&(this._front=r),o[t]=void 0,a},e.prototype.forEach=function(e){for(var r=this._cursor,t=this._front,n=t._elements;!(r===n.length&&void 0===t._next||r===n.length&&(r=0,0===(n=(t=t._next)._elements).length));)e(n[r]),++r},e.prototype.peek=function(){var e=this._front,r=this._cursor;return e._elements[r]},e}(),R=e("[[AbortSteps]]"),T=e("[[ErrorSteps]]"),P=e("[[CancelSteps]]"),C=e("[[PullSteps]]"),q=e("[[ReleaseSteps]]");function E(e,r){e._ownerReadableStream=r,r._reader=e,"readable"===r._state?B(e):"closed"===r._state?function(e){B(e),z(e)}(e):k(e,r._storedError)}function O(e,r){return xt(e._ownerReadableStream,r)}function W(e){var r=e._ownerReadableStream;"readable"===r._state?A(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,r){k(e,r)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),r._readableStreamController[q](),r._reader=void 0,e._ownerReadableStream=void 0}function j(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function B(e){e._closedPromise=c((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t}))}function k(e,r){B(e),A(e,r)}function A(e,r){void 0!==e._closedPromise_reject&&(v(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function z(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}var L=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},D=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function F(e,r){if(void 0!==e&&("object"!=typeof(t=e)&&"function"!=typeof t))throw new TypeError("".concat(r," is not an object."));var t}function I(e,r){if("function"!=typeof e)throw new TypeError("".concat(r," is not a function."))}function M(e,r){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError("".concat(r," is not an object."))}function Y(e,r,t){if(void 0===e)throw new TypeError("Parameter ".concat(r," is required in '").concat(t,"'."))}function x(e,r,t){if(void 0===e)throw new TypeError("".concat(r," is required in '").concat(t,"'."))}function Q(e){return Number(e)}function N(e){return 0===e?0:e}function H(e,r){var t=Number.MAX_SAFE_INTEGER,n=Number(e);if(n=N(n),!L(n))throw new TypeError("".concat(r," is not a finite number"));if((n=function(e){return N(D(e))}(n))<0||n>t)throw new TypeError("".concat(r," is outside the accepted range of ").concat(0," to ").concat(t,", inclusive"));return L(n)&&0!==n?n:0}function V(e,r){if(!Mt(e))throw new TypeError("".concat(r," is not a ReadableStream."))}function U(e){return new re(e)}function G(e,r){e._reader._readRequests.push(r)}function X(e,r,t){var n=e._reader._readRequests.shift();t?n._closeSteps():n._chunkSteps(r)}function J(e){return e._reader._readRequests.length}function K(e){var r=e._reader;return void 0!==r&&!!te(r)}var Z,$,ee,re=function(){function ReadableStreamDefaultReader(e){if(Y(e,1,"ReadableStreamDefaultReader"),V(e,"First parameter"),Yt(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");E(this,e),this._readRequests=new w}return Object.defineProperty(ReadableStreamDefaultReader.prototype,"closed",{get:function(){return te(this)?this._closedPromise:d(ae("closed"))},enumerable:!1,configurable:!0}),ReadableStreamDefaultReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),te(this)?void 0===this._ownerReadableStream?d(j("cancel")):O(this,e):d(ae("cancel"))},ReadableStreamDefaultReader.prototype.read=function(){if(!te(this))return d(ae("read"));if(void 0===this._ownerReadableStream)return d(j("read from"));var e,r,t=c((function(t,n){e=t,r=n}));return ne(this,{_chunkSteps:function(r){return e({value:r,done:!1})},_closeSteps:function(){return e({value:void 0,done:!0})},_errorSteps:function(e){return r(e)}}),t},ReadableStreamDefaultReader.prototype.releaseLock=function(){if(!te(this))throw ae("releaseLock");void 0!==this._ownerReadableStream&&function(e){W(e);var r=new TypeError("Reader was released");oe(e,r)}(this)},ReadableStreamDefaultReader}();function te(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof re)}function ne(e,r){var t=e._ownerReadableStream;t._disturbed=!0,"closed"===t._state?r._closeSteps():"errored"===t._state?r._errorSteps(t._storedError):t._readableStreamController[C](r)}function oe(e,r){var t=e._readRequests;e._readRequests=new w,t.forEach((function(e){e._errorSteps(r)}))}function ae(e){return new TypeError("ReadableStreamDefaultReader.prototype.".concat(e," can only be used on a ReadableStreamDefaultReader"))}function ie(e){return e.slice()}function le(e,r,t,n,o){new Uint8Array(e).set(new Uint8Array(t,n,o),r)}Object.defineProperties(re.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),o(re.prototype.cancel,"cancel"),o(re.prototype.read,"read"),o(re.prototype.releaseLock,"releaseLock"),"symbol"==typeof e.toStringTag&&Object.defineProperty(re.prototype,e.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});var ue=function(e){return(ue="function"==typeof e.transfer?function(e){return e.transfer()}:"function"==typeof structuredClone?function(e){return structuredClone(e,{transfer:[e]})}:function(e){return e})(e)},se=function(e){return(se="boolean"==typeof e.detached?function(e){return e.detached}:function(e){return 0===e.byteLength})(e)};function ce(e,r,t){if(e.slice)return e.slice(r,t);var n=t-r,o=new ArrayBuffer(n);return le(o,0,e,r,n),o}function fe(e,r){var t=e[r];if(null!=t){if("function"!=typeof t)throw new TypeError("".concat(String(r)," is not a function"));return t}}function de(e){try{var r=e.done,t=e.value;return b(s(t),(function(e){return{done:r,value:e}}))}catch(e){return d(e)}}var be,pe=null!==(ee=null!==(Z=e.asyncIterator)&&void 0!==Z?Z:null===($=e.for)||void 0===$?void 0:$.call(e,"Symbol.asyncIterator"))&&void 0!==ee?ee:"@@asyncIterator";function he(r,n,o){if(void 0===n&&(n="sync"),void 0===o)if("async"===n){if(void 0===(o=fe(r,pe)))return function(e){var r={next:function(){var r;try{r=_e(e)}catch(e){return d(e)}return de(r)},return:function(r){var n;try{var o=fe(e.iterator,"return");if(void 0===o)return f({done:!0,value:r});n=g(o,e.iterator,[r])}catch(e){return d(e)}return t(n)?de(n):d(new TypeError("The iterator.return() method must return an object"))}};return{iterator:r,nextMethod:r.next,done:!1}}(he(r,"sync",fe(r,e.iterator)))}else o=fe(r,e.iterator);if(void 0===o)throw new TypeError("The object is not iterable");var a=g(o,r,[]);if(!t(a))throw new TypeError("The iterator method must return an object");return{iterator:a,nextMethod:a.next,done:!1}}function _e(e){var r=g(e.nextMethod,e.iterator,[]);if(!t(r))throw new TypeError("The iterator.next() method must return an object");return r}var me=function(){function e(e,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=r}return e.prototype.next=function(){var e=this,r=function(){return e._nextSteps()};return this._ongoingPromise=this._ongoingPromise?m(this._ongoingPromise,r,r):r(),this._ongoingPromise},e.prototype.return=function(e){var r=this,t=function(){return r._returnSteps(e)};return this._ongoingPromise=this._ongoingPromise?m(this._ongoingPromise,t,t):t(),this._ongoingPromise},e.prototype._nextSteps=function(){var e=this;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});var r,t,n=this._reader,o=c((function(e,n){r=e,t=n}));return ne(n,{_chunkSteps:function(t){e._ongoingPromise=void 0,y((function(){return r({value:t,done:!1})}))},_closeSteps:function(){e._ongoingPromise=void 0,e._isFinished=!0,W(n),r({value:void 0,done:!0})},_errorSteps:function(r){e._ongoingPromise=void 0,e._isFinished=!0,W(n),t(r)}}),o},e.prototype._returnSteps=function(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;var r=this._reader;if(!this._preventCancel){var t=O(r,e);return W(r),m(t,(function(){return{value:e,done:!0}}))}return W(r),f({value:e,done:!0})},e}(),ve=((be={next:function(){return ye(this)?this._asyncIteratorImpl.next():d(ge("next"))},return:function(e){return ye(this)?this._asyncIteratorImpl.return(e):d(ge("return"))}})[pe]=function(){return this},be);function ye(e){if(!t(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof me}catch(e){return!1}}function ge(e){return new TypeError("ReadableStreamAsyncIterator.".concat(e," can only be used on a ReadableSteamAsyncIterator"))}Object.defineProperty(ve,pe,{enumerable:!1});var Se=Number.isNaN||function(e){return e!=e};function we(e){var r=ce(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(r)}function Re(e){var r=e._queue.shift();return e._queueTotalSize-=r.size,e._queueTotalSize<0&&(e._queueTotalSize=0),r.value}function Te(e,r,t){if("number"!=typeof(n=t)||Se(n)||n<0||t===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var n;e._queue.push({value:r,size:t}),e._queueTotalSize+=t}function Pe(e){e._queue=new w,e._queueTotalSize=0}function Ce(e){return e===DataView}var qe=function(){function ReadableStreamBYOBRequest(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamBYOBRequest.prototype,"view",{get:function(){if(!We(this))throw nr("view");return this._view},enumerable:!1,configurable:!0}),ReadableStreamBYOBRequest.prototype.respond=function(e){if(!We(this))throw nr("respond");if(Y(e,1,"respond"),e=H(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(se(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");er(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest.prototype.respondWithNewView=function(e){if(!We(this))throw nr("respondWithNewView");if(Y(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(se(e.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");rr(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest}();Object.defineProperties(qe.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),o(qe.prototype.respond,"respond"),o(qe.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof e.toStringTag&&Object.defineProperty(qe.prototype,e.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});var Ee=function(){function ReadableByteStreamController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableByteStreamController.prototype,"byobRequest",{get:function(){if(!Oe(this))throw or("byobRequest");return Ze(this)},enumerable:!1,configurable:!0}),Object.defineProperty(ReadableByteStreamController.prototype,"desiredSize",{get:function(){if(!Oe(this))throw or("desiredSize");return $e(this)},enumerable:!1,configurable:!0}),ReadableByteStreamController.prototype.close=function(){if(!Oe(this))throw or("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError("The stream (in ".concat(e," state) is not in the readable state and cannot be closed"));Ge(this)},ReadableByteStreamController.prototype.enqueue=function(e){if(!Oe(this))throw or("enqueue");if(Y(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");var r=this._controlledReadableByteStream._state;if("readable"!==r)throw new TypeError("The stream (in ".concat(r," state) is not in the readable state and cannot be enqueued to"));Xe(this,e)},ReadableByteStreamController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Oe(this))throw or("error");Je(this,e)},ReadableByteStreamController.prototype[P]=function(e){Be(this),Pe(this);var r=this._cancelAlgorithm(e);return Ue(this),r},ReadableByteStreamController.prototype[C]=function(e){var r=this._controlledReadableByteStream;if(this._queueTotalSize>0)Ke(this,e);else{var t=this._autoAllocateChunkSize;if(void 0!==t){var n=void 0;try{n=new ArrayBuffer(t)}catch(r){return void e._errorSteps(r)}var o={buffer:n,bufferByteLength:t,byteOffset:0,byteLength:t,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}G(r,e),je(this)}},ReadableByteStreamController.prototype[q]=function(){if(this._pendingPullIntos.length>0){var e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new w,this._pendingPullIntos.push(e)}},ReadableByteStreamController}();function Oe(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof Ee)}function We(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof qe)}function je(e){var r=function(e){var r=e._controlledReadableByteStream;if("readable"!==r._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(K(r)&&J(r)>0)return!0;if(sr(r)&&ur(r)>0)return!0;var t=$e(e);if(t>0)return!0;return!1}(e);r&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,p(e._pullAlgorithm(),(function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,je(e)),null}),(function(r){return Je(e,r),null}))))}function Be(e){xe(e),e._pendingPullIntos=new w}function ke(e,r){var t=!1;"closed"===e._state&&(t=!0);var n=ze(r);"default"===r.readerType?X(e,n,t):function(e,r,t){var n=e._reader,o=n._readIntoRequests.shift();t?o._closeSteps(r):o._chunkSteps(r)}(e,n,t)}function Ae(e,r){for(var t=0;t<r.length;++t)ke(e,r[t])}function ze(e){var r=e.bytesFilled,t=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,r/t)}function Le(e,r,t,n){e._queue.push({buffer:r,byteOffset:t,byteLength:n}),e._queueTotalSize+=n}function De(e,r,t,n){var o;try{o=ce(r,t,t+n)}catch(r){throw Je(e,r),r}Le(e,o,0,n)}function Fe(e,r){r.bytesFilled>0&&De(e,r.buffer,r.byteOffset,r.bytesFilled),Ve(e)}function Ie(e,r){var t=Math.min(e._queueTotalSize,r.byteLength-r.bytesFilled),n=r.bytesFilled+t,o=t,a=!1,i=n-n%r.elementSize;i>=r.minimumFill&&(o=i-r.bytesFilled,a=!0);for(var l=e._queue;o>0;){var u=l.peek(),s=Math.min(o,u.byteLength),c=r.byteOffset+r.bytesFilled;le(r.buffer,c,u.buffer,u.byteOffset,s),u.byteLength===s?l.shift():(u.byteOffset+=s,u.byteLength-=s),e._queueTotalSize-=s,Me(e,s,r),o-=s}return a}function Me(e,r,t){t.bytesFilled+=r}function Ye(e){0===e._queueTotalSize&&e._closeRequested?(Ue(e),Qt(e._controlledReadableByteStream)):je(e)}function xe(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Qe(e){for(var r=[];e._pendingPullIntos.length>0&&0!==e._queueTotalSize;){var t=e._pendingPullIntos.peek();Ie(e,t)&&(Ve(e),r.push(t))}return r}function Ne(e,r,t,n){var o,a=e._controlledReadableByteStream,i=r.constructor,l=function(e){return Ce(e)?1:e.BYTES_PER_ELEMENT}(i),u=r.byteOffset,s=r.byteLength,c=t*l;try{o=ue(r.buffer)}catch(b){return void n._errorSteps(b)}var f={buffer:o,bufferByteLength:o.byteLength,byteOffset:u,byteLength:s,bytesFilled:0,minimumFill:c,elementSize:l,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(f),void lr(a,n);if("closed"!==a._state){if(e._queueTotalSize>0){if(Ie(e,f)){var d=ze(f);return Ye(e),void n._chunkSteps(d)}if(e._closeRequested){var b=new TypeError("Insufficient bytes to fill elements in the given buffer");return Je(e,b),void n._errorSteps(b)}}e._pendingPullIntos.push(f),lr(a,n),je(e)}else{var p=new i(f.buffer,f.byteOffset,0);n._closeSteps(p)}}function He(e,r){var t=e._pendingPullIntos.peek();xe(e),"closed"===e._controlledReadableByteStream._state?function(e,r){"none"===r.readerType&&Ve(e);var t=e._controlledReadableByteStream;if(sr(t)){for(var n=[],o=0;o<ur(t);++o)n.push(Ve(e));Ae(t,n)}}(e,t):function(e,r,t){if(Me(0,r,t),"none"!==t.readerType){if(!(t.bytesFilled<t.minimumFill)){Ve(e);var n=t.bytesFilled%t.elementSize;if(n>0){var o=t.byteOffset+t.bytesFilled;De(e,t.buffer,o-n,n)}t.bytesFilled-=n;var a=Qe(e);ke(e._controlledReadableByteStream,t),Ae(e._controlledReadableByteStream,a)}}else{Fe(e,t);var i=Qe(e);Ae(e._controlledReadableByteStream,i)}}(e,r,t),je(e)}function Ve(e){return e._pendingPullIntos.shift()}function Ue(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Ge(e){var r=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===r._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){var t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){var n=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Je(e,n),n}}Ue(e),Qt(r)}}function Xe(e,r){var t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){var n=r.buffer,o=r.byteOffset,a=r.byteLength;if(se(n))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");var i=ue(n);if(e._pendingPullIntos.length>0){var l=e._pendingPullIntos.peek();if(se(l.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");xe(e),l.buffer=ue(l.buffer),"none"===l.readerType&&Fe(e,l)}if(K(t))if(function(e){for(var r=e._controlledReadableByteStream._reader;r._readRequests.length>0;){if(0===e._queueTotalSize)return;Ke(e,r._readRequests.shift())}}(e),0===J(t))Le(e,i,o,a);else e._pendingPullIntos.length>0&&Ve(e),X(t,new Uint8Array(i,o,a),!1);else if(sr(t)){Le(e,i,o,a);var u=Qe(e);Ae(e._controlledReadableByteStream,u)}else Le(e,i,o,a);je(e)}}function Je(e,r){var t=e._controlledReadableByteStream;"readable"===t._state&&(Be(e),Pe(e),Ue(e),Nt(t,r))}function Ke(e,r){var t=e._queue.shift();e._queueTotalSize-=t.byteLength,Ye(e);var n=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);r._chunkSteps(n)}function Ze(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){var r=e._pendingPullIntos.peek(),t=new Uint8Array(r.buffer,r.byteOffset+r.bytesFilled,r.byteLength-r.bytesFilled),n=Object.create(qe.prototype);!function(e,r,t){e._associatedReadableByteStreamController=r,e._view=t}(n,e,t),e._byobRequest=n}return e._byobRequest}function $e(e){var r=e._controlledReadableByteStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function er(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===r)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(t.bytesFilled+r>t.byteLength)throw new RangeError("bytesWritten out of range")}t.buffer=ue(t.buffer),He(e,r)}function rr(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===r.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(t.byteOffset+t.bytesFilled!==r.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(t.bufferByteLength!==r.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(t.bytesFilled+r.byteLength>t.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");var n=r.byteLength;t.buffer=ue(r.buffer),He(e,n)}function tr(e,r,t,n,o,a,i){r._controlledReadableByteStream=e,r._pullAgain=!1,r._pulling=!1,r._byobRequest=null,r._queue=r._queueTotalSize=void 0,Pe(r),r._closeRequested=!1,r._started=!1,r._strategyHWM=a,r._pullAlgorithm=n,r._cancelAlgorithm=o,r._autoAllocateChunkSize=i,r._pendingPullIntos=new w,e._readableStreamController=r,p(f(t()),(function(){return r._started=!0,je(r),null}),(function(e){return Je(r,e),null}))}function nr(e){return new TypeError("ReadableStreamBYOBRequest.prototype.".concat(e," can only be used on a ReadableStreamBYOBRequest"))}function or(e){return new TypeError("ReadableByteStreamController.prototype.".concat(e," can only be used on a ReadableByteStreamController"))}function ar(e,r){if("byob"!==(e="".concat(e)))throw new TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamReaderMode"));return e}function ir(e){return new cr(e)}function lr(e,r){e._reader._readIntoRequests.push(r)}function ur(e){return e._reader._readIntoRequests.length}function sr(e){var r=e._reader;return void 0!==r&&!!fr(r)}Object.defineProperties(Ee.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),o(Ee.prototype.close,"close"),o(Ee.prototype.enqueue,"enqueue"),o(Ee.prototype.error,"error"),"symbol"==typeof e.toStringTag&&Object.defineProperty(Ee.prototype,e.toStringTag,{value:"ReadableByteStreamController",configurable:!0});var cr=function(){function ReadableStreamBYOBReader(e){if(Y(e,1,"ReadableStreamBYOBReader"),V(e,"First parameter"),Yt(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Oe(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");E(this,e),this._readIntoRequests=new w}return Object.defineProperty(ReadableStreamBYOBReader.prototype,"closed",{get:function(){return fr(this)?this._closedPromise:d(pr("closed"))},enumerable:!1,configurable:!0}),ReadableStreamBYOBReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),fr(this)?void 0===this._ownerReadableStream?d(j("cancel")):O(this,e):d(pr("cancel"))},ReadableStreamBYOBReader.prototype.read=function(e,r){if(void 0===r&&(r={}),!fr(this))return d(pr("read"));if(!ArrayBuffer.isView(e))return d(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return d(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return d(new TypeError("view's buffer must have non-zero byteLength"));if(se(e.buffer))return d(new TypeError("view's buffer has been detached"));var t;try{t=function(e,r){var t;return F(e,r),{min:H(null!==(t=null==e?void 0:e.min)&&void 0!==t?t:1,"".concat(r," has member 'min' that"))}}(r,"options")}catch(e){return d(e)}var n,o,a=t.min;if(0===a)return d(new TypeError("options.min must be greater than 0"));if(function(e){return Ce(e.constructor)}(e)){if(a>e.byteLength)return d(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(a>e.length)return d(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return d(j("read from"));var i=c((function(e,r){n=e,o=r}));return dr(this,e,a,{_chunkSteps:function(e){return n({value:e,done:!1})},_closeSteps:function(e){return n({value:e,done:!0})},_errorSteps:function(e){return o(e)}}),i},ReadableStreamBYOBReader.prototype.releaseLock=function(){if(!fr(this))throw pr("releaseLock");void 0!==this._ownerReadableStream&&function(e){W(e);var r=new TypeError("Reader was released");br(e,r)}(this)},ReadableStreamBYOBReader}();function fr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof cr)}function dr(e,r,t,n){var o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?n._errorSteps(o._storedError):Ne(o._readableStreamController,r,t,n)}function br(e,r){var t=e._readIntoRequests;e._readIntoRequests=new w,t.forEach((function(e){e._errorSteps(r)}))}function pr(e){return new TypeError("ReadableStreamBYOBReader.prototype.".concat(e," can only be used on a ReadableStreamBYOBReader"))}function hr(e,r){var t=e.highWaterMark;if(void 0===t)return r;if(Se(t)||t<0)throw new RangeError("Invalid highWaterMark");return t}function _r(e){var r=e.size;return r||function(){return 1}}function mr(e,r){F(e,r);var t=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===t?void 0:Q(t),size:void 0===n?void 0:vr(n,"".concat(r," has member 'size' that"))}}function vr(e,r){return I(e,r),function(r){return Q(e(r))}}function yr(e,r,t){return I(e,t),function(t){return S(e,r,[t])}}function gr(e,r,t){return I(e,t),function(){return S(e,r,[])}}function Sr(e,r,t){return I(e,t),function(t){return g(e,r,[t])}}function wr(e,r,t){return I(e,t),function(t,n){return S(e,r,[t,n])}}function Rr(e,r){if(!qr(e))throw new TypeError("".concat(r," is not a WritableStream."))}Object.defineProperties(cr.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),o(cr.prototype.cancel,"cancel"),o(cr.prototype.read,"read"),o(cr.prototype.releaseLock,"releaseLock"),"symbol"==typeof e.toStringTag&&Object.defineProperty(cr.prototype,e.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});var Tr=function(){function WritableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:M(e,"First parameter");var t=mr(r,"Second parameter"),n=function(e,r){F(e,r);var t=null==e?void 0:e.abort,n=null==e?void 0:e.close,o=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===t?void 0:yr(t,e,"".concat(r," has member 'abort' that")),close:void 0===n?void 0:gr(n,e,"".concat(r," has member 'close' that")),start:void 0===o?void 0:Sr(o,e,"".concat(r," has member 'start' that")),write:void 0===i?void 0:wr(i,e,"".concat(r," has member 'write' that")),type:a}}(e,"First parameter");if(Cr(this),void 0!==n.type)throw new RangeError("Invalid type is specified");var o=_r(t);!function(e,r,t,n){var o,a,i,l,u=Object.create(Hr.prototype);o=void 0!==r.start?function(){return r.start(u)}:function(){};a=void 0!==r.write?function(e){return r.write(e,u)}:function(){return f(void 0)};i=void 0!==r.close?function(){return r.close()}:function(){return f(void 0)};l=void 0!==r.abort?function(e){return r.abort(e)}:function(){return f(void 0)};Ur(e,u,o,a,i,l,t,n)}(this,n,hr(t,1),o)}return Object.defineProperty(WritableStream.prototype,"locked",{get:function(){if(!qr(this))throw et("locked");return Er(this)},enumerable:!1,configurable:!0}),WritableStream.prototype.abort=function(e){return void 0===e&&(e=void 0),qr(this)?Er(this)?d(new TypeError("Cannot abort a stream that already has a writer")):Or(this,e):d(et("abort"))},WritableStream.prototype.close=function(){return qr(this)?Er(this)?d(new TypeError("Cannot close a stream that already has a writer")):Ar(this)?d(new TypeError("Cannot close an already-closing stream")):Wr(this):d(et("close"))},WritableStream.prototype.getWriter=function(){if(!qr(this))throw et("getWriter");return Pr(this)},WritableStream}();function Pr(e){return new Dr(e)}function Cr(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new w,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function qr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof Tr)}function Er(e){return void 0!==e._writer}function Or(e,r){var t;if("closed"===e._state||"errored"===e._state)return f(void 0);e._writableStreamController._abortReason=r,null===(t=e._writableStreamController._abortController)||void 0===t||t.abort(r);var n=e._state;if("closed"===n||"errored"===n)return f(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;var o=!1;"erroring"===n&&(o=!0,r=void 0);var a=c((function(t,n){e._pendingAbortRequest={_promise:void 0,_resolve:t,_reject:n,_reason:r,_wasAlreadyErroring:o}}));return e._pendingAbortRequest._promise=a,o||Br(e,r),a}function Wr(e){var r=e._state;if("closed"===r||"errored"===r)return d(new TypeError("The stream (in ".concat(r," state) is not in the writable state and cannot be closed")));var t,n=c((function(r,t){var n={_resolve:r,_reject:t};e._closeRequest=n})),o=e._writer;return void 0!==o&&e._backpressure&&"writable"===r&&dt(o),Te(t=e._writableStreamController,Nr,0),Jr(t),n}function jr(e,r){"writable"!==e._state?kr(e):Br(e,r)}function Br(e,r){var t=e._writableStreamController;e._state="erroring",e._storedError=r;var n=e._writer;void 0!==n&&Yr(n,r),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&t._started&&kr(e)}function kr(e){e._state="errored",e._writableStreamController[T]();var r=e._storedError;if(e._writeRequests.forEach((function(e){e._reject(r)})),e._writeRequests=new w,void 0!==e._pendingAbortRequest){var t=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,t._wasAlreadyErroring)return t._reject(r),void zr(e);p(e._writableStreamController[R](t._reason),(function(){return t._resolve(),zr(e),null}),(function(r){return t._reject(r),zr(e),null}))}else zr(e)}function Ar(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function zr(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var r=e._writer;void 0!==r&&it(r,e._storedError)}function Lr(e,r){var t=e._writer;void 0!==t&&r!==e._backpressure&&(r?function(e){ut(e)}(t):dt(t)),e._backpressure=r}Object.defineProperties(Tr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),o(Tr.prototype.abort,"abort"),o(Tr.prototype.close,"close"),o(Tr.prototype.getWriter,"getWriter"),"symbol"==typeof e.toStringTag&&Object.defineProperty(Tr.prototype,e.toStringTag,{value:"WritableStream",configurable:!0});var Dr=function(){function WritableStreamDefaultWriter(e){if(Y(e,1,"WritableStreamDefaultWriter"),Rr(e,"First parameter"),Er(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;var r,t=e._state;if("writable"===t)!Ar(e)&&e._backpressure?ut(this):ct(this),ot(this);else if("erroring"===t)st(this,e._storedError),ot(this);else if("closed"===t)ct(this),ot(r=this),lt(r);else{var n=e._storedError;st(this,n),at(this,n)}}return Object.defineProperty(WritableStreamDefaultWriter.prototype,"closed",{get:function(){return Fr(this)?this._closedPromise:d(tt("closed"))},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"desiredSize",{get:function(){if(!Fr(this))throw tt("desiredSize");if(void 0===this._ownerWritableStream)throw nt("desiredSize");return function(e){var r=e._ownerWritableStream,t=r._state;if("errored"===t||"erroring"===t)return null;if("closed"===t)return 0;return Xr(r._writableStreamController)}(this)},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"ready",{get:function(){return Fr(this)?this._readyPromise:d(tt("ready"))},enumerable:!1,configurable:!0}),WritableStreamDefaultWriter.prototype.abort=function(e){return void 0===e&&(e=void 0),Fr(this)?void 0===this._ownerWritableStream?d(nt("abort")):function(e,r){return Or(e._ownerWritableStream,r)}(this,e):d(tt("abort"))},WritableStreamDefaultWriter.prototype.close=function(){if(!Fr(this))return d(tt("close"));var e=this._ownerWritableStream;return void 0===e?d(nt("close")):Ar(e)?d(new TypeError("Cannot close an already-closing stream")):Ir(this)},WritableStreamDefaultWriter.prototype.releaseLock=function(){if(!Fr(this))throw tt("releaseLock");void 0!==this._ownerWritableStream&&xr(this)},WritableStreamDefaultWriter.prototype.write=function(e){return void 0===e&&(e=void 0),Fr(this)?void 0===this._ownerWritableStream?d(nt("write to")):Qr(this,e):d(tt("write"))},WritableStreamDefaultWriter}();function Fr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof Dr)}function Ir(e){return Wr(e._ownerWritableStream)}function Mr(e,r){"pending"===e._closedPromiseState?it(e,r):function(e,r){at(e,r)}(e,r)}function Yr(e,r){"pending"===e._readyPromiseState?ft(e,r):function(e,r){st(e,r)}(e,r)}function xr(e){var r=e._ownerWritableStream,t=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");Yr(e,t),Mr(e,t),r._writer=void 0,e._ownerWritableStream=void 0}function Qr(e,r){var t=e._ownerWritableStream,n=t._writableStreamController,o=function(e,r){if(void 0===e._strategySizeAlgorithm)return 1;try{return e._strategySizeAlgorithm(r)}catch(r){return Kr(e,r),1}}(n,r);if(t!==e._ownerWritableStream)return d(nt("write to"));var a=t._state;if("errored"===a)return d(t._storedError);if(Ar(t)||"closed"===a)return d(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return d(t._storedError);var i=function(e){return c((function(r,t){var n={_resolve:r,_reject:t};e._writeRequests.push(n)}))}(t);return function(e,r,t){try{Te(e,r,t)}catch(r){return void Kr(e,r)}var n=e._controlledWritableStream;if(!Ar(n)&&"writable"===n._state){Lr(n,Zr(e))}Jr(e)}(n,r,o),i}Object.defineProperties(Dr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),o(Dr.prototype.abort,"abort"),o(Dr.prototype.close,"close"),o(Dr.prototype.releaseLock,"releaseLock"),o(Dr.prototype.write,"write"),"symbol"==typeof e.toStringTag&&Object.defineProperty(Dr.prototype,e.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});var Nr={},Hr=function(){function WritableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(WritableStreamDefaultController.prototype,"abortReason",{get:function(){if(!Vr(this))throw rt("abortReason");return this._abortReason},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultController.prototype,"signal",{get:function(){if(!Vr(this))throw rt("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal},enumerable:!1,configurable:!0}),WritableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Vr(this))throw rt("error");"writable"===this._controlledWritableStream._state&&$r(this,e)},WritableStreamDefaultController.prototype[R]=function(e){var r=this._abortAlgorithm(e);return Gr(this),r},WritableStreamDefaultController.prototype[T]=function(){Pe(this)},WritableStreamDefaultController}();function Vr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof Hr)}function Ur(e,r,t,n,o,a,i,l){r._controlledWritableStream=e,e._writableStreamController=r,r._queue=void 0,r._queueTotalSize=void 0,Pe(r),r._abortReason=void 0,r._abortController=function(){if("function"==typeof AbortController)return new AbortController}(),r._started=!1,r._strategySizeAlgorithm=l,r._strategyHWM=i,r._writeAlgorithm=n,r._closeAlgorithm=o,r._abortAlgorithm=a;var u=Zr(r);Lr(e,u),p(f(t()),(function(){return r._started=!0,Jr(r),null}),(function(t){return r._started=!0,jr(e,t),null}))}function Gr(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Xr(e){return e._strategyHWM-e._queueTotalSize}function Jr(e){var r=e._controlledWritableStream;if(e._started&&void 0===r._inFlightWriteRequest)if("erroring"!==r._state){if(0!==e._queue.length){var t=e._queue.peek().value;t===Nr?function(e){var r=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(r),Re(e);var t=e._closeAlgorithm();Gr(e),p(t,(function(){return function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&lt(r)}(r),null}),(function(e){return function(e,r){e._inFlightCloseRequest._reject(r),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(r),e._pendingAbortRequest=void 0),jr(e,r)}(r,e),null}))}(e):function(e,r){var t=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(t);var n=e._writeAlgorithm(r);p(n,(function(){!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(t);var r=t._state;if(Re(e),!Ar(t)&&"writable"===r){var n=Zr(e);Lr(t,n)}return Jr(e),null}),(function(r){return"writable"===t._state&&Gr(e),function(e,r){e._inFlightWriteRequest._reject(r),e._inFlightWriteRequest=void 0,jr(e,r)}(t,r),null}))}(e,t)}}else kr(r)}function Kr(e,r){"writable"===e._controlledWritableStream._state&&$r(e,r)}function Zr(e){return Xr(e)<=0}function $r(e,r){var t=e._controlledWritableStream;Gr(e),Br(t,r)}function et(e){return new TypeError("WritableStream.prototype.".concat(e," can only be used on a WritableStream"))}function rt(e){return new TypeError("WritableStreamDefaultController.prototype.".concat(e," can only be used on a WritableStreamDefaultController"))}function tt(e){return new TypeError("WritableStreamDefaultWriter.prototype.".concat(e," can only be used on a WritableStreamDefaultWriter"))}function nt(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function ot(e){e._closedPromise=c((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t,e._closedPromiseState="pending"}))}function at(e,r){ot(e),it(e,r)}function it(e,r){void 0!==e._closedPromise_reject&&(v(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function lt(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function ut(e){e._readyPromise=c((function(r,t){e._readyPromise_resolve=r,e._readyPromise_reject=t})),e._readyPromiseState="pending"}function st(e,r){ut(e),ft(e,r)}function ct(e){ut(e),dt(e)}function ft(e,r){void 0!==e._readyPromise_reject&&(v(e._readyPromise),e._readyPromise_reject(r),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function dt(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(Hr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof e.toStringTag&&Object.defineProperty(Hr.prototype,e.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});var bt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0;var pt,ht=(function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;if("DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(pt=null==bt?void 0:bt.DOMException)?pt:void 0)||function(){var e=function(e,r){this.message=e||"",this.name=r||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return o(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function _t(e,t,n,o,a,i){var l=U(e),u=Pr(t);e._disturbed=!0;var s=!1,m=f(void 0);return c((function(y,g){var S,w,R,T;if(void 0!==i){if(S=function(){var r=void 0!==i.reason?i.reason:new ht("Aborted","AbortError"),n=[];o||n.push((function(){return"writable"===t._state?Or(t,r):f(void 0)})),a||n.push((function(){return"readable"===e._state?xt(e,r):f(void 0)})),E((function(){return Promise.all(n.map((function(e){return e()})))}),!0,r)},i.aborted)return void S();i.addEventListener("abort",S)}if(q(e,l._closedPromise,(function(e){return o?O(!0,e):E((function(){return Or(t,e)}),!0,e),null})),q(t,u._closedPromise,(function(r){return a?O(!0,r):E((function(){return xt(e,r)}),!0,r),null})),w=e,R=l._closedPromise,T=function(){return n?O():E((function(){return function(e){var r=e._ownerWritableStream,t=r._state;return Ar(r)||"closed"===t?f(void 0):"errored"===t?d(r._storedError):Ir(e)}(u)})),null},"closed"===w._state?T():h(R,T),Ar(t)||"closed"===t._state){var P=new TypeError("the destination writable stream closed before all data could be piped to it");a?O(!0,P):E((function(){return xt(e,P)}),!0,P)}function C(){var e=m;return b(m,(function(){return e!==m?C():void 0}))}function q(e,r,t){"errored"===e._state?t(e._storedError):_(r,t)}function E(e,r,n){function o(){return p(e(),(function(){return j(r,n)}),(function(e){return j(!0,e)})),null}s||(s=!0,"writable"!==t._state||Ar(t)?o():h(C(),o))}function O(e,r){s||(s=!0,"writable"!==t._state||Ar(t)?j(e,r):h(C(),(function(){return j(e,r)})))}function j(e,r){return xr(u),W(l),void 0!==i&&i.removeEventListener("abort",S),e?g(r):y(void 0),null}v(c((function(e,t){!function n(o){o?e():b(s?f(!0):b(u._readyPromise,(function(){return c((function(e,t){ne(l,{_chunkSteps:function(t){m=b(Qr(u,t),void 0,r),e(!1)},_closeSteps:function(){return e(!0)},_errorSteps:t})}))})),n,t)}(!1)})))}))}var mt=function(){function ReadableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamDefaultController.prototype,"desiredSize",{get:function(){if(!vt(this))throw Et("desiredSize");return Pt(this)},enumerable:!1,configurable:!0}),ReadableStreamDefaultController.prototype.close=function(){if(!vt(this))throw Et("close");if(!Ct(this))throw new TypeError("The stream is not in a state that permits close");wt(this)},ReadableStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!vt(this))throw Et("enqueue");if(!Ct(this))throw new TypeError("The stream is not in a state that permits enqueue");return Rt(this,e)},ReadableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!vt(this))throw Et("error");Tt(this,e)},ReadableStreamDefaultController.prototype[P]=function(e){Pe(this);var r=this._cancelAlgorithm(e);return St(this),r},ReadableStreamDefaultController.prototype[C]=function(e){var r=this._controlledReadableStream;if(this._queue.length>0){var t=Re(this);this._closeRequested&&0===this._queue.length?(St(this),Qt(r)):yt(this),e._chunkSteps(t)}else G(r,e),yt(this)},ReadableStreamDefaultController.prototype[q]=function(){},ReadableStreamDefaultController}();function vt(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof mt)}function yt(e){gt(e)&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,p(e._pullAlgorithm(),(function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,yt(e)),null}),(function(r){return Tt(e,r),null}))))}function gt(e){var r=e._controlledReadableStream;return!!Ct(e)&&(!!e._started&&(!!(Yt(r)&&J(r)>0)||Pt(e)>0))}function St(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function wt(e){if(Ct(e)){var r=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(St(e),Qt(r))}}function Rt(e,r){if(Ct(e)){var t=e._controlledReadableStream;if(Yt(t)&&J(t)>0)X(t,r,!1);else{var n=void 0;try{n=e._strategySizeAlgorithm(r)}catch(r){throw Tt(e,r),r}try{Te(e,r,n)}catch(r){throw Tt(e,r),r}}yt(e)}}function Tt(e,r){var t=e._controlledReadableStream;"readable"===t._state&&(Pe(e),St(e),Nt(t,r))}function Pt(e){var r=e._controlledReadableStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function Ct(e){var r=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===r}function qt(e,r,t,n,o,a,i){r._controlledReadableStream=e,r._queue=void 0,r._queueTotalSize=void 0,Pe(r),r._started=!1,r._closeRequested=!1,r._pullAgain=!1,r._pulling=!1,r._strategySizeAlgorithm=i,r._strategyHWM=a,r._pullAlgorithm=n,r._cancelAlgorithm=o,e._readableStreamController=r,p(f(t()),(function(){return r._started=!0,yt(r),null}),(function(e){return Tt(r,e),null}))}function Et(e){return new TypeError("ReadableStreamDefaultController.prototype.".concat(e," can only be used on a ReadableStreamDefaultController"))}function Ot(e,r){return Oe(e._readableStreamController)?function(e){var r,t,n,o,a,i=U(e),l=!1,u=!1,s=!1,d=!1,b=!1,p=c((function(e){a=e}));function h(e){_(e._closedPromise,(function(r){return e!==i||(Je(n._readableStreamController,r),Je(o._readableStreamController,r),d&&b||a(void 0)),null}))}function m(){fr(i)&&(W(i),h(i=U(e))),ne(i,{_chunkSteps:function(r){y((function(){u=!1,s=!1;var t=r,i=r;if(!d&&!b)try{i=we(r)}catch(r){return Je(n._readableStreamController,r),Je(o._readableStreamController,r),void a(xt(e,r))}d||Xe(n._readableStreamController,t),b||Xe(o._readableStreamController,i),l=!1,u?g():s&&S()}))},_closeSteps:function(){l=!1,d||Ge(n._readableStreamController),b||Ge(o._readableStreamController),n._readableStreamController._pendingPullIntos.length>0&&er(n._readableStreamController,0),o._readableStreamController._pendingPullIntos.length>0&&er(o._readableStreamController,0),d&&b||a(void 0)},_errorSteps:function(){l=!1}})}function v(r,t){te(i)&&(W(i),h(i=ir(e)));var c=t?o:n,f=t?n:o;dr(i,r,1,{_chunkSteps:function(r){y((function(){u=!1,s=!1;var n=t?b:d;if(t?d:b)n||rr(c._readableStreamController,r);else{var o=void 0;try{o=we(r)}catch(r){return Je(c._readableStreamController,r),Je(f._readableStreamController,r),void a(xt(e,r))}n||rr(c._readableStreamController,r),Xe(f._readableStreamController,o)}l=!1,u?g():s&&S()}))},_closeSteps:function(e){l=!1;var r=t?b:d,n=t?d:b;r||Ge(c._readableStreamController),n||Ge(f._readableStreamController),void 0!==e&&(r||rr(c._readableStreamController,e),!n&&f._readableStreamController._pendingPullIntos.length>0&&er(f._readableStreamController,0)),r&&n||a(void 0)},_errorSteps:function(){l=!1}})}function g(){if(l)return u=!0,f(void 0);l=!0;var e=Ze(n._readableStreamController);return null===e?m():v(e._view,!1),f(void 0)}function S(){if(l)return s=!0,f(void 0);l=!0;var e=Ze(o._readableStreamController);return null===e?m():v(e._view,!0),f(void 0)}function w(n){if(d=!0,r=n,b){var o=ie([r,t]),i=xt(e,o);a(i)}return p}function R(n){if(b=!0,t=n,d){var o=ie([r,t]),i=xt(e,o);a(i)}return p}function T(){}return n=Ft(T,g,w),o=Ft(T,S,R),h(i),[n,o]}(e):function(e,r){var t,n,o,a,i,l=U(e),u=!1,s=!1,d=!1,b=!1,p=c((function(e){i=e}));function h(){return u?(s=!0,f(void 0)):(u=!0,ne(l,{_chunkSteps:function(e){y((function(){s=!1;var r=e,t=e;d||Rt(o._readableStreamController,r),b||Rt(a._readableStreamController,t),u=!1,s&&h()}))},_closeSteps:function(){u=!1,d||wt(o._readableStreamController),b||wt(a._readableStreamController),d&&b||i(void 0)},_errorSteps:function(){u=!1}}),f(void 0))}function m(r){if(d=!0,t=r,b){var o=ie([t,n]),a=xt(e,o);i(a)}return p}function v(r){if(b=!0,n=r,d){var o=ie([t,n]),a=xt(e,o);i(a)}return p}function g(){}return o=Dt(g,h,m),a=Dt(g,h,v),_(l._closedPromise,(function(e){return Tt(o._readableStreamController,e),Tt(a._readableStreamController,e),d&&b||i(void 0),null})),[o,a]}(e)}function Wt(e){return t(n=e)&&void 0!==n.getReader?function(e){var n;function o(){var r;try{r=e.read()}catch(e){return d(e)}return m(r,(function(e){if(!t(e))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)wt(n._readableStreamController);else{var r=e.value;Rt(n._readableStreamController,r)}}))}function a(r){try{return f(e.cancel(r))}catch(e){return d(e)}}return n=Dt(r,o,a,0),n}(e.getReader()):function(e){var n,o=he(e,"async");function a(){var e;try{e=_e(o)}catch(e){return d(e)}return m(f(e),(function(e){if(!t(e))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)wt(n._readableStreamController);else{var r=e.value;Rt(n._readableStreamController,r)}}))}function i(e){var r,n=o.iterator;try{r=fe(n,"return")}catch(e){return d(e)}return void 0===r?f(void 0):m(S(r,n,[e]),(function(e){if(!t(e))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}return n=Dt(r,a,i,0),n}(e);var n}function jt(e,r,t){return I(e,t),function(t){return S(e,r,[t])}}function Bt(e,r,t){return I(e,t),function(t){return S(e,r,[t])}}function kt(e,r,t){return I(e,t),function(t){return g(e,r,[t])}}function At(e,r){if("bytes"!==(e="".concat(e)))throw new TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamType"));return e}function zt(e,r){F(e,r);var t=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,o=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,r){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError("".concat(r," is not an AbortSignal."))}(a,"".concat(r," has member 'signal' that")),{preventAbort:Boolean(t),preventCancel:Boolean(n),preventClose:Boolean(o),signal:a}}Object.defineProperties(mt.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),o(mt.prototype.close,"close"),o(mt.prototype.enqueue,"enqueue"),o(mt.prototype.error,"error"),"symbol"==typeof e.toStringTag&&Object.defineProperty(mt.prototype,e.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});var Lt=function(){function ReadableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:M(e,"First parameter");var t=mr(r,"Second parameter"),n=function(e,r){F(e,r);var t=e,n=null==t?void 0:t.autoAllocateChunkSize,o=null==t?void 0:t.cancel,a=null==t?void 0:t.pull,i=null==t?void 0:t.start,l=null==t?void 0:t.type;return{autoAllocateChunkSize:void 0===n?void 0:H(n,"".concat(r," has member 'autoAllocateChunkSize' that")),cancel:void 0===o?void 0:jt(o,t,"".concat(r," has member 'cancel' that")),pull:void 0===a?void 0:Bt(a,t,"".concat(r," has member 'pull' that")),start:void 0===i?void 0:kt(i,t,"".concat(r," has member 'start' that")),type:void 0===l?void 0:At(l,"".concat(r," has member 'type' that"))}}(e,"First parameter");if(It(this),"bytes"===n.type){if(void 0!==t.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,r,t){var n,o,a,i=Object.create(Ee.prototype);n=void 0!==r.start?function(){return r.start(i)}:function(){},o=void 0!==r.pull?function(){return r.pull(i)}:function(){return f(void 0)},a=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return f(void 0)};var l=r.autoAllocateChunkSize;if(0===l)throw new TypeError("autoAllocateChunkSize must be greater than 0");tr(e,i,n,o,a,t,l)}(this,n,hr(t,0))}else{var o=_r(t);!function(e,r,t,n){var o,a,i,l=Object.create(mt.prototype);o=void 0!==r.start?function(){return r.start(l)}:function(){},a=void 0!==r.pull?function(){return r.pull(l)}:function(){return f(void 0)},i=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return f(void 0)},qt(e,l,o,a,i,t,n)}(this,n,hr(t,1),o)}}return Object.defineProperty(ReadableStream.prototype,"locked",{get:function(){if(!Mt(this))throw Ht("locked");return Yt(this)},enumerable:!1,configurable:!0}),ReadableStream.prototype.cancel=function(e){return void 0===e&&(e=void 0),Mt(this)?Yt(this)?d(new TypeError("Cannot cancel a stream that already has a reader")):xt(this,e):d(Ht("cancel"))},ReadableStream.prototype.getReader=function(e){if(void 0===e&&(e=void 0),!Mt(this))throw Ht("getReader");return void 0===function(e,r){F(e,r);var t=null==e?void 0:e.mode;return{mode:void 0===t?void 0:ar(t,"".concat(r," has member 'mode' that"))}}(e,"First parameter").mode?U(this):ir(this)},ReadableStream.prototype.pipeThrough=function(e,r){if(void 0===r&&(r={}),!Mt(this))throw Ht("pipeThrough");Y(e,1,"pipeThrough");var t=function(e,r){F(e,r);var t=null==e?void 0:e.readable;x(t,"readable","ReadableWritablePair"),V(t,"".concat(r," has member 'readable' that"));var n=null==e?void 0:e.writable;return x(n,"writable","ReadableWritablePair"),Rr(n,"".concat(r," has member 'writable' that")),{readable:t,writable:n}}(e,"First parameter"),n=zt(r,"Second parameter");if(Yt(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Er(t.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return v(_t(this,t.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),t.readable},ReadableStream.prototype.pipeTo=function(e,r){if(void 0===r&&(r={}),!Mt(this))return d(Ht("pipeTo"));if(void 0===e)return d("Parameter 1 is required in 'pipeTo'.");if(!qr(e))return d(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));var t;try{t=zt(r,"Second parameter")}catch(e){return d(e)}return Yt(this)?d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Er(e)?d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):_t(this,e,t.preventClose,t.preventAbort,t.preventCancel,t.signal)},ReadableStream.prototype.tee=function(){if(!Mt(this))throw Ht("tee");return ie(Ot(this))},ReadableStream.prototype.values=function(e){if(void 0===e&&(e=void 0),!Mt(this))throw Ht("values");var r,t,n,o,a,i=function(e,r){F(e,r);var t=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(t)}}(e,"First parameter");return r=this,t=i.preventCancel,n=U(r),o=new me(n,t),(a=Object.create(ve))._asyncIteratorImpl=o,a},ReadableStream.prototype[pe]=function(e){return this.values(e)},ReadableStream.from=function(e){return Wt(e)},ReadableStream}();function Dt(e,r,t,n,o){void 0===n&&(n=1),void 0===o&&(o=function(){return 1});var a=Object.create(Lt.prototype);return It(a),qt(a,Object.create(mt.prototype),e,r,t,n,o),a}function Ft(e,r,t){var n=Object.create(Lt.prototype);return It(n),tr(n,Object.create(Ee.prototype),e,r,t,0,void 0),n}function It(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Mt(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof Lt)}function Yt(e){return void 0!==e._reader}function xt(e,t){if(e._disturbed=!0,"closed"===e._state)return f(void 0);if("errored"===e._state)return d(e._storedError);Qt(e);var n=e._reader;if(void 0!==n&&fr(n)){var o=n._readIntoRequests;n._readIntoRequests=new w,o.forEach((function(e){e._closeSteps(void 0)}))}return m(e._readableStreamController[P](t),r)}function Qt(e){e._state="closed";var r=e._reader;if(void 0!==r&&(z(r),te(r))){var t=r._readRequests;r._readRequests=new w,t.forEach((function(e){e._closeSteps()}))}}function Nt(e,r){e._state="errored",e._storedError=r;var t=e._reader;void 0!==t&&(A(t,r),te(t)?oe(t,r):br(t,r))}function Ht(e){return new TypeError("ReadableStream.prototype.".concat(e," can only be used on a ReadableStream"))}function Vt(e,r){F(e,r);var t=null==e?void 0:e.highWaterMark;return x(t,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Q(t)}}Object.defineProperties(Lt,{from:{enumerable:!0}}),Object.defineProperties(Lt.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),o(Lt.from,"from"),o(Lt.prototype.cancel,"cancel"),o(Lt.prototype.getReader,"getReader"),o(Lt.prototype.pipeThrough,"pipeThrough"),o(Lt.prototype.pipeTo,"pipeTo"),o(Lt.prototype.tee,"tee"),o(Lt.prototype.values,"values"),"symbol"==typeof e.toStringTag&&Object.defineProperty(Lt.prototype,e.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(Lt.prototype,pe,{value:Lt.prototype.values,writable:!0,configurable:!0});var Ut=function(e){return e.byteLength};o(Ut,"size");var Gt=function(){function ByteLengthQueuingStrategy(e){Y(e,1,"ByteLengthQueuingStrategy"),e=Vt(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(ByteLengthQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!Jt(this))throw Xt("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(ByteLengthQueuingStrategy.prototype,"size",{get:function(){if(!Jt(this))throw Xt("size");return Ut},enumerable:!1,configurable:!0}),ByteLengthQueuingStrategy}();function Xt(e){return new TypeError("ByteLengthQueuingStrategy.prototype.".concat(e," can only be used on a ByteLengthQueuingStrategy"))}function Jt(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof Gt)}Object.defineProperties(Gt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof e.toStringTag&&Object.defineProperty(Gt.prototype,e.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});var Kt=function(){return 1};o(Kt,"size");var Zt=function(){function CountQueuingStrategy(e){Y(e,1,"CountQueuingStrategy"),e=Vt(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(CountQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!en(this))throw $t("highWaterMark");return this._countQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(CountQueuingStrategy.prototype,"size",{get:function(){if(!en(this))throw $t("size");return Kt},enumerable:!1,configurable:!0}),CountQueuingStrategy}();function $t(e){return new TypeError("CountQueuingStrategy.prototype.".concat(e," can only be used on a CountQueuingStrategy"))}function en(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof Zt)}function rn(e,r,t){return I(e,t),function(t){return S(e,r,[t])}}function tn(e,r,t){return I(e,t),function(t){return g(e,r,[t])}}function nn(e,r,t){return I(e,t),function(t,n){return S(e,r,[t,n])}}function on(e,r,t){return I(e,t),function(t){return S(e,r,[t])}}Object.defineProperties(Zt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof e.toStringTag&&Object.defineProperty(Zt.prototype,e.toStringTag,{value:"CountQueuingStrategy",configurable:!0});var an=function(){function TransformStream(e,r,t){void 0===e&&(e={}),void 0===r&&(r={}),void 0===t&&(t={}),void 0===e&&(e=null);var n=mr(r,"Second parameter"),o=mr(t,"Third parameter"),a=function(e,r){F(e,r);var t=null==e?void 0:e.cancel,n=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,a=null==e?void 0:e.start,i=null==e?void 0:e.transform,l=null==e?void 0:e.writableType;return{cancel:void 0===t?void 0:on(t,e,"".concat(r," has member 'cancel' that")),flush:void 0===n?void 0:rn(n,e,"".concat(r," has member 'flush' that")),readableType:o,start:void 0===a?void 0:tn(a,e,"".concat(r," has member 'start' that")),transform:void 0===i?void 0:nn(i,e,"".concat(r," has member 'transform' that")),writableType:l}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");var i,l=hr(o,0),u=_r(o),s=hr(n,1),b=_r(n);!function(e,r,t,n,o,a){function i(){return r}function l(r){return function(e,r){var t=e._transformStreamController;if(e._backpressure){return m(e._backpressureChangePromise,(function(){var n=e._writable;if("erroring"===n._state)throw n._storedError;return _n(t,r)}))}return _n(t,r)}(e,r)}function u(r){return function(e,r){var t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;var n=e._readable;t._finishPromise=c((function(e,r){t._finishPromise_resolve=e,t._finishPromise_reject=r}));var o=t._cancelAlgorithm(r);return pn(t),p(o,(function(){return"errored"===n._state?yn(t,n._storedError):(Tt(n._readableStreamController,r),vn(t)),null}),(function(e){return Tt(n._readableStreamController,e),yn(t,e),null})),t._finishPromise}(e,r)}function s(){return function(e){var r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;var t=e._readable;r._finishPromise=c((function(e,t){r._finishPromise_resolve=e,r._finishPromise_reject=t}));var n=r._flushAlgorithm();return pn(r),p(n,(function(){return"errored"===t._state?yn(r,t._storedError):(wt(t._readableStreamController),vn(r)),null}),(function(e){return Tt(t._readableStreamController,e),yn(r,e),null})),r._finishPromise}(e)}function f(){return function(e){return fn(e,!1),e._backpressureChangePromise}(e)}function d(r){return function(e,r){var t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;var n=e._writable;t._finishPromise=c((function(e,r){t._finishPromise_resolve=e,t._finishPromise_reject=r}));var o=t._cancelAlgorithm(r);return pn(t),p(o,(function(){return"errored"===n._state?yn(t,n._storedError):(Kr(n._writableStreamController,r),cn(e),vn(t)),null}),(function(r){return Kr(n._writableStreamController,r),cn(e),yn(t,r),null})),t._finishPromise}(e,r)}e._writable=function(e,r,t,n,o,a){void 0===o&&(o=1),void 0===a&&(a=function(){return 1});var i=Object.create(Tr.prototype);return Cr(i),Ur(i,Object.create(Hr.prototype),e,r,t,n,o,a),i}(i,l,s,u,t,n),e._readable=Dt(i,f,d,o,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,fn(e,!0),e._transformStreamController=void 0}(this,c((function(e){i=e})),s,b,l,u),function(e,r){var t,n,o,a=Object.create(dn.prototype);t=void 0!==r.transform?function(e){return r.transform(e,a)}:function(e){try{return hn(a,e),f(void 0)}catch(e){return d(e)}};n=void 0!==r.flush?function(){return r.flush(a)}:function(){return f(void 0)};o=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return f(void 0)};!function(e,r,t,n,o){r._controlledTransformStream=e,e._transformStreamController=r,r._transformAlgorithm=t,r._flushAlgorithm=n,r._cancelAlgorithm=o,r._finishPromise=void 0,r._finishPromise_resolve=void 0,r._finishPromise_reject=void 0}(e,a,t,n,o)}(this,a),void 0!==a.start?i(a.start(this._transformStreamController)):i(void 0)}return Object.defineProperty(TransformStream.prototype,"readable",{get:function(){if(!ln(this))throw gn("readable");return this._readable},enumerable:!1,configurable:!0}),Object.defineProperty(TransformStream.prototype,"writable",{get:function(){if(!ln(this))throw gn("writable");return this._writable},enumerable:!1,configurable:!0}),TransformStream}();function ln(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof an)}function un(e,r){Tt(e._readable._readableStreamController,r),sn(e,r)}function sn(e,r){pn(e._transformStreamController),Kr(e._writable._writableStreamController,r),cn(e)}function cn(e){e._backpressure&&fn(e,!1)}function fn(e,r){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=c((function(r){e._backpressureChangePromise_resolve=r})),e._backpressure=r}Object.defineProperties(an.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof e.toStringTag&&Object.defineProperty(an.prototype,e.toStringTag,{value:"TransformStream",configurable:!0});var dn=function(){function TransformStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(TransformStreamDefaultController.prototype,"desiredSize",{get:function(){if(!bn(this))throw mn("desiredSize");return Pt(this._controlledTransformStream._readable._readableStreamController)},enumerable:!1,configurable:!0}),TransformStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!bn(this))throw mn("enqueue");hn(this,e)},TransformStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!bn(this))throw mn("error");var r;r=e,un(this._controlledTransformStream,r)},TransformStreamDefaultController.prototype.terminate=function(){if(!bn(this))throw mn("terminate");!function(e){var r=e._controlledTransformStream;wt(r._readable._readableStreamController);var t=new TypeError("TransformStream terminated");sn(r,t)}(this)},TransformStreamDefaultController}();function bn(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof dn)}function pn(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function hn(e,r){var t=e._controlledTransformStream,n=t._readable._readableStreamController;if(!Ct(n))throw new TypeError("Readable side is not in a state that permits enqueue");try{Rt(n,r)}catch(e){throw sn(t,e),t._readable._storedError}var o=function(e){return!gt(e)}(n);o!==t._backpressure&&fn(t,!0)}function _n(e,r){return m(e._transformAlgorithm(r),void 0,(function(r){throw un(e._controlledTransformStream,r),r}))}function mn(e){return new TypeError("TransformStreamDefaultController.prototype.".concat(e," can only be used on a TransformStreamDefaultController"))}function vn(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function yn(e,r){void 0!==e._finishPromise_reject&&(v(e._finishPromise),e._finishPromise_reject(r),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function gn(e){return new TypeError("TransformStream.prototype.".concat(e," can only be used on a TransformStream"))}Object.defineProperties(dn.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),o(dn.prototype.enqueue,"enqueue"),o(dn.prototype.error,"error"),o(dn.prototype.terminate,"terminate"),"symbol"==typeof e.toStringTag&&Object.defineProperty(dn.prototype,e.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});var Sn={ReadableStream:Lt,ReadableStreamDefaultController:mt,ReadableByteStreamController:Ee,ReadableStreamBYOBRequest:qe,ReadableStreamDefaultReader:re,ReadableStreamBYOBReader:cr,WritableStream:Tr,WritableStreamDefaultController:Hr,WritableStreamDefaultWriter:Dr,ByteLengthQueuingStrategy:Gt,CountQueuingStrategy:Zt,TransformStream:an,TransformStreamDefaultController:dn};for(var wn in Sn)Object.prototype.hasOwnProperty.call(Sn,wn)&&Object.defineProperty(bt,wn,{value:Sn[wn],writable:!0,configurable:!0})}();
