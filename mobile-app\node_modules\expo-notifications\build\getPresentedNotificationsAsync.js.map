{"version": 3, "file": "getPresentedNotificationsAsync.js", "sourceRoot": "", "sources": ["../src/getPresentedNotificationsAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,qBAAqB,MAAM,+BAA+B,CAAC;AAElE,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAElE;;;;;GAKG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,8BAA8B;IAC1D,IAAI,CAAC,qBAAqB,CAAC,8BAA8B,EAAE,CAAC;QAC1D,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,gCAAgC,CAAC,CAAC;IACnF,CAAC;IAED,OAAO,CAAC,MAAM,qBAAqB,CAAC,8BAA8B,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CACzF,eAAe,CAAC,YAAY,CAAC,CAC9B,CAAC;AACJ,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationPresenter from './NotificationPresenterModule';\nimport { Notification } from './Notifications.types';\nimport { mapNotification } from './utils/mapNotificationResponse';\n\n/**\n * Fetches information about all notifications present in the notification tray (Notification Center).\n * > This method is not supported on Android below 6.0 (API level 23) – on these devices it will resolve to an empty array.\n * @return A Promise which resolves with a list of notifications ([`Notification`](#notification)) currently present in the notification tray (Notification Center).\n * @header dismiss\n */\nexport default async function getPresentedNotificationsAsync(): Promise<Notification[]> {\n  if (!NotificationPresenter.getPresentedNotificationsAsync) {\n    throw new UnavailabilityError('Notifications', 'getPresentedNotificationsAsync');\n  }\n\n  return (await NotificationPresenter.getPresentedNotificationsAsync()).map((notification) =>\n    mapNotification(notification)\n  );\n}\n"]}