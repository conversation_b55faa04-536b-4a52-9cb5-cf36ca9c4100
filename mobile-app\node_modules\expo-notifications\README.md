<p>
  <a href="https://docs.expo.dev/versions/latest/sdk/notifications/">
    <img
      src="../../.github/resources/expo-notifications.svg"
      alt="expo-notifications"
      height="64" />
  </a>
</p>

Provides an API to fetch push notification tokens and to present, schedule, receive and respond to notifications.

## Features

- 📣 schedule a one-off notification for a specific date, or some time from now,
- 🔁 schedule a notification repeating in some time interval (or a calendar date match on iOS),
- 1️⃣ get and set application badge icon number,
- 📲 fetch a native device push token so you can send push notifications with FCM and APNS,
- 😎 fetch an Expo push token so you can send push notifications with Expo,
- 📬 listen to incoming notifications in the foreground and background,
- 👆 listen to interactions with notifications (tapping or dismissing),
- 🎛 handle notifications when the app is in foreground,
- 🔕 imperatively dismiss notifications from Notification Center/tray,
- 🗂 create, update, delete Android notification channels,
- 🎨 set custom icon and color for notifications on Android.

# Guides

Please refer to our [notification guides](https://docs.expo.dev/push-notifications/overview/).

# API documentation

- [Documentation for the latest stable release](https://docs.expo.dev/versions/latest/sdk/notifications/)
- [Documentation for the main branch](https://docs.expo.dev/versions/unversioned/sdk/notifications/)
