{"cli": {"version": ">= 12.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": true, "buildConfiguration": "Debug"}, "android": {"resourceClass": "medium", "gradleCommand": ":app:assembleDebug", "buildType": "apk"}, "env": {"ENVIRONMENT": "development", "API_BASE_URL": "https://dev-api.deliveryapp.africa", "SUPABASE_URL": "https://dev-project.supabase.co", "ENABLE_LOGGING": "true", "ENABLE_FLIPPER": "true"}}, "staging": {"distribution": "internal", "ios": {"resourceClass": "m-medium", "buildConfiguration": "Release", "autoIncrement": "buildNumber"}, "android": {"resourceClass": "medium", "buildType": "aab", "autoIncrement": "versionCode"}, "env": {"ENVIRONMENT": "staging", "API_BASE_URL": "https://staging-api.deliveryapp.africa", "SUPABASE_URL": "https://staging-project.supabase.co", "ENABLE_LOGGING": "true", "ENABLE_FLIPPER": "false"}}, "production": {"distribution": "store", "ios": {"resourceClass": "m-large", "buildConfiguration": "Release", "autoIncrement": "buildNumber", "bundleIdentifier": "com.deliveryapp.africa"}, "android": {"resourceClass": "large", "buildType": "aab", "autoIncrement": "versionCode", "applicationId": "com.deliveryapp.africa"}, "env": {"ENVIRONMENT": "production", "API_BASE_URL": "https://api.deliveryapp.africa", "SUPABASE_URL": "https://prod-project.supabase.co", "ENABLE_LOGGING": "false", "ENABLE_FLIPPER": "false"}}, "production-africa": {"extends": "production", "distribution": "store", "ios": {"resourceClass": "m-large", "buildConfiguration": "Release", "autoIncrement": "buildNumber", "bundleIdentifier": "com.deliveryapp.africa"}, "android": {"resourceClass": "large", "buildType": "aab", "autoIncrement": "versionCode", "applicationId": "com.deliveryapp.africa"}, "env": {"ENVIRONMENT": "production", "API_BASE_URL": "https://api.deliveryapp.africa", "SUPABASE_URL": "https://prod-project.supabase.co", "ENABLE_LOGGING": "false", "ENABLE_FLIPPER": "false", "REGION": "africa", "DEFAULT_COUNTRY": "SEN", "DEFAULT_CURRENCY": "XOF", "DEFAULT_LANGUAGE": "fr", "OFFLINE_SUPPORT": "true", "LOW_BANDWIDTH_MODE": "true", "COMPRESSION_ENABLED": "true", "CACHE_SIZE": "50MB", "SYNC_INTERVAL": "300000", "MAX_RETRIES": "3", "REQUEST_TIMEOUT": "30000"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "*********", "appleTeamId": "YOUR_TEAM_ID", "sku": "delivery-app-africa-ios", "language": "en-US", "companyName": "Delivery App Africa Ltd", "appName": "Delivery App Africa", "copyright": "2024 Delivery App Africa Ltd. All rights reserved.", "primaryCategory": "FOOD_AND_DRINK", "secondaryCategory": "BUSINESS", "contentRights": {"containsThirdPartyContent": false, "usesIdfa": true}, "reviewDetails": {"firstName": "Review", "lastName": "Team", "email": "<EMAIL>", "phone": "+************", "demoAccountName": "<EMAIL>", "demoAccountPassword": "DemoPassword123!", "notes": "This is a delivery app for African markets with mobile money integration. Demo account provides access to all features including ordering, payment, and delivery tracking."}, "appReviewInformation": {"contactFirstName": "Support", "contactLastName": "Team", "contactPhone": "+************", "contactEmail": "<EMAIL>", "demoAccountName": "<EMAIL>", "demoAccountPassword": "DemoPassword123!", "notes": "Test with demo account. App supports multiple African countries and mobile money payments."}}, "android": {"serviceAccountKeyPath": "./google-play-service-account.json", "track": "production", "releaseStatus": "completed", "applicationId": "com.deliveryapp.africa"}}, "staging": {"ios": {"appleId": "<EMAIL>", "ascAppId": "*********", "appleTeamId": "YOUR_TEAM_ID"}, "android": {"serviceAccountKeyPath": "./google-play-service-account.json", "track": "internal", "releaseStatus": "draft"}}}, "metadata": {"ios": {"infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs location access to show your current position and calculate delivery distances.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs background location access to track deliveries and provide real-time updates.", "NSCameraUsageDescription": "This app needs camera access to take photos for proof of delivery and profile pictures.", "NSMicrophoneUsageDescription": "This app needs microphone access for voice notes and customer communication.", "NSPhotoLibraryUsageDescription": "This app needs photo library access to select images for profile and delivery proof.", "NSContactsUsageDescription": "This app needs contacts access to easily invite friends and family.", "NSFaceIDUsageDescription": "This app uses Face ID for secure authentication and payment verification.", "UIBackgroundModes": ["location", "background-fetch", "remote-notification"], "LSApplicationQueriesSchemes": ["tel", "mailto", "sms", "whatsapp", "fb", "twitter"], "ITSAppUsesNonExemptEncryption": false}}, "android": {"permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "CAMERA", "RECORD_AUDIO", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "READ_MEDIA_IMAGES", "READ_MEDIA_VIDEO", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "WAKE_LOCK", "INTERNET", "ACCESS_NETWORK_STATE", "ACCESS_WIFI_STATE", "CALL_PHONE", "READ_PHONE_STATE", "SEND_SMS", "READ_CONTACTS", "WRITE_CONTACTS", "USE_FINGERPRINT", "USE_BIOMETRIC", "FOREGROUND_SERVICE", "FOREGROUND_SERVICE_LOCATION", "POST_NOTIFICATIONS"]}}}