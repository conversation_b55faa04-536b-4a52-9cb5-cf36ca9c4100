import React from 'react';
import { View, Text, StyleSheet, TextInput, Button, TouchableOpacity } from 'react-native';
import { useAuth } from '../context/AuthContext';

const AuthScreen = () => {
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [confirmPassword, setConfirmPassword] = React.useState('');
  const [isLogin, setIsLogin] = React.useState(true); // State to toggle between login and signup
  const { login, signup } = useAuth();
  const [error, setError] = React.useState<string | null>(null);

  const handleLogin = async () => {
    setError(null);
    const { error: loginError } = await login(email, password);
    if (loginError) {
      setError(loginError.message);
    }
  };

  const handleSignup = async () => {
    setError(null);
    if (password !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }
    // TODO: Add more robust validation (email format, password strength)
    const { error: signupError } = await signup(email, password);
    if (signupError) {
      setError(signupError.message);
    } else {
      // TODO: Show success message and/or redirect to email confirmation screen
      console.log('Signup successful, check email for confirmation.');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{isLogin ? 'Login' : 'Sign Up'}</Text>

      <TextInput
        style={styles.input}
        placeholder="Email"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <TextInput
        style={styles.input}
        placeholder="Password"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
      />

      {!isLogin && (
        <TextInput
          style={styles.input}
          placeholder="Confirm Password"
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          secureTextEntry
        />
      )}

      {error && <Text style={styles.errorText}>{error}</Text>}

      <Button title={isLogin ? 'Login' : 'Sign Up'} onPress={isLogin ? handleLogin : handleSignup} />

      <TouchableOpacity onPress={() => setIsLogin(!isLogin)}>
        <Text style={styles.switchText}>
          {isLogin ? 'Need an account? Sign Up' : 'Already have an account? Login'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    marginBottom: 20,
  },
  input: {
    width: '100%',
    padding: 10,
    marginVertical: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
  },
  switchText: {
    marginTop: 20,
    color: 'blue',
  },
  errorText: {
    color: 'red',
    marginTop: 10,
  },
});

export default AuthScreen;