{"version": 3, "file": "ServerRegistrationModule.web.js", "sourceRoot": "", "sources": ["../src/ServerRegistrationModule.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAIrD,MAAM,mBAAmB,GAAG,oCAAoC,CAAC;AACjE,MAAM,qBAAqB,GAAG,sCAAsC,CAAC;AAErE,uDAAuD;AACvD,IAAI,yBAAyB,GAAG,GAAG,EAAE;IACnC,MAAM,qBAAqB,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;IACxC,yBAAyB,GAAG,GAAG,EAAE,CAAC,qBAAqB,CAAC;AAC1D,CAAC,CAAC;AAEF,eAAe;IACb,sBAAsB,EAAE,KAAK,IAAI,EAAE;QACjC,IAAI,cAAc,CAAC;QAEnB,IAAI,CAAC;YACH,cAAc,GAAG,YAAY,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YAC3D,IAAI,CAAC,cAAc,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE,CAAC;gBAC1D,cAAc,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC3B,YAAY,CAAC,OAAO,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,cAAc,GAAG,yBAAyB,EAAE,CAAC;QAC/C,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IACD,wBAAwB,EAAE,KAAK,IAAI,EAAE;QACnC,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,YAAY,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;IACrD,CAAC;IACD,wBAAwB,EAAE,KAAK,EAAE,gBAA+B,EAAE,EAAE;QAClE,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;YACxC,OAAO;QACT,CAAC;QACD,IAAI,CAAC;YACH,IAAI,gBAAgB,EAAE,CAAC;gBACrB,YAAY,CAAC,OAAO,CAAC,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,UAAU,CAClB,iCAAiC,EACjC,2EAA2E,KAAK,EAAE,CACnF,CAAC;QACJ,CAAC;IACH,CAAC;IACD,uBAAuB;IACvB,WAAW,EAAE,GAAG,EAAE,GAAE,CAAC;IACrB,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;CACE,CAAC", "sourcesContent": ["import { CodedError, uuid } from 'expo-modules-core';\n\nimport { ServerRegistrationModule } from './ServerRegistrationModule.types';\n\nconst INSTALLATION_ID_KEY = 'EXPO_NOTIFICATIONS_INSTALLATION_ID';\nconst REGISTRATION_INFO_KEY = 'EXPO_NOTIFICATIONS_REGISTRATION_INFO';\n\n// Lazy fallback installationId per session initializer\nlet getFallbackInstallationId = () => {\n  const sessionInstallationId = uuid.v4();\n  getFallbackInstallationId = () => sessionInstallationId;\n};\n\nexport default {\n  getInstallationIdAsync: async () => {\n    let installationId;\n\n    try {\n      installationId = localStorage.getItem(INSTALLATION_ID_KEY);\n      if (!installationId || typeof installationId !== 'string') {\n        installationId = uuid.v4();\n        localStorage.setItem(INSTALLATION_ID_KEY, installationId);\n      }\n    } catch {\n      installationId = getFallbackInstallationId();\n    }\n\n    return installationId;\n  },\n  getRegistrationInfoAsync: async () => {\n    if (typeof localStorage === 'undefined') {\n      return null;\n    }\n    return localStorage.getItem(REGISTRATION_INFO_KEY);\n  },\n  setRegistrationInfoAsync: async (registrationInfo: string | null) => {\n    if (typeof localStorage === 'undefined') {\n      return;\n    }\n    try {\n      if (registrationInfo) {\n        localStorage.setItem(REGISTRATION_INFO_KEY, registrationInfo);\n      } else {\n        localStorage.removeItem(REGISTRATION_INFO_KEY);\n      }\n    } catch (error) {\n      throw new CodedError(\n        'ERR_NOTIFICATIONS_STORAGE_ERROR',\n        `Could not modify localStorage to persist auto-registration information: ${error}`\n      );\n    }\n  },\n  // mock implementations\n  addListener: () => {},\n  removeListeners: () => {},\n} as ServerRegistrationModule;\n"]}