// =====================================================
// ÉCRAN INSCRIPTION - US-C002
// =====================================================

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuthStore } from '../../store/authStore';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';

// Schéma de validation
const registerSchema = z.object({
  firstName: z
    .string()
    .min(2, 'Le prénom doit contenir au moins 2 caractères'),
  lastName: z
    .string()
    .min(2, 'Le nom doit contenir au moins 2 caractères'),
  phone: z
    .string()
    .min(8, 'Numéro de téléphone invalide')
    .regex(/^[+]?[0-9\s-()]+$/, 'Format de téléphone invalide'),
  email: z
    .string()
    .email('Format email invalide')
    .optional()
    .or(z.literal('')),
  password: z
    .string()
    .min(6, 'Le mot de passe doit contenir au moins 6 caractères'),
  confirmPassword: z
    .string()
    .min(6, 'Confirmation requise'),
  role: z.enum(['client', 'deliverer', 'merchant']),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Les mots de passe ne correspondent pas",
  path: ["confirmPassword"],
});

type RegisterFormData = z.infer<typeof registerSchema>;

const roles = [
  {
    id: 'client',
    title: 'Client',
    description: 'Je veux commander des produits',
    emoji: '🛒',
    color: '#007AFF',
  },
  {
    id: 'deliverer',
    title: 'Livreur',
    description: 'Je veux livrer des commandes',
    emoji: '🚚',
    color: '#34C759',
  },
  {
    id: 'merchant',
    title: 'Commerçant',
    description: 'Je veux vendre mes produits',
    emoji: '🏪',
    color: '#FF9500',
  },
];

export default function RegisterScreen() {
  const { register, isLoading, error, clearError } = useAuthStore();
  const [selectedRole, setSelectedRole] = useState<string>('client');

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    mode: 'onChange',
    defaultValues: {
      role: 'client',
    },
  });

  const onSubmit = async (data: RegisterFormData) => {
    try {
      clearError();
      
      const registerData = {
        ...data,
        role: selectedRole as 'client' | 'deliverer' | 'merchant',
        profile: {
          firstName: data.firstName,
          lastName: data.lastName,
          phone: data.phone,
          email: data.email || undefined,
        },
      };

      await register(registerData);
      
      // Redirection vers vérification téléphone
      router.push('/(auth)/phone-verification');
    } catch (error) {
      console.error('❌ Erreur inscription:', error);
      Alert.alert(
        'Erreur d\'inscription',
        'Une erreur est survenue. Veuillez réessayer.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Créer un compte</Text>
            <Text style={styles.subtitle}>
              Rejoignez la communauté DeliveryApp Africa
            </Text>
          </View>

          {/* Sélection du rôle */}
          <View style={styles.roleSection}>
            <Text style={styles.sectionTitle}>Je suis un :</Text>
            <View style={styles.roleGrid}>
              {roles.map((role) => (
                <Button
                  key={role.id}
                  title={`${role.emoji} ${role.title}`}
                  onPress={() => setSelectedRole(role.id)}
                  variant={selectedRole === role.id ? 'primary' : 'outline'}
                  size="medium"
                  style={[
                    styles.roleButton,
                    selectedRole === role.id && { backgroundColor: role.color },
                  ]}
                />
              ))}
            </View>
            <Text style={styles.roleDescription}>
              {roles.find(r => r.id === selectedRole)?.description}
            </Text>
          </View>

          {/* Formulaire */}
          <View style={styles.form}>
            <View style={styles.nameRow}>
              <Controller
                control={control}
                name="firstName"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Prénom"
                    placeholder="Votre prénom"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={errors.firstName?.message}
                    containerStyle={styles.halfInput}
                    required
                  />
                )}
              />

              <Controller
                control={control}
                name="lastName"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Nom"
                    placeholder="Votre nom"
                    value={value}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={errors.lastName?.message}
                    containerStyle={styles.halfInput}
                    required
                  />
                )}
              />
            </View>

            <Controller
              control={control}
              name="phone"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Numéro de téléphone"
                  placeholder="+221 70 123 45 67"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.phone?.message}
                  keyboardType="phone-pad"
                  leftIcon="call"
                  required
                />
              )}
            />

            <Controller
              control={control}
              name="email"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Email (optionnel)"
                  placeholder="<EMAIL>"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.email?.message}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  leftIcon="mail"
                />
              )}
            />

            <Controller
              control={control}
              name="password"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Mot de passe"
                  placeholder="Minimum 6 caractères"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.password?.message}
                  secureTextEntry
                  leftIcon="lock-closed"
                  required
                />
              )}
            />

            <Controller
              control={control}
              name="confirmPassword"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Confirmer le mot de passe"
                  placeholder="Répétez votre mot de passe"
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.confirmPassword?.message}
                  secureTextEntry
                  leftIcon="lock-closed"
                  required
                />
              )}
            />

            {/* Erreur globale */}
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            {/* Bouton d'inscription */}
            <Button
              title="Créer mon compte"
              onPress={handleSubmit(onSubmit)}
              variant="primary"
              size="large"
              fullWidth
              loading={isLoading}
              disabled={!isValid || isLoading}
              style={styles.registerButton}
            />
          </View>

          {/* Lien de connexion */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Déjà un compte ?{' '}
            </Text>
            <Button
              title="Se connecter"
              onPress={handleLogin}
              variant="ghost"
              size="small"
              style={styles.loginLink}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
  },
  roleSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 16,
  },
  roleGrid: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  roleButton: {
    flex: 1,
  },
  roleDescription: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  form: {
    marginBottom: 32,
  },
  nameRow: {
    flexDirection: 'row',
    gap: 12,
  },
  halfInput: {
    flex: 1,
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    color: '#D32F2F',
    fontSize: 14,
    textAlign: 'center',
  },
  registerButton: {
    marginTop: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
  },
  footerText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  loginLink: {
    marginLeft: -8,
  },
});
