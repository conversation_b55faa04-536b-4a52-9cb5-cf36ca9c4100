// =====================================================
// ÉCRAN D'ACCUEIL PRINCIPAL - REDIRECTION
// =====================================================

import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import { useAuthStore } from '../store/authStore';
import Button from '../components/ui/Button';

const { width, height } = Dimensions.get('window');

export default function WelcomeScreen() {
  const { isAuthenticated, user, isLoading } = useAuthStore();

  useEffect(() => {
    // Redirection automatique si l'utilisateur est connecté
    if (isAuthenticated && user) {
      redirectToUserDashboard(user.role);
    }
  }, [isAuthenticated, user]);

  const redirectToUserDashboard = (role: string) => {
    switch (role) {
      case 'client':
        router.replace('/(client)/(tabs)/home');
        break;
      case 'deliverer':
        router.replace('/(deliverer)/(tabs)/dashboard');
        break;
      case 'merchant':
        router.replace('/(merchant)/(tabs)/orders');
        break;
      default:
        console.warn('Rôle utilisateur non reconnu:', role);
    }
  };

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  const handleRegister = () => {
    router.push('/(auth)/register');
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Chargement...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Logo et titre */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoEmoji}>🚚</Text>
          </View>
          <Text style={styles.title}>DeliveryApp</Text>
          <Text style={styles.subtitle}>Africa</Text>
          <Text style={styles.tagline}>
            Livraison rapide et fiable dans toute l'Afrique
          </Text>
        </View>

        {/* Fonctionnalités */}
        <View style={styles.features}>
          <FeatureItem
            icon="🍽️"
            title="Restaurants"
            description="Commandez vos plats préférés"
          />
          <FeatureItem
            icon="📦"
            title="Colis"
            description="Envoyez et recevez vos colis"
          />
          <FeatureItem
            icon="🛒"
            title="Courses"
            description="Faites vos courses en ligne"
          />
        </View>

        {/* Boutons d'action */}
        <View style={styles.actions}>
          <Button
            title="Se connecter"
            onPress={handleLogin}
            variant="primary"
            size="large"
            fullWidth
          />
          <Button
            title="Créer un compte"
            onPress={handleRegister}
            variant="outline"
            size="large"
            fullWidth
          />
        </View>

        {/* Informations supplémentaires */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Disponible au Sénégal, Kenya, Ghana, Nigeria et plus
          </Text>
        </View>
      </View>
    </View>
  );
}

interface FeatureItemProps {
  icon: string;
  title: string;
  description: string;
}

const FeatureItem: React.FC<FeatureItemProps> = ({ icon, title, description }) => (
  <View style={styles.featureItem}>
    <Text style={styles.featureIcon}>{icon}</Text>
    <View style={styles.featureContent}>
      <Text style={styles.featureTitle}>{title}</Text>
      <Text style={styles.featureDescription}>{description}</Text>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#007AFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#8E8E93',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  logoEmoji: {
    fontSize: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  features: {
    flex: 1,
    justifyContent: 'center',
    gap: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  featureIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 20,
  },
  actions: {
    gap: 16,
    marginBottom: 20,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 18,
  },
});
