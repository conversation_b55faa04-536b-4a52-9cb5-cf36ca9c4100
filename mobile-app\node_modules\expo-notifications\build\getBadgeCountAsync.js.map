{"version": 3, "file": "getBadgeCountAsync.js", "sourceRoot": "", "sources": ["../src/getBadgeCountAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,WAAW,MAAM,eAAe,CAAC;AAExC;;;;;GAKG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,kBAAkB;IAC9C,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;QACpC,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;IAC3E,CAAC;IAED,OAAO,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAC;AAChD,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport BadgeModule from './BadgeModule';\n\n/**\n * Fetches the number currently set as the badge of the app icon on device's home screen. A `0` value means that the badge is not displayed.\n * > **Note:** Not all Android launchers support application badges. If the launcher does not support icon badges, the method will always resolve to `0`.\n * @return Returns a Promise resolving to a number that represents the current badge of the app icon.\n * @header badge\n */\nexport default async function getBadgeCountAsync(): Promise<number> {\n  if (!BadgeModule.getBadgeCountAsync) {\n    throw new UnavailabilityError('ExpoNotifications', 'getBadgeCountAsync');\n  }\n\n  return await BadgeModule.getBadgeCountAsync();\n}\n"]}