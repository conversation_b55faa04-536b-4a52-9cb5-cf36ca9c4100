/**
 * Typography System - Optimized for African Markets
 * High readability on low-end devices and outdoor conditions
 */

export interface TypographyToken {
  fontSize: number;
  lineHeight: number;
  fontWeight: string;
  letterSpacing?: number;
  fontFamily: string;
}

// Font Families - Optimized for readability and file size
export const FontFamilies = {
  primary: 'Inter', // Main UI font - excellent readability
  secondary: 'System', // System fallback
  mono: 'SF Mono', // Code/numbers
} as const;

// Font Weights
export const FontWeights = {
  light: '300',
  normal: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800',
} as const;

// Font Sizes - Touch-optimized scale
export const FontSizes = {
  xs: 12,    // 0.75rem - Labels, captions
  sm: 14,    // 0.875rem - Secondary text
  base: 16,  // 1rem - Body text (minimum for accessibility)
  lg: 18,    // 1.125rem - Important text
  xl: 20,    // 1.25rem - Subheadings
  '2xl': 24, // 1.5rem - Section headers
  '3xl': 28, // 1.75rem - Page headers
  '4xl': 32, // 2rem - Main titles
  '5xl': 36, // 2.25rem - Hero titles
  '6xl': 48, // 3rem - Display titles
} as const;

// Line Heights - Optimized for readability
export const LineHeights = {
  tight: 1.25,    // Headlines
  normal: 1.5,    // Body text
  relaxed: 1.625, // Long form content
  loose: 2,       // Spaced content
} as const;

// Letter Spacing
export const LetterSpacing = {
  tighter: -0.05,
  tight: -0.025,
  normal: 0,
  wide: 0.025,
  wider: 0.05,
  widest: 0.1,
} as const;

// Typography Scale - Semantic naming
export const Typography = {
  // Display styles - for hero sections and major headings
  display: {
    large: {
      fontSize: FontSizes['6xl'],
      lineHeight: LineHeights.tight,
      fontWeight: FontWeights.bold,
      letterSpacing: LetterSpacing.tight,
      fontFamily: FontFamilies.primary,
    },
    medium: {
      fontSize: FontSizes['5xl'],
      lineHeight: LineHeights.tight,
      fontWeight: FontWeights.bold,
      letterSpacing: LetterSpacing.tight,
      fontFamily: FontFamilies.primary,
    },
    small: {
      fontSize: FontSizes['4xl'],
      lineHeight: LineHeights.tight,
      fontWeight: FontWeights.semibold,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.primary,
    },
  },

  // Heading styles - for section headers
  heading: {
    h1: {
      fontSize: FontSizes['3xl'],
      lineHeight: LineHeights.tight,
      fontWeight: FontWeights.bold,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.primary,
    },
    h2: {
      fontSize: FontSizes['2xl'],
      lineHeight: LineHeights.tight,
      fontWeight: FontWeights.semibold,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.primary,
    },
    h3: {
      fontSize: FontSizes.xl,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.semibold,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.primary,
    },
    h4: {
      fontSize: FontSizes.lg,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.medium,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.primary,
    },
  },

  // Body text styles
  body: {
    large: {
      fontSize: FontSizes.lg,
      lineHeight: LineHeights.relaxed,
      fontWeight: FontWeights.normal,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.primary,
    },
    medium: {
      fontSize: FontSizes.base,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.normal,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.primary,
    },
    small: {
      fontSize: FontSizes.sm,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.normal,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.primary,
    },
  },

  // Label styles - for form labels, captions
  label: {
    large: {
      fontSize: FontSizes.base,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.medium,
      letterSpacing: LetterSpacing.wide,
      fontFamily: FontFamilies.primary,
    },
    medium: {
      fontSize: FontSizes.sm,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.medium,
      letterSpacing: LetterSpacing.wide,
      fontFamily: FontFamilies.primary,
    },
    small: {
      fontSize: FontSizes.xs,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.medium,
      letterSpacing: LetterSpacing.wider,
      fontFamily: FontFamilies.primary,
    },
  },

  // Button styles - optimized for touch targets
  button: {
    large: {
      fontSize: FontSizes.lg,
      lineHeight: LineHeights.tight,
      fontWeight: FontWeights.semibold,
      letterSpacing: LetterSpacing.wide,
      fontFamily: FontFamilies.primary,
    },
    medium: {
      fontSize: FontSizes.base,
      lineHeight: LineHeights.tight,
      fontWeight: FontWeights.medium,
      letterSpacing: LetterSpacing.wide,
      fontFamily: FontFamilies.primary,
    },
    small: {
      fontSize: FontSizes.sm,
      lineHeight: LineHeights.tight,
      fontWeight: FontWeights.medium,
      letterSpacing: LetterSpacing.wide,
      fontFamily: FontFamilies.primary,
    },
  },

  // Caption styles - for metadata, timestamps
  caption: {
    large: {
      fontSize: FontSizes.sm,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.normal,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.primary,
    },
    medium: {
      fontSize: FontSizes.xs,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.normal,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.primary,
    },
  },

  // Monospace styles - for codes, numbers
  mono: {
    large: {
      fontSize: FontSizes.base,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.normal,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.mono,
    },
    medium: {
      fontSize: FontSizes.sm,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.normal,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.mono,
    },
    small: {
      fontSize: FontSizes.xs,
      lineHeight: LineHeights.normal,
      fontWeight: FontWeights.normal,
      letterSpacing: LetterSpacing.normal,
      fontFamily: FontFamilies.mono,
    },
  },
} as const;

// Utility function to get typography styles for React Native
export const getTypographyStyle = (variant: keyof typeof Typography, size: string) => {
  const style = Typography[variant]?.[size as keyof typeof Typography[typeof variant]];
  if (!style) {
    console.warn(`Typography variant ${variant}.${size} not found`);
    return Typography.body.medium;
  }
  return style;
};

// CSS-in-JS helper for web
export const createTypographyCSS = (token: TypographyToken) => ({
  fontSize: `${token.fontSize}px`,
  lineHeight: token.lineHeight,
  fontWeight: token.fontWeight,
  letterSpacing: token.letterSpacing ? `${token.letterSpacing}em` : 'normal',
  fontFamily: token.fontFamily,
});

// Responsive typography helpers
export const ResponsiveTypography = {
  // Scale down on smaller screens
  mobile: {
    scaleRatio: 0.875, // 87.5% of base size
  },
  // Scale up on larger screens
  tablet: {
    scaleRatio: 1.125, // 112.5% of base size
  },
};

// Accessibility helpers
export const AccessibilityTypography = {
  // Minimum font sizes for readability
  minimumSizes: {
    body: 14, // Never go below 14px for body text
    button: 16, // Never go below 16px for buttons
    label: 12, // Never go below 12px for labels
  },
  
  // High contrast mode adjustments
  highContrast: {
    fontWeight: FontWeights.semibold, // Increase weight for better visibility
    letterSpacing: LetterSpacing.wide, // Increase spacing for clarity
  },
};

export type TypographyVariant = keyof typeof Typography;
export type TypographySize<T extends TypographyVariant> = keyof typeof Typography[T];
