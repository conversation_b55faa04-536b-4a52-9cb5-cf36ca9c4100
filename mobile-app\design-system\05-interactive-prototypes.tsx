/**
 * Interactive Prototypes - High Fidelity User Flows
 * Demonstrating key user journeys optimized for African markets
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import { Colors } from '../constants/Colors';
import { Typography } from './02-typography-system';
import { 
  Button, 
  Input, 
  Card, 
  Avatar, 
  Badge, 
  Loading,
  Spacing,
  TouchTargets,
  BorderRadius 
} from './03-component-library';
import {
  HomeIcon,
  SearchIcon,
  RestaurantIcon,
  TruckIcon,
  MapPinIcon,
  ClockIcon,
  MobileMoneyIcon,
  CheckIcon,
  StarIcon,
  IconSizes,
} from './04-iconography-system';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// =============================================================================
// PROTOTYPE 1: CLIENT ORDER FLOW
// =============================================================================

interface OrderFlowState {
  step: 'home' | 'restaurant' | 'cart' | 'checkout' | 'payment' | 'tracking' | 'delivered';
  selectedItems: Array<{ id: string; name: string; price: number; quantity: number }>;
  orderTotal: number;
  deliveryAddress: string;
  paymentMethod: string;
  orderStatus: 'preparing' | 'pickup' | 'delivery' | 'delivered';
}

export const ClientOrderFlowPrototype: React.FC = () => {
  const [state, setState] = useState<OrderFlowState>({
    step: 'home',
    selectedItems: [],
    orderTotal: 0,
    deliveryAddress: '',
    paymentMethod: '',
    orderStatus: 'preparing',
  });

  const [fadeAnim] = useState(new Animated.Value(1));

  const animateTransition = (callback: () => void) => {
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
    
    setTimeout(callback, 150);
  };

  const addToCart = (item: { id: string; name: string; price: number }) => {
    setState(prev => ({
      ...prev,
      selectedItems: [...prev.selectedItems, { ...item, quantity: 1 }],
      orderTotal: prev.orderTotal + item.price,
    }));
  };

  const proceedToNextStep = () => {
    const stepFlow: Record<string, string> = {
      home: 'restaurant',
      restaurant: 'cart',
      cart: 'checkout',
      checkout: 'payment',
      payment: 'tracking',
      tracking: 'delivered',
    };

    animateTransition(() => {
      setState(prev => ({
        ...prev,
        step: stepFlow[prev.step] as any,
      }));
    });
  };

  const renderHomeScreen = () => (
    <View style={styles.screen}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Bonjour Amina! 👋</Text>
        <Text style={styles.headerSubtitle}>Que souhaitez-vous commander aujourd'hui?</Text>
      </View>

      <Input
        value=""
        onChangeText={() => {}}
        placeholder="Rechercher restaurants, plats..."
        leftIcon={<SearchIcon size="sm" color={Colors.light.icon} />}
        style={styles.searchInput}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.categoriesSection}>
          <Text style={styles.sectionTitle}>Catégories populaires</Text>
          <View style={styles.categoriesGrid}>
            {[
              { icon: RestaurantIcon, name: 'Restaurants', color: Colors.light.primary },
              { icon: TruckIcon, name: 'Épicerie', color: Colors.light.secondary },
              { icon: RestaurantIcon, name: 'Fast Food', color: Colors.light.warning },
              { icon: RestaurantIcon, name: 'Pharmacie', color: Colors.light.info },
            ].map((category, index) => (
              <Card
                key={index}
                onPress={proceedToNextStep}
                style={styles.categoryCard}
                variant="outlined"
              >
                <View style={styles.categoryContent}>
                  <category.icon size="lg" color={category.color} />
                  <Text style={styles.categoryName}>{category.name}</Text>
                </View>
              </Card>
            ))}
          </View>
        </View>

        <View style={styles.restaurantsSection}>
          <Text style={styles.sectionTitle}>Restaurants près de vous</Text>
          {[
            { name: 'Chez Mama Africa', cuisine: 'Cuisine locale', rating: 4.8, time: '25-35 min', image: '🍛' },
            { name: 'Pizza Express', cuisine: 'Italien', rating: 4.5, time: '20-30 min', image: '🍕' },
            { name: 'Healthy Bowls', cuisine: 'Healthy', rating: 4.7, time: '15-25 min', image: '🥗' },
          ].map((restaurant, index) => (
            <Card
              key={index}
              onPress={proceedToNextStep}
              style={styles.restaurantCard}
              variant="elevated"
            >
              <View style={styles.restaurantContent}>
                <Text style={styles.restaurantEmoji}>{restaurant.image}</Text>
                <View style={styles.restaurantInfo}>
                  <Text style={styles.restaurantName}>{restaurant.name}</Text>
                  <Text style={styles.restaurantCuisine}>{restaurant.cuisine}</Text>
                  <View style={styles.restaurantMeta}>
                    <View style={styles.ratingContainer}>
                      <StarIcon size="xs" color={Colors.light.warning} />
                      <Text style={styles.rating}>{restaurant.rating}</Text>
                    </View>
                    <View style={styles.timeContainer}>
                      <ClockIcon size="xs" color={Colors.light.icon} />
                      <Text style={styles.time}>{restaurant.time}</Text>
                    </View>
                  </View>
                </View>
              </View>
            </Card>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  const renderRestaurantScreen = () => (
    <View style={styles.screen}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Chez Mama Africa</Text>
        <Text style={styles.headerSubtitle}>Cuisine locale authentique</Text>
        <View style={styles.restaurantMeta}>
          <Badge text="4.8 ⭐" variant="warning" size="small" />
          <Badge text="25-35 min" variant="secondary" size="small" />
          <Badge text="Livraison 500 FCFA" variant="primary" size="small" />
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.menuSection}>
          <Text style={styles.sectionTitle}>Plats populaires</Text>
          {[
            { id: '1', name: 'Thieboudienne', description: 'Riz au poisson, légumes', price: 2500, image: '🍛' },
            { id: '2', name: 'Yassa Poulet', description: 'Poulet aux oignons et citron', price: 2000, image: '🍗' },
            { id: '3', name: 'Mafé', description: 'Ragoût à la pâte d\'arachide', price: 2200, image: '🥘' },
          ].map((dish) => (
            <Card
              key={dish.id}
              style={styles.dishCard}
              variant="outlined"
            >
              <View style={styles.dishContent}>
                <Text style={styles.dishEmoji}>{dish.image}</Text>
                <View style={styles.dishInfo}>
                  <Text style={styles.dishName}>{dish.name}</Text>
                  <Text style={styles.dishDescription}>{dish.description}</Text>
                  <Text style={styles.dishPrice}>{dish.price} FCFA</Text>
                </View>
                <Button
                  title="Ajouter"
                  onPress={() => addToCart(dish)}
                  size="small"
                  variant="primary"
                />
              </View>
            </Card>
          ))}
        </View>
      </ScrollView>

      {state.selectedItems.length > 0 && (
        <View style={styles.cartSummary}>
          <View style={styles.cartInfo}>
            <Text style={styles.cartItems}>{state.selectedItems.length} article(s)</Text>
            <Text style={styles.cartTotal}>{state.orderTotal} FCFA</Text>
          </View>
          <Button
            title="Voir panier"
            onPress={proceedToNextStep}
            variant="primary"
            style={styles.cartButton}
          />
        </View>
      )}
    </View>
  );

  const renderPaymentScreen = () => (
    <View style={styles.screen}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Paiement</Text>
        <Text style={styles.headerSubtitle}>Choisissez votre mode de paiement</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.paymentSection}>
          <Text style={styles.sectionTitle}>Modes de paiement</Text>
          
          {[
            { 
              id: 'orange_money', 
              name: 'Orange Money', 
              description: 'Paiement sécurisé via Orange Money',
              icon: MobileMoneyIcon,
              color: Colors.light.warning,
              popular: true
            },
            { 
              id: 'wave', 
              name: 'Wave', 
              description: 'Paiement rapide avec Wave',
              icon: MobileMoneyIcon,
              color: Colors.light.info,
              popular: false
            },
            { 
              id: 'cash', 
              name: 'Espèces', 
              description: 'Paiement à la livraison',
              icon: MobileMoneyIcon,
              color: Colors.light.secondary,
              popular: false
            },
          ].map((method) => (
            <Card
              key={method.id}
              onPress={() => setState(prev => ({ ...prev, paymentMethod: method.id }))}
              style={[
                styles.paymentCard,
                state.paymentMethod === method.id && styles.paymentCardSelected
              ]}
              variant="outlined"
            >
              <View style={styles.paymentContent}>
                <method.icon size="md" color={method.color} />
                <View style={styles.paymentInfo}>
                  <View style={styles.paymentHeader}>
                    <Text style={styles.paymentName}>{method.name}</Text>
                    {method.popular && <Badge text="Populaire" variant="primary" size="small" />}
                  </View>
                  <Text style={styles.paymentDescription}>{method.description}</Text>
                </View>
                {state.paymentMethod === method.id && (
                  <CheckIcon size="sm" color={Colors.light.success} />
                )}
              </View>
            </Card>
          ))}
        </View>

        <View style={styles.orderSummary}>
          <Text style={styles.sectionTitle}>Résumé de la commande</Text>
          <Card style={styles.summaryCard} variant="elevated">
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Sous-total</Text>
              <Text style={styles.summaryValue}>{state.orderTotal} FCFA</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Livraison</Text>
              <Text style={styles.summaryValue}>500 FCFA</Text>
            </View>
            <View style={[styles.summaryRow, styles.summaryTotal]}>
              <Text style={styles.summaryTotalLabel}>Total</Text>
              <Text style={styles.summaryTotalValue}>{state.orderTotal + 500} FCFA</Text>
            </View>
          </Card>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title={`Payer ${state.orderTotal + 500} FCFA`}
          onPress={proceedToNextStep}
          variant="primary"
          fullWidth
          disabled={!state.paymentMethod}
        />
      </View>
    </View>
  );

  const renderTrackingScreen = () => (
    <View style={styles.screen}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Suivi de commande</Text>
        <Text style={styles.headerSubtitle}>Commande #12345</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.trackingCard} variant="elevated">
          <View style={styles.trackingHeader}>
            <Text style={styles.trackingStatus}>En préparation</Text>
            <Text style={styles.trackingTime}>Temps estimé: 25 min</Text>
          </View>
          
          <View style={styles.trackingSteps}>
            {[
              { step: 'Commande confirmée', completed: true, time: '14:30' },
              { step: 'En préparation', completed: true, time: '14:35', current: true },
              { step: 'Prêt pour livraison', completed: false, time: '14:50' },
              { step: 'En livraison', completed: false, time: '15:00' },
              { step: 'Livré', completed: false, time: '15:15' },
            ].map((item, index) => (
              <View key={index} style={styles.trackingStep}>
                <View style={[
                  styles.trackingDot,
                  item.completed && styles.trackingDotCompleted,
                  item.current && styles.trackingDotCurrent,
                ]} />
                <View style={styles.trackingStepContent}>
                  <Text style={[
                    styles.trackingStepText,
                    item.current && styles.trackingStepTextCurrent,
                  ]}>
                    {item.step}
                  </Text>
                  <Text style={styles.trackingStepTime}>{item.time}</Text>
                </View>
              </View>
            ))}
          </View>
        </Card>

        <Card style={styles.deliveryCard} variant="outlined">
          <View style={styles.deliveryHeader}>
            <Text style={styles.deliveryTitle}>Votre livreur</Text>
          </View>
          <View style={styles.deliveryContent}>
            <Avatar name="Mamadou Diallo" size="medium" />
            <View style={styles.deliveryInfo}>
              <Text style={styles.deliveryName}>Mamadou Diallo</Text>
              <View style={styles.deliveryMeta}>
                <StarIcon size="xs" color={Colors.light.warning} />
                <Text style={styles.deliveryRating}>4.9</Text>
                <Text style={styles.deliveryVehicle}>• Moto</Text>
              </View>
            </View>
            <Button
              title="Appeler"
              onPress={() => Alert.alert('Appel', 'Appel en cours...')}
              size="small"
              variant="outline"
            />
          </View>
        </Card>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title="Simuler livraison"
          onPress={proceedToNextStep}
          variant="secondary"
          fullWidth
        />
      </View>
    </View>
  );

  const getCurrentScreen = () => {
    switch (state.step) {
      case 'home':
        return renderHomeScreen();
      case 'restaurant':
        return renderRestaurantScreen();
      case 'payment':
        return renderPaymentScreen();
      case 'tracking':
        return renderTrackingScreen();
      default:
        return renderHomeScreen();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.light.background} />
      <Animated.View style={[styles.animatedContainer, { opacity: fadeAnim }]}>
        {getCurrentScreen()}
      </Animated.View>
    </SafeAreaView>
  );
};

// =============================================================================
// STYLES
// =============================================================================

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  animatedContainer: {
    flex: 1,
  },
  screen: {
    flex: 1,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  headerTitle: {
    fontSize: Typography.heading.h2.fontSize,
    fontWeight: Typography.heading.h2.fontWeight,
    color: Colors.light.text,
    marginBottom: Spacing.xs,
  },
  headerSubtitle: {
    fontSize: Typography.body.medium.fontSize,
    color: Colors.light.textSecondary,
    marginBottom: Spacing.md,
  },
  searchInput: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  categoriesSection: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.heading.h4.fontSize,
    fontWeight: Typography.heading.h4.fontWeight,
    color: Colors.light.text,
    marginBottom: Spacing.md,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryCard: {
    width: (screenWidth - Spacing.lg * 2 - Spacing.md) / 2,
    marginBottom: Spacing.md,
    aspectRatio: 1,
  },
  categoryContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryName: {
    fontSize: Typography.label.medium.fontSize,
    fontWeight: Typography.label.medium.fontWeight,
    color: Colors.light.text,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
  restaurantsSection: {
    marginBottom: Spacing.xl,
  },
  restaurantCard: {
    marginBottom: Spacing.md,
  },
  restaurantContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  restaurantEmoji: {
    fontSize: 40,
    marginRight: Spacing.md,
  },
  restaurantInfo: {
    flex: 1,
  },
  restaurantName: {
    fontSize: Typography.body.large.fontSize,
    fontWeight: Typography.body.large.fontWeight,
    color: Colors.light.text,
    marginBottom: Spacing.xs,
  },
  restaurantCuisine: {
    fontSize: Typography.body.small.fontSize,
    color: Colors.light.textSecondary,
    marginBottom: Spacing.sm,
  },
  restaurantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  rating: {
    fontSize: Typography.caption.medium.fontSize,
    color: Colors.light.textSecondary,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  time: {
    fontSize: Typography.caption.medium.fontSize,
    color: Colors.light.textSecondary,
  },
  menuSection: {
    marginBottom: Spacing.xl,
  },
  dishCard: {
    marginBottom: Spacing.md,
  },
  dishContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dishEmoji: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  dishInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },
  dishName: {
    fontSize: Typography.body.medium.fontSize,
    fontWeight: Typography.body.medium.fontWeight,
    color: Colors.light.text,
    marginBottom: Spacing.xs,
  },
  dishDescription: {
    fontSize: Typography.body.small.fontSize,
    color: Colors.light.textSecondary,
    marginBottom: Spacing.xs,
  },
  dishPrice: {
    fontSize: Typography.label.medium.fontSize,
    fontWeight: Typography.label.medium.fontWeight,
    color: Colors.light.primary,
  },
  cartSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Spacing.lg,
    backgroundColor: Colors.light.backgroundSecondary,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  cartInfo: {
    flex: 1,
  },
  cartItems: {
    fontSize: Typography.body.small.fontSize,
    color: Colors.light.textSecondary,
  },
  cartTotal: {
    fontSize: Typography.heading.h4.fontSize,
    fontWeight: Typography.heading.h4.fontWeight,
    color: Colors.light.text,
  },
  cartButton: {
    marginLeft: Spacing.md,
  },
  paymentSection: {
    marginBottom: Spacing.xl,
  },
  paymentCard: {
    marginBottom: Spacing.md,
  },
  paymentCardSelected: {
    borderColor: Colors.light.primary,
    borderWidth: 2,
  },
  paymentContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentInfo: {
    flex: 1,
    marginLeft: Spacing.md,
  },
  paymentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    marginBottom: Spacing.xs,
  },
  paymentName: {
    fontSize: Typography.body.medium.fontSize,
    fontWeight: Typography.body.medium.fontWeight,
    color: Colors.light.text,
  },
  paymentDescription: {
    fontSize: Typography.body.small.fontSize,
    color: Colors.light.textSecondary,
  },
  orderSummary: {
    marginBottom: Spacing.xl,
  },
  summaryCard: {
    padding: Spacing.lg,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  summaryLabel: {
    fontSize: Typography.body.medium.fontSize,
    color: Colors.light.textSecondary,
  },
  summaryValue: {
    fontSize: Typography.body.medium.fontSize,
    color: Colors.light.text,
  },
  summaryTotal: {
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
    marginBottom: 0,
  },
  summaryTotalLabel: {
    fontSize: Typography.body.large.fontSize,
    fontWeight: Typography.body.large.fontWeight,
    color: Colors.light.text,
  },
  summaryTotalValue: {
    fontSize: Typography.body.large.fontSize,
    fontWeight: Typography.body.large.fontWeight,
    color: Colors.light.primary,
  },
  footer: {
    padding: Spacing.lg,
    backgroundColor: Colors.light.background,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  trackingCard: {
    marginBottom: Spacing.lg,
  },
  trackingHeader: {
    marginBottom: Spacing.lg,
  },
  trackingStatus: {
    fontSize: Typography.heading.h3.fontSize,
    fontWeight: Typography.heading.h3.fontWeight,
    color: Colors.light.primary,
    marginBottom: Spacing.xs,
  },
  trackingTime: {
    fontSize: Typography.body.medium.fontSize,
    color: Colors.light.textSecondary,
  },
  trackingSteps: {
    paddingLeft: Spacing.md,
  },
  trackingStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.lg,
  },
  trackingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.light.border,
    marginRight: Spacing.md,
    marginTop: 4,
  },
  trackingDotCompleted: {
    backgroundColor: Colors.light.success,
  },
  trackingDotCurrent: {
    backgroundColor: Colors.light.primary,
  },
  trackingStepContent: {
    flex: 1,
  },
  trackingStepText: {
    fontSize: Typography.body.medium.fontSize,
    color: Colors.light.textSecondary,
    marginBottom: Spacing.xs,
  },
  trackingStepTextCurrent: {
    color: Colors.light.text,
    fontWeight: Typography.body.medium.fontWeight,
  },
  trackingStepTime: {
    fontSize: Typography.caption.medium.fontSize,
    color: Colors.light.textTertiary,
  },
  deliveryCard: {
    marginBottom: Spacing.lg,
  },
  deliveryHeader: {
    marginBottom: Spacing.md,
  },
  deliveryTitle: {
    fontSize: Typography.heading.h4.fontSize,
    fontWeight: Typography.heading.h4.fontWeight,
    color: Colors.light.text,
  },
  deliveryContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryInfo: {
    flex: 1,
    marginLeft: Spacing.md,
  },
  deliveryName: {
    fontSize: Typography.body.medium.fontSize,
    fontWeight: Typography.body.medium.fontWeight,
    color: Colors.light.text,
    marginBottom: Spacing.xs,
  },
  deliveryMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  deliveryRating: {
    fontSize: Typography.caption.medium.fontSize,
    color: Colors.light.textSecondary,
  },
  deliveryVehicle: {
    fontSize: Typography.caption.medium.fontSize,
    color: Colors.light.textSecondary,
  },
});

export default ClientOrderFlowPrototype;
