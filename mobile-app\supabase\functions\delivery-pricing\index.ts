import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface PricingRequest {
  pickup_location: {
    latitude: number
    longitude: number
  }
  delivery_location: {
    latitude: number
    longitude: number
  }
  delivery_type: 'food' | 'package' | 'grocery' | 'pharmacy' | 'wholesale' | 'documents'
  order_value?: number
  weight_kg?: number
  is_urgent?: boolean
  merchant_id?: string
  customer_id: string
}

interface PricingResponse {
  base_fee: number
  distance_fee: number
  weight_fee: number
  urgency_fee: number
  service_fee: number
  total_fee: number
  estimated_time_minutes: number
  currency_code: string
  breakdown: {
    distance_km: number
    base_rate: number
    per_km_rate: number
    weight_rate?: number
    urgency_multiplier?: number
  }
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { data: requestData, error: parseError } = await req.json() as { data: PricingRequest, error: any }
    
    if (parseError) {
      throw new Error('Invalid request format')
    }

    const {
      pickup_location,
      delivery_location,
      delivery_type,
      order_value = 0,
      weight_kg = 1,
      is_urgent = false,
      merchant_id,
      customer_id
    } = requestData

    // Validate input
    if (!pickup_location || !delivery_location || !customer_id) {
      throw new Error('Missing required fields')
    }

    // Get user's country and currency
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select(`
        country_id,
        countries (
          currency_code,
          currency_symbol
        )
      `)
      .eq('id', customer_id)
      .single()

    if (profileError) {
      throw new Error('User not found')
    }

    const currency_code = userProfile.countries.currency_code
    
    // Calculate distance using Haversine formula
    const distance_km = calculateDistance(
      pickup_location.latitude,
      pickup_location.longitude,
      delivery_location.latitude,
      delivery_location.longitude
    )

    // Get delivery zone information
    const { data: deliveryZone } = await supabase
      .rpc('get_delivery_zone_for_point', {
        point_lat: delivery_location.latitude,
        point_lng: delivery_location.longitude
      })

    // Get pricing configuration for the country and delivery type
    const { data: pricingConfig, error: configError } = await supabase
      .from('delivery_pricing_config')
      .select('*')
      .eq('country_id', userProfile.country_id)
      .eq('delivery_type', delivery_type)
      .eq('is_active', true)
      .single()

    // Default pricing if no config found
    const config = pricingConfig || getDefaultPricing(delivery_type, currency_code)

    // Calculate fees
    const base_fee = deliveryZone?.base_delivery_fee || config.base_fee
    const distance_fee = Math.max(0, (distance_km - config.free_distance_km)) * config.per_km_rate
    const weight_fee = weight_kg > config.free_weight_kg ? 
      (weight_kg - config.free_weight_kg) * config.per_kg_rate : 0
    const urgency_fee = is_urgent ? base_fee * config.urgency_multiplier : 0
    
    // Service fee (percentage of order value)
    const service_fee = order_value * config.service_fee_percentage

    // Apply merchant-specific discounts if applicable
    let merchant_discount = 0
    if (merchant_id) {
      const { data: merchant } = await supabase
        .from('merchants')
        .select('delivery_fee')
        .eq('id', merchant_id)
        .single()
      
      if (merchant && merchant.delivery_fee < base_fee) {
        merchant_discount = base_fee - merchant.delivery_fee
      }
    }

    const total_fee = Math.max(0, base_fee + distance_fee + weight_fee + urgency_fee + service_fee - merchant_discount)

    // Calculate estimated delivery time
    const base_time = deliveryZone?.estimated_delivery_time || 30
    const distance_time = distance_km * config.minutes_per_km
    const urgency_reduction = is_urgent ? -10 : 0
    const estimated_time_minutes = Math.max(15, base_time + distance_time + urgency_reduction)

    // Log pricing calculation for analytics
    await supabase
      .from('pricing_calculations')
      .insert({
        customer_id,
        merchant_id,
        delivery_type,
        distance_km,
        weight_kg,
        is_urgent,
        base_fee,
        distance_fee,
        weight_fee,
        urgency_fee,
        service_fee,
        total_fee,
        estimated_time_minutes,
        currency_code
      })

    const response: PricingResponse = {
      base_fee: Math.round(base_fee),
      distance_fee: Math.round(distance_fee),
      weight_fee: Math.round(weight_fee),
      urgency_fee: Math.round(urgency_fee),
      service_fee: Math.round(service_fee),
      total_fee: Math.round(total_fee),
      estimated_time_minutes: Math.round(estimated_time_minutes),
      currency_code,
      breakdown: {
        distance_km: Math.round(distance_km * 100) / 100,
        base_rate: config.base_fee,
        per_km_rate: config.per_km_rate,
        weight_rate: config.per_kg_rate,
        urgency_multiplier: config.urgency_multiplier
      }
    }

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Pricing calculation error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error',
        code: 'PRICING_ERROR'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

// Haversine formula to calculate distance between two points
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371 // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1)
  const dLon = toRadians(lon2 - lon1)
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2)
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}

// Default pricing configuration
function getDefaultPricing(delivery_type: string, currency_code: string) {
  const baseRates = {
    'XOF': { base: 500, per_km: 100, per_kg: 50 }, // West Africa CFA
    'KES': { base: 50, per_km: 10, per_kg: 5 },    // Kenya Shilling
    'NGN': { base: 200, per_km: 50, per_kg: 20 },  // Nigerian Naira
    'GHS': { base: 5, per_km: 1, per_kg: 0.5 },    // Ghana Cedi
    'ZAR': { base: 25, per_km: 5, per_kg: 2 },     // South African Rand
  }

  const rates = baseRates[currency_code] || baseRates['XOF']
  
  const typeMultipliers = {
    'food': 1.0,
    'package': 1.2,
    'grocery': 1.1,
    'pharmacy': 1.3,
    'wholesale': 1.5,
    'documents': 0.8
  }

  const multiplier = typeMultipliers[delivery_type] || 1.0

  return {
    base_fee: rates.base * multiplier,
    per_km_rate: rates.per_km * multiplier,
    per_kg_rate: rates.per_kg,
    free_distance_km: 2,
    free_weight_kg: 2,
    urgency_multiplier: 0.5,
    service_fee_percentage: 0.02, // 2%
    minutes_per_km: 3
  }
}
