{"version": 3, "file": "PictureRef.js", "sourceRoot": "", "sources": ["../src/PictureRef.ts"], "names": [], "mappings": "AAGA,OAAO,aAAa,MAAM,qBAAqB,CAAC;AAuBhD,eAAe,aAAa,CAAC,OAA4B,CAAC", "sourcesContent": ["import { SharedRef } from 'expo';\n\nimport type { PhotoResult, SavePictureOptions } from './Camera.types';\nimport CameraManager from './ExpoCameraManager';\n\n/**\n * A reference to a native instance of the image.\n */\nexport declare class PictureRef extends SharedRef<'image'> {\n  /**\n   * Width of the image.\n   */\n  width: number;\n\n  /**\n   * Height of the image.\n   */\n  height: number;\n\n  /**\n   * Saves the image to the file system in the cache directory.\n   * @param options A map defining how modified image should be saved.\n   */\n  savePictureAsync(options?: SavePictureOptions): Promise<PhotoResult>;\n}\n\nexport default CameraManager.Picture as typeof PictureRef;\n"]}