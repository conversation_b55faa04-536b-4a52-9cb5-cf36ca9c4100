{"version": 3, "file": "ServerRegistrationModule.native.js", "sourceRoot": "", "sources": ["../src/ServerRegistrationModule.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAChC,uCAAuC,CACxC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { ServerRegistrationModule } from './ServerRegistrationModule.types';\n\nexport default requireNativeModule<ServerRegistrationModule>(\n  'NotificationsServerRegistrationModule'\n);\n"]}