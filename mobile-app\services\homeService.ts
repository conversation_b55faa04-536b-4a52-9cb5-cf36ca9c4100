// =====================================================
// HOME SCREEN DATA SERVICE - REACT NATIVE COMPATIBLE
// =====================================================

import { supabase } from '../lib/supabase';

export interface Category {
  id: string;
  name: string;
  emoji: string | null;
  color: string | null;
  count: number;
}

export interface Merchant {
  id: string;
  name: string;
  image_url: string | null;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  category: string | null;
  isPromoted: boolean;
  description: string | null;
  address: string | null;
  is_open: boolean;
}

export interface Promotion {
  id: string;
  title: string;
  description: string | null;
  image_url: string | null;
  discount: string;
  merchantId: string | null;
  merchant_name?: string;
}

export class HomeService {
  /**
   * Fetch all active categories with merchant count
   */
  static async getCategories(): Promise<Category[]> {
    try {
      // First get categories
      const { data: categories, error: categoriesError } = await supabase
        .from('categories')
        .select('id, name, emoji, color')
        .eq('is_active', true)
        .order('sort_order');

      if (categoriesError) throw categoriesError;

      // Then get merchant counts for each category
      const categoriesWithCounts = await Promise.all(
        (categories || []).map(async (category) => {
          const { count } = await supabase
            .from('merchants')
            .select('*', { count: 'exact', head: true })
            .eq('category_id', category.id)
            .eq('is_verified', true);

          return {
            id: category.id,
            name: category.name,
            emoji: category.emoji,
            color: category.color,
            count: count || 0,
          };
        })
      );

      return categoriesWithCounts;
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Return fallback data on error
      return [
        { id: '1', name: 'Restaurants', emoji: '🍽️', color: '#FF6B6B', count: 5 },
        { id: '2', name: 'Pharmacie', emoji: '💊', color: '#4ECDC4', count: 3 },
        { id: '3', name: 'Épicerie', emoji: '🛒', color: '#45B7D1', count: 4 },
        { id: '4', name: 'Beauté', emoji: '💄', color: '#F7DC6F', count: 2 },
      ];
    }
  }

  /**
   * Fetch active promotions with merchant info
   */
  static async getPromotions(): Promise<Promotion[]> {
    try {
      const { data: promotions, error } = await supabase
        .from('promotions')
        .select('id, title, description, image_url, discount_type, discount_value, merchant_id')
        .eq('is_active', true)
        .gte('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      // Get merchant names separately to avoid join issues
      const promotionsWithMerchants = await Promise.all(
        (promotions || []).map(async (promo) => {
          let merchantName = '';
          if (promo.merchant_id) {
            const { data: merchant } = await supabase
              .from('merchants')
              .select('name')
              .eq('id', promo.merchant_id)
              .single();
            merchantName = merchant?.name || '';
          }

          return {
            id: promo.id,
            title: promo.title,
            description: promo.description,
            image_url: promo.image_url,
            discount: this.formatDiscount(promo.discount_type, promo.discount_value),
            merchantId: promo.merchant_id,
            merchant_name: merchantName,
          };
        })
      );

      return promotionsWithMerchants;
    } catch (error) {
      console.error('Error fetching promotions:', error);
      // Return fallback promotions
      return [
        {
          id: '1',
          title: 'Livraison Gratuite',
          description: 'Sur votre première commande',
          image_url: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400',
          discount: 'Gratuit',
          merchantId: '1',
          merchant_name: 'Chez Fatou Restaurant',
        },
        {
          id: '2',
          title: 'Menu du Jour -20%',
          description: 'Réduction sur tous les plats',
          image_url: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400',
          discount: '-20%',
          merchantId: '2',
          merchant_name: 'Le Lagon Restaurant',
        },
      ];
    }
  }

  /**
   * Fetch nearby merchants (for now, just verified and open merchants)
   */
  static async getNearbyMerchants(limit: number = 10): Promise<Merchant[]> {
    try {
      const { data: merchants, error } = await supabase
        .from('merchants')
        .select(`
          id,
          name,
          image_url,
          rating,
          delivery_time_min,
          delivery_time_max,
          delivery_fee,
          is_promoted,
          description,
          address,
          is_open,
          category_id
        `)
        .eq('is_verified', true)
        .eq('is_open', true)
        .order('is_promoted', { ascending: false })
        .order('rating', { ascending: false })
        .limit(limit);

      if (error) throw error;

      // Get category names separately
      const merchantsWithCategories = await Promise.all(
        (merchants || []).map(async (merchant) => {
          let categoryName = '';
          if (merchant.category_id) {
            const { data: category } = await supabase
              .from('categories')
              .select('name')
              .eq('id', merchant.category_id)
              .single();
            categoryName = category?.name || '';
          }

          return {
            id: merchant.id,
            name: merchant.name,
            image_url: merchant.image_url,
            rating: merchant.rating,
            deliveryTime: `${merchant.delivery_time_min}-${merchant.delivery_time_max} min`,
            deliveryFee: merchant.delivery_fee,
            category: categoryName,
            isPromoted: merchant.is_promoted,
            description: merchant.description,
            address: merchant.address,
            is_open: merchant.is_open,
          };
        })
      );

      return merchantsWithCategories;
    } catch (error) {
      console.error('Error fetching merchants:', error);
      // Return fallback merchants
      return [
        {
          id: '1',
          name: 'Chez Fatou Restaurant',
          image_url: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400',
          rating: 4.8,
          deliveryTime: '25-35 min',
          deliveryFee: 500,
          category: 'Restaurants',
          isPromoted: true,
          description: 'Cuisine sénégalaise authentique',
          address: 'Plateau, Dakar',
          is_open: true,
        },
        {
          id: '2',
          name: 'Pharmacie du Plateau',
          image_url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400',
          rating: 4.6,
          deliveryTime: '15-25 min',
          deliveryFee: 300,
          category: 'Pharmacie',
          isPromoted: false,
          description: 'Service 24h/24',
          address: 'Avenue Senghor, Dakar',
          is_open: true,
        },
      ];
    }
  }

  /**
   * Search merchants by category
   */
  static async getMerchantsByCategory(categoryId: string): Promise<Merchant[]> {
    try {
      const { data: merchants, error } = await supabase
        .from('merchants')
        .select(`
          id,
          name,
          image_url,
          rating,
          delivery_time_min,
          delivery_time_max,
          delivery_fee,
          is_promoted,
          description,
          address,
          is_open,
          categories(name)
        `)
        .eq('category_id', categoryId)
        .eq('is_verified', true)
        .order('is_promoted', { ascending: false })
        .order('rating', { ascending: false });

      if (error) throw error;

      return merchants?.map(merchant => ({
        id: merchant.id,
        name: merchant.name,
        image_url: merchant.image_url,
        rating: merchant.rating,
        deliveryTime: `${merchant.delivery_time_min}-${merchant.delivery_time_max} min`,
        deliveryFee: merchant.delivery_fee,
        category: merchant.categories?.name,
        isPromoted: merchant.is_promoted,
        description: merchant.description,
        address: merchant.address,
        is_open: merchant.is_open,
      })) || [];
    } catch (error) {
      console.error('Error fetching merchants by category:', error);
      return [];
    }
  }

  /**
   * Format discount for display
   */
  private static formatDiscount(type: string, value: number): string {
    switch (type) {
      case 'percentage':
        return `-${value}%`;
      case 'fixed_amount':
        return `-${value} FCFA`;
      case 'free_delivery':
        return 'Livraison gratuite';
      default:
        return `-${value}%`;
    }
  }

  /**
   * Get merchant details by ID
   */
  static async getMerchantById(merchantId: string) {
    try {
      const { data: merchant, error } = await supabase
        .from('merchants')
        .select(`
          *,
          categories(name, emoji),
          products(
            id,
            name,
            description,
            price,
            image_url,
            is_available,
            is_featured,
            preparation_time
          )
        `)
        .eq('id', merchantId)
        .single();

      if (error) throw error;
      return merchant;
    } catch (error) {
      console.error('Error fetching merchant details:', error);
      return null;
    }
  }
}
