// =====================================================
// HOME SCREEN DATA SERVICE
// =====================================================

import { supabase } from '../lib/supabase';

export interface Category {
  id: string;
  name: string;
  emoji: string | null;
  color: string | null;
  count: number;
}

export interface Merchant {
  id: string;
  name: string;
  image_url: string | null;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  category: string | null;
  isPromoted: boolean;
  description: string | null;
  address: string | null;
  is_open: boolean;
}

export interface Promotion {
  id: string;
  title: string;
  description: string | null;
  image_url: string | null;
  discount: string;
  merchantId: string | null;
  merchant_name?: string;
}

export class HomeService {
  /**
   * Fetch all active categories with merchant count
   */
  static async getCategories(): Promise<Category[]> {
    try {
      const { data: categories, error } = await supabase
        .from('categories')
        .select(`
          id,
          name,
          emoji,
          color,
          merchants!inner(id)
        `)
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;

      return categories?.map(category => ({
        id: category.id,
        name: category.name,
        emoji: category.emoji,
        color: category.color,
        count: category.merchants?.length || 0,
      })) || [];
    } catch (error) {
      console.error('Error fetching categories:', error);
      return [];
    }
  }

  /**
   * Fetch active promotions with merchant info
   */
  static async getPromotions(): Promise<Promotion[]> {
    try {
      const { data: promotions, error } = await supabase
        .from('promotions')
        .select(`
          id,
          title,
          description,
          image_url,
          discount_type,
          discount_value,
          merchant_id,
          merchants(name)
        `)
        .eq('is_active', true)
        .gte('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      return promotions?.map(promo => ({
        id: promo.id,
        title: promo.title,
        description: promo.description,
        image_url: promo.image_url,
        discount: this.formatDiscount(promo.discount_type, promo.discount_value),
        merchantId: promo.merchant_id,
        merchant_name: promo.merchants?.name,
      })) || [];
    } catch (error) {
      console.error('Error fetching promotions:', error);
      return [];
    }
  }

  /**
   * Fetch nearby merchants (for now, just verified and open merchants)
   */
  static async getNearbyMerchants(limit: number = 10): Promise<Merchant[]> {
    try {
      const { data: merchants, error } = await supabase
        .from('merchants')
        .select(`
          id,
          name,
          image_url,
          rating,
          delivery_time_min,
          delivery_time_max,
          delivery_fee,
          is_promoted,
          description,
          address,
          is_open,
          categories(name)
        `)
        .eq('is_verified', true)
        .eq('is_open', true)
        .order('is_promoted', { ascending: false })
        .order('rating', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return merchants?.map(merchant => ({
        id: merchant.id,
        name: merchant.name,
        image_url: merchant.image_url,
        rating: merchant.rating,
        deliveryTime: `${merchant.delivery_time_min}-${merchant.delivery_time_max} min`,
        deliveryFee: merchant.delivery_fee,
        category: merchant.categories?.name,
        isPromoted: merchant.is_promoted,
        description: merchant.description,
        address: merchant.address,
        is_open: merchant.is_open,
      })) || [];
    } catch (error) {
      console.error('Error fetching merchants:', error);
      return [];
    }
  }

  /**
   * Search merchants by category
   */
  static async getMerchantsByCategory(categoryId: string): Promise<Merchant[]> {
    try {
      const { data: merchants, error } = await supabase
        .from('merchants')
        .select(`
          id,
          name,
          image_url,
          rating,
          delivery_time_min,
          delivery_time_max,
          delivery_fee,
          is_promoted,
          description,
          address,
          is_open,
          categories(name)
        `)
        .eq('category_id', categoryId)
        .eq('is_verified', true)
        .order('is_promoted', { ascending: false })
        .order('rating', { ascending: false });

      if (error) throw error;

      return merchants?.map(merchant => ({
        id: merchant.id,
        name: merchant.name,
        image_url: merchant.image_url,
        rating: merchant.rating,
        deliveryTime: `${merchant.delivery_time_min}-${merchant.delivery_time_max} min`,
        deliveryFee: merchant.delivery_fee,
        category: merchant.categories?.name,
        isPromoted: merchant.is_promoted,
        description: merchant.description,
        address: merchant.address,
        is_open: merchant.is_open,
      })) || [];
    } catch (error) {
      console.error('Error fetching merchants by category:', error);
      return [];
    }
  }

  /**
   * Format discount for display
   */
  private static formatDiscount(type: string, value: number): string {
    switch (type) {
      case 'percentage':
        return `-${value}%`;
      case 'fixed_amount':
        return `-${value} FCFA`;
      case 'free_delivery':
        return 'Livraison gratuite';
      default:
        return `-${value}%`;
    }
  }

  /**
   * Get merchant details by ID
   */
  static async getMerchantById(merchantId: string) {
    try {
      const { data: merchant, error } = await supabase
        .from('merchants')
        .select(`
          *,
          categories(name, emoji),
          products(
            id,
            name,
            description,
            price,
            image_url,
            is_available,
            is_featured,
            preparation_time
          )
        `)
        .eq('id', merchantId)
        .single();

      if (error) throw error;
      return merchant;
    } catch (error) {
      console.error('Error fetching merchant details:', error);
      return null;
    }
  }
}
