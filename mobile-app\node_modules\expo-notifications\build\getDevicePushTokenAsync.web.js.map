{"version": 3, "file": "getDevicePushTokenAsync.web.js", "sourceRoot": "", "sources": ["../src/getDevicePushTokenAsync.web.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,gBAAgB,CAAC;AACvC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AACzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AAIlD,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,uBAAuB;IACnD,MAAM,IAAI,GAAG,MAAM,wCAAwC,EAAE,CAAC;IAC9D,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;IACxE,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;AACrC,CAAC;AAED,SAAS,eAAe;IACtB,IAAI,CAAC,CAAC,cAAc,IAAI,MAAM,CAAC,EAAE,CAAC;QAChC,MAAM,IAAI,UAAU,CAClB,iBAAiB,EACjB,4DAA4D,CAC7D,CAAC;IACJ,CAAC;IACD,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;QAC7B,MAAM,IAAI,UAAU,CAClB,iBAAiB,EACjB,mKAAmK,CACpK,CAAC;IACJ,CAAC;IACD,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;QAC1C,MAAM,IAAI,UAAU,CAClB,qCAAqC,EACrC,wGAAwG,CACzG,CAAC;IACJ,CAAC;AACH,CAAC;AAED,KAAK,UAAU,wCAAwC;IACrD,4CAA4C;IAC5C,MAAM,cAAc,GAAkB,SAAS,CAAC,UAAU,EAAE,YAAY,EAAE,cAAc,CAAC;IACzF,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,MAAM,IAAI,UAAU,CAClB,2CAA2C,EAC3C,uKAAuK,CACxK,CAAC;IACJ,CAAC;IAED,4CAA4C;IAC5C,MAAM,iBAAiB,GAAG,SAAS,CAAC,UAAU,EAAE,YAAY,EAAE,iBAAiB,CAAC;IAChF,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,MAAM,IAAI,UAAU,CAClB,8CAA8C,EAC9C,qLAAqL,CACtL,CAAC;IACJ,CAAC;IACD,eAAe,EAAE,CAAC;IAElB,IAAI,YAAY,GAAqC,IAAI,CAAC;IAC1D,IAAI,CAAC;QACH,YAAY,GAAG,MAAM,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;IAC3E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,UAAU,CAClB,4CAA4C,EAC5C,qFAAqF,iBAAiB,8BAA8B,KAAK,EAAE,CAC5I,CAAC;IACJ,CAAC;IACD,MAAM,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;IAEpC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QACzB,MAAM,IAAI,UAAU,CAClB,4CAA4C,EAC5C,iGAAiG,CAClG,CAAC;IACJ,CAAC;IAED,MAAM,gBAAgB,GAAG;QACvB,eAAe,EAAE,IAAI;QACrB,oBAAoB,EAAE,sBAAsB,CAAC,cAAc,CAAC;KAC7D,CAAC;IACF,IAAI,gBAAgB,GAA4B,IAAI,CAAC;IACrD,IAAI,CAAC;QACH,gBAAgB,GAAG,MAAM,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;IAChF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,UAAU,CAClB,4CAA4C,EAC5C,yFAAyF;YACvF,KAAK;YACL,GAAG,CACN,CAAC;IACJ,CAAC;IACD,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC;IAEvD,MAAM,kBAAkB,GAAG;QACzB,QAAQ,EAAE,oBAAoB,CAAC,QAAQ;QACvC,IAAI,EAAE;YACJ,MAAM,EAAE,oBAAoB,CAAC,IAAK,CAAC,MAAM;YACzC,IAAI,EAAE,oBAAoB,CAAC,IAAK,CAAC,IAAI;SACtC;KACF,CAAC;IAEF,oDAAoD;IACpD,yDAAyD;IACzD,iEAAiE;IACjE,wDAAwD;IACxD,+CAA+C;IAC/C,MAAM,gBAAgB,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,YAAY,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC;IACzE,MAAM,YAAY,CAAC,MAAM,CAAC,WAAW,CACnC,IAAI,CAAC,SAAS,CAAC,EAAE,iBAAiB,EAAE,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAC5D,CAAC;IAEF,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED,qFAAqF;AACrF,SAAS,sBAAsB,CAAC,YAAoB;IAClD,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChE,MAAM,MAAM,GAAG,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAE9E,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpC,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAEnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACxC,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IACD,OAAO,WAAW,CAAC;AACrB,CAAC", "sourcesContent": ["import Constants from 'expo-constants';\nimport { CodedError, Platform } from 'expo-modules-core';\nimport { DeviceEventEmitter } from 'react-native';\n\nimport { DevicePushToken } from './Tokens.types';\n\nexport default async function getDevicePushTokenAsync(): Promise<DevicePushToken> {\n  const data = await _subscribeDeviceToPushNotificationsAsync();\n  DeviceEventEmitter.emit('onDevicePushToken', { devicePushToken: data });\n  return { type: Platform.OS, data };\n}\n\nfunction guardPermission() {\n  if (!('Notification' in window)) {\n    throw new CodedError(\n      'ERR_UNAVAILABLE',\n      'The Web Notifications API is not available on this device.'\n    );\n  }\n  if (!navigator.serviceWorker) {\n    throw new CodedError(\n      'ERR_UNAVAILABLE',\n      'Notifications cannot be used because the service worker API is not supported on this device. This might also happen because your web page does not support HTTPS.'\n    );\n  }\n  if (Notification.permission !== 'granted') {\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_PERMISSION_DENIED',\n      `Cannot use web notifications without permissions granted. Request permissions with \"expo-permissions\".`\n    );\n  }\n}\n\nasync function _subscribeDeviceToPushNotificationsAsync(): Promise<DevicePushToken['data']> {\n  // @ts-expect-error: TODO: not on the schema\n  const vapidPublicKey: string | null = Constants.expoConfig?.notification?.vapidPublicKey;\n  if (!vapidPublicKey) {\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_PUSH_WEB_MISSING_CONFIG',\n      'You must provide `notification.vapidPublicKey` in `app.json` to use push notifications on web. Learn more: https://docs.expo.dev/versions/latest/guides/using-vapid/.'\n    );\n  }\n\n  // @ts-expect-error: TODO: not on the schema\n  const serviceWorkerPath = Constants.expoConfig?.notification?.serviceWorkerPath;\n  if (!serviceWorkerPath) {\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_PUSH_MISSING_CONFIGURATION',\n      'You must specify `notification.serviceWorkerPath` in `app.json` to use push notifications on the web. Please provide the path to the service worker that will handle notifications.'\n    );\n  }\n  guardPermission();\n\n  let registration: ServiceWorkerRegistration | null = null;\n  try {\n    registration = await navigator.serviceWorker.register(serviceWorkerPath);\n  } catch (error) {\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_PUSH_REGISTRATION_FAILED',\n      `Could not register this device for push notifications because the service worker (${serviceWorkerPath}) could not be registered: ${error}`\n    );\n  }\n  await navigator.serviceWorker.ready;\n\n  if (!registration.active) {\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_PUSH_REGISTRATION_FAILED',\n      'Could not register this device for push notifications because the service worker is not active.'\n    );\n  }\n\n  const subscribeOptions = {\n    userVisibleOnly: true,\n    applicationServerKey: _urlBase64ToUint8Array(vapidPublicKey),\n  };\n  let pushSubscription: PushSubscription | null = null;\n  try {\n    pushSubscription = await registration.pushManager.subscribe(subscribeOptions);\n  } catch (error) {\n    throw new CodedError(\n      'ERR_NOTIFICATIONS_PUSH_REGISTRATION_FAILED',\n      'The device was unable to register for remote notifications with the browser endpoint. (' +\n        error +\n        ')'\n    );\n  }\n  const pushSubscriptionJson = pushSubscription.toJSON();\n\n  const subscriptionObject = {\n    endpoint: pushSubscriptionJson.endpoint,\n    keys: {\n      p256dh: pushSubscriptionJson.keys!.p256dh,\n      auth: pushSubscriptionJson.keys!.auth,\n    },\n  };\n\n  // Store notification icon string in service worker.\n  // This message is received by `/expo-service-worker.js`.\n  // We wrap it with `fromExpoWebClient` to make sure other message\n  // will not override content such as `notificationIcon`.\n  // https://stackoverflow.com/a/35729334/2603230\n  const notificationIcon = (Constants.expoConfig?.notification ?? {}).icon;\n  await registration.active.postMessage(\n    JSON.stringify({ fromExpoWebClient: { notificationIcon } })\n  );\n\n  return subscriptionObject;\n}\n\n// https://github.com/web-push-libs/web-push#using-vapid-key-for-applicationserverkey\nfunction _urlBase64ToUint8Array(base64String: string): Uint8Array {\n  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\n  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');\n\n  const rawData = window.atob(base64);\n  const outputArray = new Uint8Array(rawData.length);\n\n  for (let i = 0; i < rawData.length; ++i) {\n    outputArray[i] = rawData.charCodeAt(i);\n  }\n  return outputArray;\n}\n"]}