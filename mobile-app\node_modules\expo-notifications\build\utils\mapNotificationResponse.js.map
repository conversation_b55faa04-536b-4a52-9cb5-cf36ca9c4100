{"version": 3, "file": "mapNotificationResponse.js", "sourceRoot": "", "sources": ["../../src/utils/mapNotificationResponse.ts"], "names": [], "mappings": "AAOA;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,CAAC,QAA8B,EAAE,EAAE;IACxE,OAAO;QACL,GAAG,QAAQ;QACX,YAAY,EAAE,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC;KACrD,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,YAA0B,EAAE,EAAE,CAAC,CAAC;IAC9D,GAAG,YAAY;IACf,OAAO,EAAE,sBAAsB,CAAC,YAAY,CAAC,OAAO,CAAC;CACtD,CAAC,CAAC;AAEH;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,OAA4B,EAAE,EAAE,CAAC,CAAC;IACvE,GAAG,OAAO;IACV,OAAO,EAAE,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC;CACjD,CAAC,CAAC;AAEH,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,OAA4B,EAAE,EAAE;IACrE,IAAI,CAAC;QACH,yFAAyF;QACzF,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;QACzC,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,aAAa,GAAkD,EAAE,GAAG,OAAO,EAAE,CAAC;YACpF,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC5C,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,YAAY,EAAE;gBACjD,GAAG;oBACD,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,OAAO,GAAG,IAAI,CAAC;wBACf,kFAAkF;wBAClF,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;oBACrE,CAAC;oBACD,OAAO,UAAU,CAAC;gBACpB,CAAC;aACF,CAAC,CAAC;YACH,OAAO,aAAa,CAAC;QACvB,CAAC;IACH,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC", "sourcesContent": ["import {\n  Notification,\n  NotificationContent,\n  NotificationRequest,\n  NotificationResponse,\n} from '../Notifications.types';\n\n/**\n * @hidden\n *\n * Does any required processing of a notification response from native code\n * before it is passed to a notification response listener, or to the\n * last notification response hook.\n *\n * @param response The raw response passed in from native code\n * @returns the mapped response.\n */\nexport const mapNotificationResponse = (response: NotificationResponse) => {\n  return {\n    ...response,\n    notification: mapNotification(response.notification),\n  };\n};\n\n/**\n * @hidden\n *\n * Does any required processing of a notification from native code\n * before it is passed to a notification listener.\n *\n * @param notification The raw notification passed in from native code\n * @returns the mapped notification.\n */\nexport const mapNotification = (notification: Notification) => ({\n  ...notification,\n  request: mapNotificationRequest(notification.request),\n});\n\n/**\n * @hidden\n *\n * Does any required processing of a notification request from native code\n * before it is passed to other JS code.\n *\n * @param request The raw request passed in from native code\n * @returns the mapped request.\n */\nexport const mapNotificationRequest = (request: NotificationRequest) => ({\n  ...request,\n  content: mapNotificationContent(request.content),\n});\n\nlet didWarn = false;\n/**\n * @hidden\n * Does any required processing of notification content from native code\n * before being passed to other JS code.\n *\n * @param content The raw content passed in from native code\n * @returns the mapped content.\n */\nexport const mapNotificationContent = (content: NotificationContent) => {\n  try {\n    // @ts-expect-error: TODO(@kitten): This is not present in the types! This is error prone\n    const dataString = content['dataString'];\n    if (typeof dataString === 'string') {\n      const mappedContent: NotificationContent & { dataString?: string } = { ...content };\n      mappedContent.data = JSON.parse(dataString);\n      Object.defineProperty(mappedContent, 'dataString', {\n        get() {\n          if (!didWarn) {\n            didWarn = true;\n            // TODO(vonovak) remove this warning and delete dataString entry in a next version\n            console.warn('reading dataString is deprecated, use data instead');\n          }\n          return dataString;\n        },\n      });\n      return mappedContent;\n    }\n  } catch (e: any) {\n    console.error(`Error parsing notification: ${e}`);\n  }\n  return content;\n};\n"]}