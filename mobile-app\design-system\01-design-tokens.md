# DESIGN TOKENS - SYSTÈME DE DESIGN AFRIQUE

## PHILOSOPHIE DU DESIGN

### Principes Fondamentaux

1. **UBUNTU** - "Je suis parce que nous sommes" - Design inclusif et communautaire
2. **SIMPLICITÉ** - Interfaces épurées pour tous niveaux de littératie
3. **RÉSILIENCE** - Performance optimale sur hardware limité
4. **ACCESSIBILITÉ** - Utilisable par tous, partout, tout le temps
5. **AUTHENTICITÉ** - Respect des cultures et contextes locaux

## 1. PALETTE DE COULEURS

### 1.1 Couleurs Primaires - Inspirées de l'Afrique

#### Orange <PERSON>eil (Primary)

```css
--color-primary-50: #FFF7ED;   /* Aube dorée */
--color-primary-100: #FFEDD5;  /* Sable du Sahara */
--color-primary-200: #FED7AA;  /* Terre de Sienna */
--color-primary-300: #FDBA74;  /* <PERSON>ucher de soleil */
--color-primary-400: #FB923C;  /* Orange vif */
--color-primary-500: #F97316;  /* Orange principal */
--color-primary-600: #EA580C;  /* Orange foncé */
--color-primary-700: #C2410C;  /* Terre cuite */
--color-primary-800: #9A3412;  /* Brique rouge */
--color-primary-900: #7C2D12;  /* Acajou */
```

#### Vert Baobab (Secondary)
```css
--color-secondary-50: #F0FDF4;   /* Feuille tendre */
--color-secondary-100: #DCFCE7;  /* Vert clair */
--color-secondary-200: #BBF7D0;  /* Vert prairie */
--color-secondary-300: #86EFAC;  /* Vert vif */
--color-secondary-400: #4ADE80;  /* Vert nature */
--color-secondary-500: #22C55E;  /* Vert principal */
--color-secondary-600: #16A34A;  /* Vert forêt */
--color-secondary-700: #15803D;  /* Vert foncé */
--color-secondary-800: #166534;  /* Vert profond */
--color-secondary-900: #14532D;  /* Vert sombre */
```

### 1.2 Couleurs Neutres - Haute Lisibilité

#### Échelle de Gris Optimisée
```css
--color-neutral-0: #FFFFFF;     /* Blanc pur */
--color-neutral-50: #FAFAFA;    /* Blanc cassé */
--color-neutral-100: #F5F5F5;   /* Gris très clair */
--color-neutral-200: #E5E5E5;   /* Gris clair */
--color-neutral-300: #D4D4D4;   /* Gris moyen clair */
--color-neutral-400: #A3A3A3;   /* Gris moyen */
--color-neutral-500: #737373;   /* Gris */
--color-neutral-600: #525252;   /* Gris foncé */
--color-neutral-700: #404040;   /* Gris très foncé */
--color-neutral-800: #262626;   /* Presque noir */
--color-neutral-900: #171717;   /* Noir profond */
--color-neutral-950: #0A0A0A;   /* Noir absolu */
```

### 1.3 Couleurs Sémantiques

#### États et Feedback
```css
/* Succès - Vert Malachite */
--color-success-light: #D1FAE5;
--color-success: #10B981;
--color-success-dark: #047857;

/* Attention - Jaune Safran */
--color-warning-light: #FEF3C7;
--color-warning: #F59E0B;
--color-warning-dark: #D97706;

/* Erreur - Rouge Corail */
--color-error-light: #FEE2E2;
--color-error: #EF4444;
--color-error-dark: #DC2626;

/* Information - Bleu Indigo */
--color-info-light: #DBEAFE;
--color-info: #3B82F6;
--color-info-dark: #1D4ED8;
```

### 1.4 Mode Sombre - Économie Batterie

#### Palette Sombre Optimisée
```css
--color-dark-bg-primary: #0F0F0F;    /* Noir OLED */
--color-dark-bg-secondary: #1A1A1A;  /* Gris très sombre */
--color-dark-bg-tertiary: #262626;   /* Gris sombre */
--color-dark-text-primary: #FFFFFF;  /* Blanc pur */
--color-dark-text-secondary: #D4D4D4; /* Gris clair */
--color-dark-text-tertiary: #A3A3A3; /* Gris moyen */
--color-dark-border: #404040;        /* Bordures sombres */
```

## 2. TYPOGRAPHIE

### 2.1 Familles de Polices

#### Police Principale - Inter (Optimisée Lisibilité)
```css
--font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
--font-family-secondary: 'Inter', system-ui, sans-serif;
--font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;
```

**Justification Inter :**
- Excellente lisibilité sur petits écrans
- Support étendu caractères internationaux
- Optimisée pour interfaces digitales
- Poids de fichier raisonnable

### 2.2 Échelle Typographique

#### Tailles Optimisées Touch
```css
/* Titres */
--font-size-h1: 32px;    /* 2rem - Titres principaux */
--font-size-h2: 28px;    /* 1.75rem - Sous-titres */
--font-size-h3: 24px;    /* 1.5rem - Sections */
--font-size-h4: 20px;    /* 1.25rem - Sous-sections */

/* Corps de texte */
--font-size-lg: 18px;    /* 1.125rem - Texte important */
--font-size-base: 16px;  /* 1rem - Texte standard */
--font-size-sm: 14px;    /* 0.875rem - Texte secondaire */
--font-size-xs: 12px;    /* 0.75rem - Labels, captions */

/* Boutons et interactions */
--font-size-button-lg: 18px;  /* Boutons principaux */
--font-size-button: 16px;     /* Boutons standards */
--font-size-button-sm: 14px;  /* Boutons secondaires */
```

### 2.3 Poids et Styles

#### Hiérarchie Visuelle
```css
--font-weight-light: 300;     /* Texte léger */
--font-weight-normal: 400;    /* Texte normal */
--font-weight-medium: 500;    /* Texte medium */
--font-weight-semibold: 600;  /* Texte semi-gras */
--font-weight-bold: 700;      /* Texte gras */
--font-weight-extrabold: 800; /* Titres importants */
```

### 2.4 Hauteurs de Ligne

#### Lisibilité Optimisée
```css
--line-height-tight: 1.25;    /* Titres */
--line-height-normal: 1.5;    /* Texte standard */
--line-height-relaxed: 1.625; /* Texte long */
--line-height-loose: 2;       /* Espacement large */
```

## 3. ESPACEMENT

### 3.1 Système d'Espacement 8pt

#### Échelle Harmonieuse
```css
--space-0: 0px;      /* Aucun espacement */
--space-1: 4px;      /* 0.25rem - Micro */
--space-2: 8px;      /* 0.5rem - Petit */
--space-3: 12px;     /* 0.75rem - Compact */
--space-4: 16px;     /* 1rem - Standard */
--space-5: 20px;     /* 1.25rem - Confortable */
--space-6: 24px;     /* 1.5rem - Large */
--space-8: 32px;     /* 2rem - Très large */
--space-10: 40px;    /* 2.5rem - Extra large */
--space-12: 48px;    /* 3rem - Section */
--space-16: 64px;    /* 4rem - Grande section */
--space-20: 80px;    /* 5rem - Séparation majeure */
```

### 3.2 Zones de Touch Optimisées

#### Cibles Tactiles Accessibles
```css
--touch-target-min: 44px;     /* Minimum recommandé */
--touch-target-comfortable: 48px; /* Confortable */
--touch-target-large: 56px;   /* Large pour actions importantes */
--touch-target-xl: 64px;      /* Extra large pour seniors */
```

## 4. BORDURES ET RAYONS

### 4.1 Rayons de Bordure

#### Cohérence Visuelle
```css
--border-radius-none: 0px;     /* Aucun rayon */
--border-radius-sm: 4px;       /* Petit rayon */
--border-radius: 8px;          /* Rayon standard */
--border-radius-md: 12px;      /* Rayon moyen */
--border-radius-lg: 16px;      /* Grand rayon */
--border-radius-xl: 24px;      /* Très grand rayon */
--border-radius-full: 9999px;  /* Cercle parfait */
```

### 4.2 Épaisseurs de Bordure

#### Hiérarchie Visuelle
```css
--border-width-0: 0px;     /* Aucune bordure */
--border-width-1: 1px;     /* Bordure fine */
--border-width-2: 2px;     /* Bordure standard */
--border-width-4: 4px;     /* Bordure épaisse */
--border-width-8: 8px;     /* Bordure très épaisse */
```

## 5. OMBRES ET ÉLÉVATIONS

### 5.1 Système d'Ombres

#### Profondeur et Hiérarchie
```css
/* Ombres claires */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

/* Ombres sombres (mode sombre) */
--shadow-dark-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
--shadow-dark: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
--shadow-dark-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
```

## 6. ANIMATIONS ET TRANSITIONS

### 6.1 Durées d'Animation

#### Performance Optimisée
```css
--duration-instant: 0ms;      /* Instantané */
--duration-fast: 150ms;       /* Rapide */
--duration-normal: 250ms;     /* Standard */
--duration-slow: 350ms;       /* Lent */
--duration-slower: 500ms;     /* Très lent */
```

### 6.2 Courbes d'Animation

#### Mouvement Naturel
```css
--ease-linear: linear;
--ease-in: cubic-bezier(0.4, 0, 1, 1);
--ease-out: cubic-bezier(0, 0, 0.2, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
--ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
```

## 7. BREAKPOINTS RESPONSIVE

### 7.1 Points de Rupture

#### Adaptation Multi-Device
```css
--breakpoint-xs: 320px;   /* Petits téléphones */
--breakpoint-sm: 375px;   /* Téléphones standards */
--breakpoint-md: 414px;   /* Grands téléphones */
--breakpoint-lg: 768px;   /* Tablettes portrait */
--breakpoint-xl: 1024px;  /* Tablettes paysage */
--breakpoint-2xl: 1280px; /* Desktop */
```

## 8. TOKENS SPÉCIFIQUES AFRIQUE

### 8.1 Adaptations Culturelles

#### Couleurs Symboliques
```css
/* Couleurs traditionnelles africaines */
--color-kente-gold: #FFD700;      /* Or Kente */
--color-mud-cloth: #8B4513;       /* Bogolan */
--color-sahara-sand: #F4E4BC;     /* Sable du Sahara */
--color-baobab-bark: #8B7355;     /* Écorce de Baobab */
--color-savanna-grass: #9ACD32;   /* Herbe de Savane */
```

### 8.2 Optimisations Techniques

#### Performance Réseau Limité
```css
/* Tailles d'images optimisées */
--image-size-thumb: 48px;     /* Miniatures */
--image-size-small: 96px;     /* Petites images */
--image-size-medium: 192px;   /* Images moyennes */
--image-size-large: 384px;    /* Grandes images */

/* Compression et qualité */
--image-quality-low: 60%;     /* Connexion lente */
--image-quality-medium: 80%;  /* Connexion normale */
--image-quality-high: 95%;    /* Connexion rapide */
```

## 9. ACCESSIBILITÉ

### 9.1 Contrastes Minimums

#### WCAG AA Compliance
```css
/* Ratios de contraste */
--contrast-ratio-aa-normal: 4.5;    /* Texte normal */
--contrast-ratio-aa-large: 3;       /* Texte large */
--contrast-ratio-aaa-normal: 7;     /* AAA normal */
--contrast-ratio-aaa-large: 4.5;    /* AAA large */
```

### 9.2 Focus et États

#### Visibilité Maximale
```css
--focus-ring-width: 3px;
--focus-ring-color: var(--color-primary-500);
--focus-ring-offset: 2px;
--focus-ring-style: solid;
```

## 10. UTILISATION DES TOKENS

### 10.1 Implémentation CSS
```css
:root {
  /* Importer tous les tokens */
  @import './tokens/colors.css';
  @import './tokens/typography.css';
  @import './tokens/spacing.css';
  @import './tokens/borders.css';
  @import './tokens/shadows.css';
  @import './tokens/animations.css';
}

/* Mode sombre automatique */
@media (prefers-color-scheme: dark) {
  :root {
    @import './tokens/dark-mode.css';
  }
}
```

### 10.2 Implémentation React Native
```typescript
export const tokens = {
  colors: {
    primary: {
      50: '#FFF7ED',
      500: '#F97316',
      900: '#7C2D12',
    },
    // ... autres couleurs
  },
  spacing: {
    1: 4,
    2: 8,
    4: 16,
    // ... autres espacements
  },
  typography: {
    fontSize: {
      base: 16,
      lg: 18,
      // ... autres tailles
    },
  },
};
```

Ces tokens forment la base de notre design system, garantissant cohérence, accessibilité et performance optimale pour le marché africain.
