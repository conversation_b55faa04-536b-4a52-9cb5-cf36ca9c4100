{"version": 3, "file": "setBadgeCountAsync.js", "sourceRoot": "", "sources": ["../src/setBadgeCountAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAElE,OAAO,WAAW,MAAM,eAAe,CAAC;AAUxC;;;;;;;;;GASG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,kBAAkB,CAC9C,UAAkB,EAClB,OAA8B;IAE9B,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;QACpC,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,CAAC;IAC3E,CAAC;IACD,MAAM,eAAe,GAAG,OAAO,EAAE,CAAC,QAAQ,CAAC,EAA0B,CAAC,CAAC;IACvE,OAAO,MAAM,WAAW,CAAC,kBAAkB,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;AAC3E,CAAC", "sourcesContent": ["import { UnavailabilityError, Platform } from 'expo-modules-core';\n\nimport BadgeModule from './BadgeModule';\nimport { WebSetBadgeCountOptions } from './BadgeModule.types';\n\nexport interface SetBadgeCountOptions {\n  /**\n   * A configuration object described [in the `badgin` documentation](https://github.com/jaulz/badgin#options).\n   */\n  web?: WebSetBadgeCountOptions;\n}\n\n/**\n * Sets the badge of the app's icon to the specified number. Setting it to `0` clears the badge. On iOS, this method requires that you have requested\n * the user's permission for `allowBadge` via [`requestPermissionsAsync`](#requestpermissionsasyncpermissions),\n * otherwise it will automatically return `false`.\n * > **Note:** Not all Android launchers support application badges. If the launcher does not support icon badges, the method will resolve to `false`.\n * @param badgeCount The count which should appear on the badge. A value of `0` will clear the badge.\n * @param options An object of options configuring behavior applied.\n * @return It returns a Promise resolving to a boolean representing whether the setting of the badge succeeded.\n * @header badge\n */\nexport default async function setBadgeCountAsync(\n  badgeCount: number,\n  options?: SetBadgeCountOptions\n): Promise<boolean> {\n  if (!BadgeModule.setBadgeCountAsync) {\n    throw new UnavailabilityError('ExpoNotifications', 'setBadgeCountAsync');\n  }\n  const platformOptions = options?.[Platform.OS as keyof typeof options];\n  return await BadgeModule.setBadgeCountAsync(badgeCount, platformOptions);\n}\n"]}