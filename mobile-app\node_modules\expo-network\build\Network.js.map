{"version": 3, "file": "Network.js", "sourceRoot": "", "sources": ["../src/Network.ts"], "names": [], "mappings": "AAAA,OAAO,EAA0B,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAChF,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAE5C,OAAO,WAAW,MAAM,eAAe,CAAC;AACxC,OAAO,EAAmC,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AAEpF,OAAO,EAAmC,gBAAgB,EAAE,CAAC;AAE7D,MAAM,uBAAuB,GAAG,uBAAuB,CAAC;AAExD,cAAc;AACd;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC;QACtC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,sBAAsB,CAAC,CAAC;IACxE,CAAC;IACD,OAAO,MAAM,WAAW,CAAC,oBAAoB,EAAE,CAAC;AAClD,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB;IACrC,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;QACnC,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;IACrE,CAAC;IACD,OAAO,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAC;AAC/C,CAAC;AAED,cAAc;AACd;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,KAAK,UAAU,0BAA0B;IAC9C,IAAI,CAAC,WAAW,CAAC,0BAA0B,EAAE,CAAC;QAC5C,MAAM,IAAI,mBAAmB,CAAC,cAAc,EAAE,4BAA4B,CAAC,CAAC;IAC9E,CAAC;IACD,OAAO,MAAM,WAAW,CAAC,0BAA0B,EAAE,CAAC;AACxD,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,uBAAuB,CACrC,QAA4C;IAE5C,OAAO,WAAW,CAAC,WAAW,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;AACpE,CAAC;AAED,cAAc;AACd;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,eAAe;IAC7B,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAe,EAAE,CAAC,CAAC;IAEnE,SAAS,CAAC,GAAG,EAAE;QACb,oBAAoB,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,uBAAuB,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1F,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,YAAY,CAAC;AACtB,CAAC", "sourcesContent": ["import { type EventSubscription, UnavailabilityError } from 'expo-modules-core';\nimport { useEffect, useState } from 'react';\n\nimport ExpoNetwork from './ExpoNetwork';\nimport { NetworkState, NetworkStateEvent, NetworkStateType } from './Network.types';\n\nexport { NetworkState, NetworkStateEvent, NetworkStateType };\n\nconst onNetworkStateEventName = 'onNetworkStateChanged';\n\n// @needsAudit\n/**\n * Gets the device's current network connection state.\n *\n * On web, `navigator.connection.type` is not available on browsers. So if there is an active\n * network connection, the field `type` returns `NetworkStateType.UNKNOWN`. Otherwise, it returns\n * `NetworkStateType.NONE`.\n * @return A `Promise` that fulfils with a `NetworkState` object.\n *\n * @example\n * ```ts\n * await Network.getNetworkStateAsync();\n * // {\n * //   type: NetworkStateType.CELLULAR,\n * //   isConnected: true,\n * //   isInternetReachable: true,\n * // }\n * ```\n */\nexport async function getNetworkStateAsync(): Promise<NetworkState> {\n  if (!ExpoNetwork.getNetworkStateAsync) {\n    throw new UnavailabilityError('expo-network', 'getNetworkStateAsync');\n  }\n  return await ExpoNetwork.getNetworkStateAsync();\n}\n\n// @needsAudit\n/**\n * Gets the device's current IPv4 address. Returns `0.0.0.0` if the IP address could not be retrieved.\n *\n * On web, this method uses the third-party [`ipify service`](https://www.ipify.org/) to get the\n * public IP address of the current device.\n * @return A `Promise` that fulfils with a `string` of the current IP address of the device's main\n * network interface. Can only be IPv4 address.\n *\n * @example\n * ```ts\n * await Network.getIpAddressAsync();\n * // \"************\"\n * ```\n */\nexport async function getIpAddressAsync(): Promise<string> {\n  if (!ExpoNetwork.getIpAddressAsync) {\n    throw new UnavailabilityError('expo-network', 'getIpAddressAsync');\n  }\n  return await ExpoNetwork.getIpAddressAsync();\n}\n\n// @needsAudit\n/**\n * Tells if the device is in airplane mode.\n * @return Returns a `Promise` that fulfils with a `boolean` value for whether the device is in\n * airplane mode or not.\n * @platform android\n *\n * @example\n * ```ts\n * await Network.isAirplaneModeEnabledAsync();\n * // false\n * ```\n */\nexport async function isAirplaneModeEnabledAsync(): Promise<boolean> {\n  if (!ExpoNetwork.isAirplaneModeEnabledAsync) {\n    throw new UnavailabilityError('expo-network', 'isAirplaneModeEnabledAsync');\n  }\n  return await ExpoNetwork.isAirplaneModeEnabledAsync();\n}\n\n/**\n * Adds a listener that will fire whenever the network state changes.\n *\n * @param listener Callback to execute when the network state changes. The callback is provided with\n * a single argument that is an object containing information about the network state.\n *\n * @example\n * ```ts\n * const subscription = addNetworkStateListener(({ type, isConnected, isInternetReachable }) => {\n *   console.log(`Network type: ${type}, Connected: ${isConnected}, Internet Reachable: ${isInternetReachable}`);\n * });\n * ```\n *\n * @returns A subscription object with a remove function to unregister the listener.\n */\nexport function addNetworkStateListener(\n  listener: (event: NetworkStateEvent) => void\n): EventSubscription {\n  return ExpoNetwork.addListener(onNetworkStateEventName, listener);\n}\n\n// @needsAudit\n/**\n * Returns the current network state of the device. This method\n * initiates a listener for network state changes and cleans up before unmounting.\n *\n * @example\n * ```ts\n * const networkState = useNetworkState();\n * console.log(`Current network type: ${networkState.type}`);\n * ```\n *\n * @return The current network state of the device, including connectivity and type.\n */\nexport function useNetworkState(): NetworkState {\n  const [networkState, setNetworkState] = useState<NetworkState>({});\n\n  useEffect(() => {\n    getNetworkStateAsync().then(setNetworkState);\n    const listener = addNetworkStateListener((networkState) => setNetworkState(networkState));\n    return () => listener.remove();\n  }, []);\n\n  return networkState;\n}\n"]}