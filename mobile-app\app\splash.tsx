// =====================================================
// SPLASH SCREEN - ÉCRAN DE DÉMARRAGE AFRICAIN
// =====================================================

import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
  Image,
} from 'react-native';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuthStore } from '../store/authStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../constants/Colors';
import { Config } from '../constants/Config';

const { width, height } = Dimensions.get('window');

export default function SplashScreen() {
  const { isAuthenticated, user, refreshToken } = useAuthStore();

  // Animations
  const illustrationScale = useRef(new Animated.Value(0.8)).current;
  const illustrationOpacity = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const subtitleOpacity = useRef(new Animated.Value(0)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const buttonScale = useRef(new Animated.Value(0.9)).current;
  const dotsOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Démarrer les animations
      startAnimations();

      // Vérifier si c'est la première fois
      const hasSeenOnboarding = await AsyncStorage.getItem('hasSeenOnboarding');

      // Vérifier la session utilisateur
      await refreshToken();

      // Attendre minimum 2.5 secondes pour l'animation
      await new Promise(resolve => setTimeout(resolve, 2500));

      // Navigation selon l'état
      if (!hasSeenOnboarding) {
        // Première fois - aller vers onboarding
        router.replace('/(auth)/onboarding');
      } else if (isAuthenticated && user) {
        // Utilisateur connecté - aller vers son interface
        redirectToUserDashboard(user.role);
      } else {
        // Utilisateur non connecté - aller vers accueil
        router.replace('/');
      }
    } catch (error) {
      console.error('❌ Erreur initialisation splash:', error);
      // En cas d'erreur, aller vers l'accueil
      setTimeout(() => {
        router.replace('/');
      }, 3000);
    }
  };

  const redirectToUserDashboard = (role: string) => {
    switch (role) {
      case 'client':
        router.replace('/(client)/(tabs)/home');
        break;
      case 'deliverer':
        router.replace('/(deliverer)/(tabs)/dashboard');
        break;
      case 'merchant':
        router.replace('/(merchant)/(tabs)/orders');
        break;
      default:
        router.replace('/');
    }
  };

  const startAnimations = () => {
    // Animation séquentielle des éléments
    Animated.sequence([
      // Animation de l'illustration (apparition et scale)
      Animated.parallel([
        Animated.timing(illustrationOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(illustrationScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      // Animation du titre principal
      Animated.timing(titleOpacity, {
        toValue: 1,
        duration: 600,
        delay: 200,
        useNativeDriver: true,
      }),
      // Animation du sous-titre
      Animated.timing(subtitleOpacity, {
        toValue: 1,
        duration: 500,
        delay: 100,
        useNativeDriver: true,
      }),
      // Animation des points de pagination
      Animated.timing(dotsOpacity, {
        toValue: 1,
        duration: 400,
        delay: 100,
        useNativeDriver: true,
      }),
      // Animation du bouton
      Animated.parallel([
        Animated.timing(buttonOpacity, {
          toValue: 1,
          duration: 500,
          delay: 200,
          useNativeDriver: true,
        }),
        Animated.spring(buttonScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  };

  // Composant pour créer l'illustration africaine
  const AfricanIllustration = () => (
    <View style={styles.illustrationContainer}>
      {/* Arrière-plan avec motifs géométriques */}
      <View style={styles.backgroundPattern}>
        <View style={[styles.geometricShape, styles.shape1]} />
        <View style={[styles.geometricShape, styles.shape2]} />
        <View style={[styles.geometricShape, styles.shape3]} />
        <View style={[styles.geometricShape, styles.shape4]} />
      </View>

      {/* Personnage africain */}
      <View style={styles.character}>
        {/* Tête */}
        <View style={styles.head}>
          <View style={styles.face} />
          <View style={styles.hat} />
        </View>

        {/* Corps */}
        <View style={styles.body}>
          <View style={styles.shirt} />
          <View style={styles.arms}>
            <View style={styles.leftArm} />
            <View style={styles.rightArm} />
          </View>
        </View>

        {/* Colis dans les mains */}
        <View style={styles.package}>
          <View style={styles.packageBox} />
          <View style={styles.packageRibbon} />
          <View style={styles.packageBow} />
        </View>
      </View>

      {/* Colis flottants */}
      <View style={styles.floatingPackages}>
        <View style={[styles.floatingPackage, styles.package1]} />
        <View style={[styles.floatingPackage, styles.package2]} />
        <View style={[styles.floatingPackage, styles.package3]} />
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.primary[50]} />

      {/* Arrière-plan avec dégradé africain */}
      <LinearGradient
        colors={[Colors.primary[50], Colors.primary[100], Colors.primary[200]]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.content}>
          {/* Illustration africaine animée */}
          <Animated.View
            style={[
              styles.illustrationWrapper,
              {
                opacity: illustrationOpacity,
                transform: [{ scale: illustrationScale }],
              },
            ]}
          >
            <AfricanIllustration />
          </Animated.View>

          {/* Titre principal */}
          <Animated.View
            style={[
              styles.titleContainer,
              { opacity: titleOpacity },
            ]}
          >
            <Text style={styles.mainTitle}>Flash sale everyday, all of</Text>
            <Text style={styles.mainTitle}>happy now</Text>
          </Animated.View>

          {/* Sous-titre */}
          <Animated.View
            style={[
              styles.subtitleContainer,
              { opacity: subtitleOpacity },
            ]}
          >
            <Text style={styles.subtitle}>
              Determine your financial planning easily.
            </Text>
            <Text style={styles.subtitle}>
              Everything is right on track. Get it today with
            </Text>
            <Text style={styles.subtitle}>
              hassle free.
            </Text>
          </Animated.View>

          {/* Points de pagination */}
          <Animated.View
            style={[
              styles.paginationDots,
              { opacity: dotsOpacity },
            ]}
          >
            <View style={[styles.dot, styles.activeDot]} />
            <View style={styles.dot} />
            <View style={styles.dot} />
            <View style={styles.dot} />
          </Animated.View>

          {/* Bouton Get Started */}
          <Animated.View
            style={[
              styles.buttonContainer,
              {
                opacity: buttonOpacity,
                transform: [{ scale: buttonScale }],
              },
            ]}
          >
            <View style={styles.getStartedButton}>
              <Text style={styles.buttonText}>Get Started</Text>
            </View>
          </Animated.View>

          {/* Version de l'app */}
          <View style={styles.footer}>
            <Text style={styles.versionText}>Version {Config.appVersion}</Text>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 60,
  },

  // Illustration africaine
  illustrationWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 40,
  },
  illustrationContainer: {
    width: width * 0.8,
    height: height * 0.4,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Motifs géométriques d'arrière-plan
  backgroundPattern: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  geometricShape: {
    position: 'absolute',
    borderRadius: 8,
  },
  shape1: {
    width: 60,
    height: 60,
    backgroundColor: Colors.primary[300],
    top: '10%',
    left: '10%',
    transform: [{ rotate: '45deg' }],
    opacity: 0.6,
  },
  shape2: {
    width: 40,
    height: 40,
    backgroundColor: Colors.secondary[400],
    top: '20%',
    right: '15%',
    transform: [{ rotate: '30deg' }],
    opacity: 0.5,
  },
  shape3: {
    width: 50,
    height: 50,
    backgroundColor: Colors.primary[400],
    bottom: '25%',
    left: '5%',
    transform: [{ rotate: '60deg' }],
    opacity: 0.4,
  },
  shape4: {
    width: 35,
    height: 35,
    backgroundColor: Colors.secondary[300],
    bottom: '15%',
    right: '10%',
    transform: [{ rotate: '15deg' }],
    opacity: 0.6,
  },

  // Personnage africain
  character: {
    position: 'relative',
    alignItems: 'center',
    zIndex: 2,
  },
  head: {
    position: 'relative',
    marginBottom: 10,
  },
  face: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#8B4513', // Couleur de peau africaine
  },
  hat: {
    position: 'absolute',
    top: -10,
    left: 5,
    width: 70,
    height: 30,
    borderRadius: 35,
    backgroundColor: Colors.primary[600],
    transform: [{ rotate: '-5deg' }],
  },
  body: {
    alignItems: 'center',
    marginBottom: 15,
  },
  shirt: {
    width: 100,
    height: 80,
    borderRadius: 20,
    backgroundColor: '#F5F5DC', // Couleur beige pour la chemise
  },
  arms: {
    position: 'absolute',
    top: 10,
    flexDirection: 'row',
    width: 140,
    justifyContent: 'space-between',
  },
  leftArm: {
    width: 25,
    height: 60,
    borderRadius: 12,
    backgroundColor: '#8B4513',
    transform: [{ rotate: '-20deg' }],
  },
  rightArm: {
    width: 25,
    height: 60,
    borderRadius: 12,
    backgroundColor: '#8B4513',
    transform: [{ rotate: '20deg' }],
  },

  // Colis principal
  package: {
    position: 'relative',
    alignItems: 'center',
    marginTop: 10,
  },
  packageBox: {
    width: 60,
    height: 60,
    backgroundColor: Colors.primary[200],
    borderRadius: 8,
    borderWidth: 2,
    borderColor: Colors.primary[400],
  },
  packageRibbon: {
    position: 'absolute',
    top: 0,
    left: '50%',
    marginLeft: -2,
    width: 4,
    height: 60,
    backgroundColor: Colors.secondary[500],
  },
  packageBow: {
    position: 'absolute',
    top: -5,
    left: '50%',
    marginLeft: -8,
    width: 16,
    height: 10,
    backgroundColor: Colors.secondary[600],
    borderRadius: 8,
  },

  // Colis flottants
  floatingPackages: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  floatingPackage: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderRadius: 6,
    borderWidth: 1,
  },
  package1: {
    backgroundColor: Colors.primary[300],
    borderColor: Colors.primary[500],
    top: '30%',
    left: '20%',
    transform: [{ rotate: '15deg' }],
  },
  package2: {
    backgroundColor: Colors.secondary[300],
    borderColor: Colors.secondary[500],
    top: '60%',
    right: '25%',
    transform: [{ rotate: '-10deg' }],
  },
  package3: {
    backgroundColor: Colors.primary[400],
    borderColor: Colors.primary[600],
    bottom: '40%',
    left: '15%',
    transform: [{ rotate: '25deg' }],
  },

  // Textes
  titleContainer: {
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  mainTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.neutral[900],
    textAlign: 'center',
    lineHeight: 34,
    marginBottom: 4,
  },
  subtitleContainer: {
    alignItems: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.neutral[600],
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 2,
  },

  // Points de pagination
  paginationDots: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 32,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.neutral[300],
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: Colors.secondary[500],
    width: 24,
    borderRadius: 12,
  },

  // Bouton
  buttonContainer: {
    width: '100%',
    marginBottom: 20,
  },
  getStartedButton: {
    backgroundColor: Colors.secondary[500],
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.secondary[500],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  buttonText: {
    color: Colors.neutral[0],
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0.5,
  },

  // Footer
  footer: {
    alignItems: 'center',
  },
  versionText: {
    fontSize: 12,
    color: Colors.neutral[500],
    textAlign: 'center',
  },
});
