export {
  PermissionStatus,
  type PermissionHookOptions,
  type PermissionExpiration,
} from 'expo-modules-core';

export { LocationEventEmitter as EventEmitter } from './LocationEventEmitter';
export { _getCurrentWatchId } from './LocationSubscribers';
export { installWebGeolocationPolyfill } from './GeolocationPolyfill';

export * from './Location';
export * from './Location.types';

export {
  LocationAccuracy as Accuracy,
  LocationActivityType as ActivityType,
  LocationGeofencingEventType as GeofencingEventType,
  LocationGeofencingRegionState as GeofencingRegionState,
} from './Location.types';
