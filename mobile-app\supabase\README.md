# 🚀 ARCHITECTURE BACKEND SUPABASE - DELIVERY APP AFRICA

## 📋 OVERVIEW

Architecture backend complète pour l'application de livraison africaine utilisant Supabase avec :
- **Row Level Security (RLS)** pour chaque table
- **Edge Functions** TypeScript pour la logique métier
- **Authentification multi-provider** (email, téléphone, social)
- **Middleware de validation** et sécurisation
- **Webhooks paiements** mobile money intégrés
- **Rate limiting** et protection contre les abus

## 🏗️ STRUCTURE DU PROJET

```
mobile-app/supabase/
├── 01-rls-policies.sql          # Politiques Row Level Security
├── 02-auth-config.sql           # Configuration authentification
├── 03-middleware-validation.ts  # Middleware sécurité et validation
├── 04-seed-test-data.sql       # Données de test
├── 05-webhook-config.ts        # Configuration webhooks paiements
├── functions/                   # Edge Functions Supabase
│   ├── delivery-pricing/        # Calcul tarifs dynamiques
│   ├── rider-matching/          # Matching livreur automatique
│   ├── send-notification/       # Notifications push temps réel
│   ├── payment-webhook/         # Webhooks paiements mobile money
│   ├── analytics-reports/       # Génération rapports analytics
│   └── geocoding-distance/      # Géocodage et calcul distances
└── README.md                    # Documentation (ce fichier)
```

## 🔒 SÉCURITÉ IMPLÉMENTÉE

### **Row Level Security (RLS)**

Chaque table est protégée par des politiques RLS spécifiques :

```sql
-- Exemple : Les clients ne voient que leurs commandes
CREATE POLICY "Customers can manage own orders" ON orders
    FOR ALL USING (auth.uid() = customer_id);

-- Les livreurs voient leurs commandes assignées
CREATE POLICY "Riders can view assigned orders" ON orders
    FOR SELECT USING (auth.uid() = delivery_rider_id);
```

**Tables protégées :**
- ✅ `profiles` - Profils utilisateurs
- ✅ `addresses` - Adresses de livraison
- ✅ `orders` - Commandes
- ✅ `payments` - Paiements
- ✅ `notifications` - Notifications
- ✅ `user_wallets` - Portefeuilles
- ✅ `delivery_tracking` - Tracking livraison

### **Protection contre les Injections SQL**

- **Requêtes paramétrées** : Toutes les requêtes utilisent des paramètres
- **Validation des entrées** : Middleware de validation stricte
- **Sanitisation** : Nettoyage automatique des données
- **Types TypeScript** : Validation au niveau du code

### **Rate Limiting**

```typescript
// Configuration par endpoint
export const RateLimitConfigs = {
  auth: { windowMs: 15 * 60 * 1000, maxRequests: 5 },      // 5 tentatives/15min
  payment: { windowMs: 60 * 1000, maxRequests: 3 },        // 3 paiements/min
  general: { windowMs: 15 * 60 * 1000, maxRequests: 100 }  // 100 req/15min
}
```

### **Validation des Données**

```typescript
// Règles de validation prédéfinies
export const ValidationRules = {
  userRegistration: [
    { field: 'email', type: 'email', required: true },
    { field: 'phone_number', type: 'phone', required: true },
    { field: 'password', type: 'string', min: 8, max: 128 }
  ]
}
```

## 🔧 EDGE FUNCTIONS

### **1. Calcul Tarifs Dynamiques** (`delivery-pricing`)

**Endpoint :** `POST /functions/v1/delivery-pricing`

```typescript
interface PricingRequest {
  pickup_location: { latitude: number, longitude: number }
  delivery_location: { latitude: number, longitude: number }
  delivery_type: 'food' | 'package' | 'grocery' | 'pharmacy'
  order_value?: number
  weight_kg?: number
  is_urgent?: boolean
}
```

**Fonctionnalités :**
- ✅ Calcul distance Haversine
- ✅ Tarifs par pays et devise
- ✅ Frais d'urgence et poids
- ✅ Zones de livraison optimisées
- ✅ Remises commerçants

### **2. Matching Livreur Automatique** (`rider-matching`)

**Endpoint :** `POST /functions/v1/rider-matching`

```typescript
interface MatchingRequest {
  order_id: string
  pickup_location: { latitude: number, longitude: number }
  delivery_location: { latitude: number, longitude: number }
  urgency_level: 'low' | 'medium' | 'high'
}
```

**Algorithme de scoring :**
- 🎯 **Proximité** (30%) - Distance au point de collecte
- ⚡ **Disponibilité** (25%) - Statut du livreur
- ⭐ **Rating** (20%) - Note moyenne
- 🏆 **Expérience** (10%) - Nombre de livraisons
- 🚗 **Véhicule** (10%) - Adéquation type de livraison
- 🗺️ **Zone** (5%) - Familiarité avec la zone

### **3. Notifications Push Temps Réel** (`send-notification`)

**Endpoint :** `POST /functions/v1/send-notification`

**Canaux supportés :**
- 📱 **Push** - Firebase Cloud Messaging
- 📧 **Email** - SendGrid
- 📱 **SMS** - Africa's Talking
- 🔔 **In-app** - Base de données

**Fonctionnalités :**
- ✅ Templates multi-langues
- ✅ Préférences utilisateur
- ✅ Heures silencieuses
- ✅ Rate limiting par utilisateur

### **4. Webhooks Paiements** (`payment-webhook`)

**Endpoints :**
- `POST /functions/v1/payment-webhook/mpesa`
- `POST /functions/v1/payment-webhook/orange-money`
- `POST /functions/v1/payment-webhook/wave`
- `POST /functions/v1/payment-webhook/paystack`

**Providers supportés :**
- 🇰🇪 **M-Pesa** (Kenya, Tanzanie)
- 🇸🇳 **Orange Money** (Sénégal, Côte d'Ivoire, Mali)
- 🇸🇳 **Wave** (Sénégal, Côte d'Ivoire)
- 🇳🇬 **Paystack** (Nigeria, Ghana)
- 🌍 **Flutterwave** (Multi-pays)

### **5. Analytics et Rapports** (`analytics-reports`)

**Endpoint :** `POST /functions/v1/analytics-reports`

**Types de rapports :**
- 📊 **daily** - Rapport quotidien
- 📈 **weekly** - Rapport hebdomadaire
- 📉 **monthly** - Rapport mensuel
- 🎯 **custom** - Période personnalisée

### **6. Géocodage et Distance** (`geocoding-distance`)

**Endpoints :**
- `POST /functions/v1/geocoding-distance/geocode`
- `POST /functions/v1/geocoding-distance/distance`
- `POST /functions/v1/geocoding-distance/optimize-route`

**Services intégrés :**
- 🗺️ **Google Maps** (principal)
- 🌍 **OpenStreetMap** (fallback)
- 📍 **MapBox** (alternative)

## 🔐 AUTHENTIFICATION

### **Providers Configurés**

```sql
-- Configuration via Supabase Dashboard
{
  "email": { "enabled": true, "confirmEmail": true },
  "phone": { "enabled": true, "confirmPhone": true },
  "google": { "enabled": true },
  "facebook": { "enabled": true },
  "apple": { "enabled": true }
}
```

### **Gestion Automatique des Profils**

```sql
-- Trigger automatique à l'inscription
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
```

**Actions automatiques :**
- ✅ Création profil utilisateur
- ✅ Portefeuille par défaut
- ✅ Préférences notifications
- ✅ Rôle client par défaut

### **Vérification OTP SMS**

```typescript
// Fonction de vérification
await supabase.rpc('verify_phone_otp', {
  phone_number: '+221701234567',
  otp_code: '123456'
})
```

## 💳 PAIEMENTS MOBILE MONEY

### **Configuration Webhooks**

```typescript
export const PaymentProviders = [
  {
    name: 'mpesa',
    countries: ['KEN', 'TZA'],
    webhook_config: {
      endpoint_url: '/functions/v1/payment-webhook/mpesa',
      events: ['payment.completed', 'payment.failed']
    }
  }
]
```

### **Validation Signatures**

Chaque provider a sa méthode de validation :

```typescript
function validatePaystackSignature(payload: string, signature: string, secret: string): boolean {
  const crypto = require('crypto')
  const expectedSignature = crypto
    .createHmac('sha512', secret)
    .update(payload)
    .digest('hex')
  
  return signature === expectedSignature
}
```

### **Gestion des Erreurs**

- ✅ **Retry automatique** avec backoff exponentiel
- ✅ **Logs détaillés** pour debugging
- ✅ **Alertes admin** en cas d'échec
- ✅ **Métriques** de performance

## 📊 MONITORING ET ANALYTICS

### **Métriques Collectées**

```sql
-- Logs de performance
CREATE TABLE webhook_metrics (
  provider TEXT,
  event_type TEXT,
  processing_time_ms INTEGER,
  success BOOLEAN,
  timestamp TIMESTAMPTZ
);
```

### **Vues Matérialisées**

```sql
-- Statistiques temps réel
CREATE MATERIALIZED VIEW merchant_stats AS
SELECT 
  m.id,
  COUNT(o.id) as total_orders,
  AVG(o.total_amount) as avg_order_value,
  m.average_rating
FROM merchants m
LEFT JOIN orders o ON m.id = o.merchant_id
GROUP BY m.id;
```

### **Rapports Automatiques**

- 📈 **Tendances commandes** par jour/heure
- 🏆 **Top performers** (commerçants, livreurs, produits)
- 💰 **Revenus** par zone/type de livraison
- ⭐ **Satisfaction client** moyenne

## 🚀 DÉPLOIEMENT

### **1. Configuration Environnement**

```bash
# Variables d'environnement requises
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# APIs externes
GOOGLE_MAPS_API_KEY=your-google-maps-key
FCM_SERVER_KEY=your-fcm-server-key
AFRICAS_TALKING_API_KEY=your-africas-talking-key

# Paiements
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
ORANGE_MONEY_API_KEY=your-orange-money-key
WAVE_API_KEY=your-wave-api-key
PAYSTACK_SECRET_KEY=your-paystack-secret-key
```

### **2. Installation Base de Données**

```bash
# 1. Exécuter les scripts SQL dans l'ordre
psql -f database/01-schema-core.sql
psql -f database/02-schema-orders-delivery.sql
psql -f database/03-schema-payments.sql
psql -f database/04-schema-notifications-audit.sql
psql -f database/05-triggers-functions.sql
psql -f database/06-seed-data.sql
psql -f database/07-indexes-optimization.sql

# 2. Configurer RLS
psql -f supabase/01-rls-policies.sql
psql -f supabase/02-auth-config.sql

# 3. Insérer données de test
psql -f supabase/04-seed-test-data.sql
```

### **3. Déploiement Edge Functions**

```bash
# Installer Supabase CLI
npm install -g supabase

# Login et link project
supabase login
supabase link --project-ref your-project-ref

# Déployer toutes les fonctions
supabase functions deploy delivery-pricing
supabase functions deploy rider-matching
supabase functions deploy send-notification
supabase functions deploy payment-webhook
supabase functions deploy analytics-reports
supabase functions deploy geocoding-distance
```

### **4. Configuration Webhooks**

```bash
# Exécuter le script de configuration
npm run setup-webhooks
```

## 🧪 TESTS

### **Données de Test Incluses**

- 👥 **8 utilisateurs** (clients, commerçants, livreurs, admin)
- 🏪 **3 commerçants** (restaurant sénégalais, kenyan, épicerie)
- 🍽️ **7 produits** avec catégories
- 📦 **2 commandes** (livrée, en cours)
- 💳 **2 paiements** (Orange Money, M-Pesa)
- ⭐ **2 évaluations** client

### **Tests Edge Functions**

```bash
# Test calcul tarifs
curl -X POST "https://your-project.supabase.co/functions/v1/delivery-pricing" \
  -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "pickup_location": {"latitude": 14.6928, "longitude": -17.4467},
    "delivery_location": {"latitude": 14.7000, "longitude": -17.4500},
    "delivery_type": "food",
    "customer_id": "11111111-1111-1111-1111-111111111111"
  }'

# Test matching livreur
curl -X POST "https://your-project.supabase.co/functions/v1/rider-matching" \
  -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee",
    "pickup_location": {"latitude": 14.6928, "longitude": -17.4467},
    "delivery_location": {"latitude": 14.7000, "longitude": -17.4500},
    "delivery_type": "food",
    "urgency_level": "medium"
  }'
```

## 📱 INTÉGRATION CLIENT

### **SDK TypeScript**

```typescript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'https://your-project.supabase.co',
  'your-anon-key'
)

// Authentification
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password',
  options: {
    data: {
      first_name: 'John',
      last_name: 'Doe',
      phone_number: '+221701234567'
    }
  }
})

// Calcul tarifs
const { data: pricing } = await supabase.functions.invoke('delivery-pricing', {
  body: {
    pickup_location: { latitude: 14.6928, longitude: -17.4467 },
    delivery_location: { latitude: 14.7000, longitude: -17.4500 },
    delivery_type: 'food',
    customer_id: user.id
  }
})
```

### **Gestion Offline**

```typescript
// Synchronisation automatique
const { data: orders } = await supabase
  .from('orders')
  .select('*')
  .eq('customer_id', user.id)
  .eq('sync_status', 'pending')

// Upload des données offline
for (const order of offlineOrders) {
  await supabase.from('orders').upsert(order)
}
```

## 🔧 MAINTENANCE

### **Scripts de Maintenance**

```sql
-- Nettoyage automatique (à programmer)
SELECT cleanup_old_data();           -- Purge anciennes données
SELECT refresh_materialized_views(); -- Rafraîchir statistiques
SELECT optimize_database();          -- Optimiser performances
```

### **Monitoring Santé**

```sql
-- Vérifier performances
SELECT * FROM slow_queries WHERE mean_time > 100;

-- Index inutilisés
SELECT * FROM unused_indexes;

-- Taille des tables
SELECT * FROM table_sizes ORDER BY size_bytes DESC;
```

## 🌍 SPÉCIFICITÉS AFRICAINES

### **Support Multi-Pays**
- ✅ **17 pays** pré-configurés
- ✅ **15+ devises** locales
- ✅ **10 langues** supportées
- ✅ **8 fuseaux horaires** africains

### **Optimisations Infrastructure**
- ✅ **Connexions 2G/3G** optimisées
- ✅ **Mode offline** complet
- ✅ **Compression données** automatique
- ✅ **Cache intelligent** multi-niveaux

### **Intégrations Locales**
- ✅ **Africa's Talking** SMS
- ✅ **Providers mobile money** locaux
- ✅ **Géocodage** adapté à l'Afrique
- ✅ **Devises** et taux de change

Cette architecture backend Supabase offre une base solide, sécurisée et évolutive pour votre application de livraison africaine, capable de supporter une croissance de 0 à 1M+ utilisateurs tout en maintenant des performances optimales.
