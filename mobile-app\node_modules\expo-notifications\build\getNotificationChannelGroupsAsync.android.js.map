{"version": 3, "file": "getNotificationChannelGroupsAsync.android.js", "sourceRoot": "", "sources": ["../src/getNotificationChannelGroupsAsync.android.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,+BAA+B,MAAM,mCAAmC,CAAC;AAGhF,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,iCAAiC;IAG7D,IAAI,CAAC,+BAA+B,CAAC,iCAAiC,EAAE,CAAC;QACvE,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,mCAAmC,CAAC,CAAC;IACtF,CAAC;IACD,OAAO,MAAM,+BAA+B,CAAC,iCAAiC,EAAE,CAAC;AACnF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationChannelGroupManager from './NotificationChannelGroupManager';\nimport { NotificationChannelGroup } from './NotificationChannelGroupManager.types';\n\nexport default async function getNotificationChannelGroupsAsync(): Promise<\n  NotificationChannelGroup[]\n> {\n  if (!NotificationChannelGroupManager.getNotificationChannelGroupsAsync) {\n    throw new UnavailabilityError('Notifications', 'getNotificationChannelGroupsAsync');\n  }\n  return await NotificationChannelGroupManager.getNotificationChannelGroupsAsync();\n}\n"]}