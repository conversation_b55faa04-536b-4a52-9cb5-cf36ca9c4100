# USER STORIES DÉTAILLÉES - APPLICATION DE LIVRAISON AFRIQUE

## MÉTHODOLOGIE

### Framework utilisé
- **Format** : En tant que [persona], je veux [action] afin de [bénéfice]
- **Critères d'acceptation** : Scénarios Given/When/Then
- **Priorisation** : MoSCoW (Must/Should/Could/Won't)
- **Estimation** : Story points (1-13)

## 1. PARCOURS CLIENT COMPLET

### 1.1 DÉCOUVERTE ET ONBOARDING

#### US-C001 : Découverte de l'application
**En tant qu'** Amina (professionnelle urbaine)  
**Je veux** découvrir l'application via recommandation sociale  
**Afin de** trouver une solution de livraison fiable

**Critères d'acceptation :**
```gherkin
Given je suis sur les réseaux sociaux
When je vois une publication d'ami sur l'app
Then je peux cliquer sur le lien de téléchargement
And je suis redirigée vers le store approprié
And je vois les avis et notes de l'application

Given je télécharge l'application
When j'ouvre l'app pour la première fois
Then je vois un onboarding en 3 étapes max
And je peux choisir ma langue (français/anglais/local)
And je peux passer l'onboarding si pressée
```

**Priorité :** Must Have | **Story Points :** 5

---

#### US-C002 : Inscription simplifiée
**En tant que** Kwame (étudiant urbain)  
**Je veux** m'inscrire rapidement avec mon numéro de téléphone  
**Afin de** commencer à utiliser l'app sans friction

**Critères d'acceptation :**
```gherkin
Given je suis sur l'écran d'inscription
When je saisis mon numéro de téléphone
Then je reçois un SMS avec code de vérification
And le code est pré-rempli automatiquement si possible
And je peux ressaisir le code si non reçu

Given je valide mon numéro
When je saisis nom et prénom
Then je peux choisir ma photo de profil (optionnel)
And je peux définir ma localisation principale
And mon compte est créé immédiatement
```

**Priorité :** Must Have | **Story Points :** 3

---

#### US-C003 : Configuration paiement
**En tant que** Fatou (mère de famille périurbaine)  
**Je veux** configurer facilement mon mode de paiement mobile money  
**Afin de** pouvoir passer commande sans friction

**Critères d'acceptation :**
```gherkin
Given je configure mon profil
When je choisis "Mobile Money" comme paiement
Then je vois la liste des opérateurs disponibles
And je peux sélectionner mon opérateur (Orange Money, etc.)
And je saisis mon numéro mobile money

Given je valide ma configuration
When je teste un micro-paiement (100 FCFA)
Then je reçois une notification de confirmation
And mon mode de paiement est activé
And je peux ajouter d'autres modes si besoin
```

**Priorité :** Must Have | **Story Points :** 8

### 1.2 PREMIÈRE COMMANDE

#### US-C004 : Découverte du catalogue
**En tant qu'** Amina (professionnelle urbaine)  
**Je veux** parcourir facilement le catalogue de produits  
**Afin de** trouver ce dont j'ai besoin rapidement

**Critères d'acceptation :**
```gherkin
Given je suis sur l'écran d'accueil
When je navigue dans les catégories
Then je vois les catégories principales (Alimentaire, Pharmacie, etc.)
And chaque catégorie affiche le nombre de produits
And je peux voir les promotions en cours

Given je sélectionne une catégorie
When je parcours les produits
Then je vois photo, nom, prix, note, temps de livraison
And je peux filtrer par prix, distance, note
And je peux rechercher un produit spécifique
```

**Priorité :** Must Have | **Story Points :** 8

---

#### US-C005 : Ajout au panier avec contraintes locales
**En tant que** Kwame (étudiant urbain)  
**Je veux** ajouter des produits au panier en voyant les frais réels  
**Afin de** contrôler mon budget serré

**Critères d'acceptation :**
```gherkin
Given je consulte un produit
When j'ajoute le produit au panier
Then je vois le prix unitaire et total
And je vois les frais de livraison estimés
And je vois le minimum de commande si applicable
And je peux ajuster la quantité facilement

Given mon panier atteint le minimum
When je consulte le résumé
Then je vois le détail : produits + livraison + taxes
And je vois le temps de livraison estimé
And je peux appliquer un code promo si disponible
```

**Priorité :** Must Have | **Story Points :** 5

---

#### US-C006 : Validation commande avec options locales
**En tant que** Fatou (mère de famille périurbaine)  
**Je veux** valider ma commande avec des options adaptées  
**Afin de** m'assurer de recevoir mes produits dans de bonnes conditions

**Critères d'acceptation :**
```gherkin
Given je valide mon panier
When je choisis les options de livraison
Then je peux sélectionner le créneau horaire
And je peux choisir "Livraison standard" ou "Express"
And je peux ajouter des instructions spéciales
And je peux choisir "Appeler avant livraison"

Given je confirme ma commande
When je procède au paiement
Then je vois un récapitulatif final
And je confirme le paiement mobile money
And je reçois une confirmation immédiate
And je reçois un SMS de confirmation
```

**Priorité :** Must Have | **Story Points :** 8

### 1.3 SUIVI ET RÉCEPTION

#### US-C007 : Tracking temps réel adapté
**En tant qu'** Amina (professionnelle urbaine)  
**Je veux** suivre ma commande en temps réel  
**Afin de** planifier ma disponibilité pour la réception

**Critères d'acceptation :**
```gherkin
Given ma commande est confirmée
When je consulte le statut
Then je vois les étapes : Préparation > En route > Livré
And je vois la position du livreur sur une carte
And je vois le temps estimé d'arrivée
And je reçois des notifications à chaque étape

Given le livreur est proche
When il arrive dans ma zone
Then je reçois une notification "Arrivée dans 5 min"
And je peux contacter le livreur directement
And je peux partager ma localisation précise
```

**Priorité :** Must Have | **Story Points :** 13

---

#### US-C008 : Réception et évaluation
**En tant que** Kwame (étudiant urbain)  
**Je veux** recevoir ma commande et donner mon avis  
**Afin de** contribuer à la qualité du service

**Critères d'acceptation :**
```gherkin
Given le livreur arrive
When je reçois ma commande
Then je peux vérifier le contenu avant signature
And je peux signaler un problème immédiatement
And je confirme la réception dans l'app
And le paiement est finalisé automatiquement

Given ma commande est livrée
When je suis invité à évaluer
Then je peux noter le service (1-5 étoiles)
And je peux noter le livreur séparément
And je peux laisser un commentaire optionnel
And je peux prendre une photo si problème
```

**Priorité :** Should Have | **Story Points :** 5

### 1.4 FIDÉLISATION

#### US-C009 : Programme de fidélité adapté
**En tant que** Fatou (mère de famille périurbaine)  
**Je veux** bénéficier d'avantages pour ma fidélité  
**Afin de** réduire mes coûts de livraison

**Critères d'acceptation :**
```gherkin
Given je suis cliente régulière
When j'accumule des points
Then je gagne 1 point par 1000 FCFA dépensés
And je peux voir mon solde de points
And je peux échanger 100 points contre livraison gratuite
And je reçois des offres personnalisées

Given je parraine une amie
When elle fait sa première commande
Then nous recevons chacune 500 points bonus
And je peux suivre mes parrainages
And je reçois des bonus pour parrainages actifs
```

**Priorité :** Should Have | **Story Points :** 8

## 2. PARCOURS LIVREUR

### 2.1 INSCRIPTION ET ONBOARDING

#### US-L001 : Inscription livreur avec vérifications
**En tant que** David (livreur moto urbain)  
**Je veux** m'inscrire comme livreur avec un processus de vérification  
**Afin de** commencer à générer des revenus

**Critères d'acceptation :**
```gherkin
Given je veux devenir livreur
When je télécharge l'app livreur
Then je peux m'inscrire avec mes informations personnelles
And je dois fournir une photo de ma pièce d'identité
And je dois fournir une photo de mon permis de conduire
And je dois fournir une photo de ma moto et assurance

Given je soumets ma candidature
When l'équipe vérifie mes documents
Then je reçois une réponse sous 48h
And si accepté, je peux accéder à la formation
And si refusé, je reçois les raisons et peux corriger
```

**Priorité :** Must Have | **Story Points :** 13

---

#### US-L002 : Formation et certification
**En tant que** Grace (livreuse vélo urbaine)  
**Je veux** suivre une formation complète  
**Afin de** fournir un service de qualité et assurer ma sécurité

**Critères d'acceptation :**
```gherkin
Given je suis acceptée comme livreuse
When j'accède à la formation
Then je vois 5 modules : Sécurité, Service client, App, Réglementation, Urgences
And chaque module dure 10-15 minutes
And je peux suivre la formation à mon rythme
And je dois réussir un quiz final (80% minimum)

Given je termine la formation
When je valide le quiz
Then je reçois ma certification digitale
And je peux commencer à recevoir des commandes
And je reçois mon kit de démarrage (sac, casque si besoin)
```

**Priorité :** Must Have | **Story Points :** 8

### 2.2 GESTION QUOTIDIENNE

#### US-L003 : Activation et disponibilité
**En tant que** David (livreur moto urbain)  
**Je veux** gérer ma disponibilité facilement  
**Afin d'** optimiser mes revenus selon mon planning

**Critères d'acceptation :**
```gherkin
Given je commence ma journée
When j'ouvre l'app livreur
Then je peux activer mon statut "Disponible"
And je peux définir ma zone de travail préférée
And je peux voir la demande en temps réel par zone
And je peux estimer mes revenus potentiels

Given je veux faire une pause
When je change mon statut
Then je peux me mettre "En pause" temporairement
And je ne reçois plus de nouvelles commandes
And je peux reprendre quand je veux
And mes statistiques sont mises à jour
```

**Priorité :** Must Have | **Story Points :** 5

---

#### US-L004 : Réception et acceptation commandes
**En tant que** Grace (livreuse vélo urbaine)  
**Je veux** recevoir des commandes adaptées à mes capacités  
**Afin de** maximiser mon efficacité

**Critères d'acceptation :**
```gherkin
Given je suis disponible
When une commande arrive
Then je vois : distance, rémunération, type de produits, destination
And je vois le temps estimé pour la livraison
And j'ai 30 secondes pour accepter/refuser
And je peux voir l'historique du client (note)

Given j'accepte une commande
When je confirme
Then je reçois les détails complets
And je vois l'itinéraire optimisé
And je peux contacter le commerçant si besoin
And mon statut passe à "En mission"
```

**Priorité :** Must Have | **Story Points :** 8

---

#### US-L005 : Navigation et livraison
**En tant que** David (livreur moto urbain)  
**Je veux** être guidé efficacement vers les destinations  
**Afin de** livrer rapidement et en sécurité

**Critères d'acceptation :**
```gherkin
Given j'ai accepté une commande
When je me rends chez le commerçant
Then je suis guidé par GPS intégré
And je vois les alertes trafic en temps réel
And je peux signaler des problèmes de route
And j'arrive chez le commerçant

Given je récupère la commande
When je confirme la prise en charge
Then je vérifie le contenu avec le commerçant
And je confirme la prise en charge dans l'app
And le client est notifié automatiquement
And je suis guidé vers le client
```

**Priorité :** Must Have | **Story Points :** 13

### 2.3 GESTION FINANCIÈRE

#### US-L006 : Suivi des revenus en temps réel
**En tant que** Mamadou (livreur piéton rural)  
**Je veux** suivre mes revenus simplement  
**Afin de** comprendre mes gains et planifier

**Critères d'acceptation :**
```gherkin
Given je termine une livraison
When je consulte mes revenus
Then je vois mes gains du jour en temps réel
And je vois le détail par livraison
And je vois mes gains de la semaine
And je vois une projection mensuelle

Given je veux analyser ma performance
When je consulte les statistiques
Then je vois mes heures travaillées
And je vois mon taux d'acceptation
And je vois ma note moyenne client
And je vois des conseils pour améliorer mes revenus
```

**Priorité :** Must Have | **Story Points :** 8

---

#### US-L007 : Paiement et retrait
**En tant que** Grace (livreuse vélo urbaine)  
**Je veux** recevoir mes paiements rapidement  
**Afin de** gérer ma trésorerie étudiante

**Critères d'acceptation :**
```gherkin
Given j'ai des revenus accumulés
When je demande un retrait
Then je peux choisir paiement quotidien ou hebdomadaire
And je peux retirer vers mon mobile money
And je peux retirer vers mon compte bancaire
And je vois les frais de transaction clairement

Given je demande un retrait
When je confirme
Then je reçois le paiement sous 2h (mobile money)
And je reçois une confirmation SMS
And je peux voir l'historique de tous mes paiements
```

**Priorité :** Must Have | **Story Points :** 8

## 3. PARCOURS COMMERÇANT

### 3.1 ONBOARDING BUSINESS

#### US-M001 : Inscription commerçant
**En tant qu'** Aisha (restauratrice urbaine)  
**Je veux** inscrire mon restaurant sur la plateforme  
**Afin d'** augmenter mes ventes

**Critères d'acceptation :**
```gherkin
Given je veux rejoindre la plateforme
When je m'inscris comme commerçant
Then je fournis les informations de mon business
And je télécharge mes documents légaux
And je définis ma zone de livraison
And je configure mes horaires d'ouverture

Given ma candidature est soumise
When l'équipe vérifie mon dossier
Then je reçois une réponse sous 72h
And si accepté, je peux configurer mon catalogue
And je reçois une formation sur la plateforme
```

**Priorité :** Must Have | **Story Points :** 13

---

#### US-M002 : Configuration catalogue
**En tant que** Kofi (grossiste produits agricoles)  
**Je veux** créer mon catalogue de produits facilement  
**Afin de** présenter mon offre attractivement

**Critères d'acceptation :**
```gherkin
Given je configure mon catalogue
When j'ajoute un produit
Then je peux prendre une photo directement
And je saisis nom, description, prix
And je définis la disponibilité et stock
And je peux créer des variantes (taille, poids)

Given je finalise mon catalogue
When je publie mes produits
Then ils sont visibles aux clients immédiatement
And je peux modifier prix et disponibilité en temps réel
And je reçois des conseils pour optimiser mes fiches
```

**Priorité :** Must Have | **Story Points :** 8

### 3.2 GESTION OPÉRATIONNELLE

#### US-M003 : Réception et traitement commandes
**En tant qu'** Aisha (restauratrice urbaine)  
**Je veux** recevoir et traiter les commandes efficacement  
**Afin de** maintenir la qualité de service

**Critères d'acceptation :**
```gherkin
Given je reçois une commande
When elle arrive sur ma tablette
Then je vois tous les détails : produits, quantités, instructions
And je vois le temps de préparation estimé
And je peux accepter ou refuser la commande
And je peux ajuster le temps de préparation

Given j'accepte une commande
When je commence la préparation
Then je mets à jour le statut "En préparation"
And le client est notifié automatiquement
And je peux signaler si un produit manque
And je notifie quand la commande est prête
```

**Priorité :** Must Have | **Story Points :** 8

---

#### US-M004 : Gestion des stocks et disponibilité
**En tant que** Kofi (grossiste produits agricoles)  
**Je veux** gérer mes stocks en temps réel  
**Afin d'** éviter les ruptures et optimiser mes ventes

**Critères d'acceptation :**
```gherkin
Given je gère mon inventaire
When je mets à jour un stock
Then la disponibilité est mise à jour immédiatement
And les clients voient la disponibilité réelle
And je reçois des alertes de stock bas
And je peux désactiver temporairement un produit

Given mes stocks évoluent
When je vends des produits
Then les quantités sont déduites automatiquement
And je vois un tableau de bord de mes ventes
And je reçois des recommandations de réapprovisionnement
```

**Priorité :** Should Have | **Story Points :** 13

## 4. CAS D'USAGE EDGE CASES SPÉCIFIQUES AFRIQUE

### 4.1 CONNECTIVITÉ INTERMITTENTE

#### US-E001 : Mode offline intelligent
**En tant que** Joseph (senior rural)  
**Je veux** utiliser l'app même sans connexion stable  
**Afin de** passer commande malgré la connectivité limitée

**Critères d'acceptation :**
```gherkin
Given je n'ai pas de connexion internet
When j'ouvre l'application
Then je peux consulter le catalogue mis en cache
And je peux ajouter des produits au panier
And je vois un indicateur "Mode hors ligne"
And mes actions sont sauvegardées localement

Given ma connexion revient
When l'app se reconnecte
Then mes actions sont synchronisées automatiquement
And ma commande est envoyée si complète
And je reçois une confirmation de synchronisation
```

**Priorité :** Must Have | **Story Points :** 13

### 4.2 PAIEMENT MOBILE MONEY COMPLEXE

#### US-E002 : Gestion échecs paiement mobile money
**En tant que** Fatou (mère de famille périurbaine)  
**Je veux** gérer les problèmes de paiement mobile money  
**Afin de** finaliser ma commande malgré les difficultés techniques

**Critères d'acceptation :**
```gherkin
Given mon paiement mobile money échoue
When je reçois l'erreur
Then je vois un message clair expliquant le problème
And je peux réessayer immédiatement
And je peux changer de mode de paiement
And ma commande reste réservée 15 minutes

Given j'ai des problèmes récurrents
When je contacte le support
Then je peux être rappelée gratuitement
And un agent peut finaliser ma commande par téléphone
And je reçois des conseils pour éviter les problèmes futurs
```

**Priorité :** Must Have | **Story Points :** 8

### 4.3 LIVRAISON ZONES DIFFICILES

#### US-E003 : Livraison zones non cartographiées
**En tant que** Mamadou (livreur piéton rural)  
**Je veux** livrer dans des zones sans adresses précises  
**Afin de** servir tous les clients de ma région

**Critères d'acceptation :**
```gherkin
Given je dois livrer dans une zone non cartographiée
When j'accepte la commande
Then je peux contacter le client directement
And je peux partager ma position en temps réel
And le client peut me guider par téléphone
And je peux enregistrer des points de repère

Given je livre régulièrement dans cette zone
When je sauvegarde les informations
Then les futurs livreurs bénéficient de mes notes
And la zone devient progressivement mieux cartographiée
And je reçois un bonus pour contribution cartographie
```

**Priorité :** Should Have | **Story Points :** 13

## 5. SCÉNARIOS OFFLINE/ONLINE HYBRIDES

### 5.1 SYNCHRONISATION INTELLIGENTE

#### US-H001 : Synchronisation progressive
**En tant qu'** utilisateur avec connexion limitée  
**Je veux** que l'app synchronise intelligemment  
**Afin d'** économiser ma data et ma batterie

**Critères d'acceptation :**
```gherkin
Given j'ai une connexion limitée
When l'app synchronise
Then elle priorise les données critiques (commandes, paiements)
And elle compresse les images automatiquement
And elle synchronise en arrière-plan quand WiFi disponible
And elle me montre la consommation data

Given je suis en zone de faible signal
When l'app détecte la situation
Then elle passe automatiquement en mode économie
And elle réduit la fréquence des mises à jour
And elle cache plus de contenu localement
```

**Priorité :** Should Have | **Story Points :** 13

### 5.2 AGENTS PHYSIQUES INTÉGRÉS

#### US-H002 : Support via agents locaux
**En tant que** Joseph (senior rural)  
**Je veux** être aidé par un agent local  
**Afin de** utiliser le service malgré mes limitations technologiques

**Critères d'acceptation :**
```gherkin
Given j'ai besoin d'aide
When je contacte un agent local
Then il peut passer commande pour moi sur sa tablette
And il m'explique le processus étape par étape
And il peut encaisser mon paiement cash
And il me remet un reçu papier

Given l'agent traite ma commande
When elle est finalisée
Then je reçois un SMS de confirmation
And l'agent suit la livraison pour moi
And il me contacte quand le livreur arrive
```

**Priorité :** Could Have | **Story Points :** 21

## 6. CRITÈRES D'ACCEPTATION TRANSVERSAUX

### 6.1 Performance
- Temps de chargement < 3s sur 3G
- Taille app < 50MB
- Consommation batterie optimisée
- Fonctionnement sur Android 6+

### 6.2 Accessibilité
- Support langues locales
- Interface adaptée faible littératie
- Support vocal pour navigation
- Contraste élevé pour visibilité soleil

### 6.3 Sécurité
- Chiffrement données sensibles
- Authentification 2FA optionnelle
- Protection contre fraude
- Conformité réglementations locales

### 6.4 Monitoring
- Analytics comportement utilisateur
- Tracking performance technique
- Alertes incidents temps réel
- Feedback utilisateur intégré
