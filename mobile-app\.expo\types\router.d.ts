/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/../types/auth`; params?: Router.UnknownInputParams; } | { pathname: `/../types/orders`; params?: Router.UnknownInputParams; } | { pathname: `/../store/authStore`; params?: Router.UnknownInputParams; } | { pathname: `/../store/orderStore`; params?: Router.UnknownInputParams; } | { pathname: `/../components/ui/Button`; params?: Router.UnknownInputParams; } | { pathname: `/../components/ui/Input`; params?: Router.UnknownInputParams; } | { pathname: `/../components/maps/MapView`; params?: Router.UnknownInputParams; } | { pathname: `/../hooks/utils/useNotifications`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/../types/auth`; params?: Router.UnknownOutputParams; } | { pathname: `/../types/orders`; params?: Router.UnknownOutputParams; } | { pathname: `/../store/authStore`; params?: Router.UnknownOutputParams; } | { pathname: `/../store/orderStore`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/ui/Button`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/ui/Input`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/maps/MapView`; params?: Router.UnknownOutputParams; } | { pathname: `/../hooks/utils/useNotifications`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/../types/auth${`?${string}` | `#${string}` | ''}` | `/../types/orders${`?${string}` | `#${string}` | ''}` | `/../store/authStore${`?${string}` | `#${string}` | ''}` | `/../store/orderStore${`?${string}` | `#${string}` | ''}` | `/../components/ui/Button${`?${string}` | `#${string}` | ''}` | `/../components/ui/Input${`?${string}` | `#${string}` | ''}` | `/../components/maps/MapView${`?${string}` | `#${string}` | ''}` | `/../hooks/utils/useNotifications${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/login${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/explore${`?${string}` | `#${string}` | ''}` | `/explore${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/../types/auth`; params?: Router.UnknownInputParams; } | { pathname: `/../types/orders`; params?: Router.UnknownInputParams; } | { pathname: `/../store/authStore`; params?: Router.UnknownInputParams; } | { pathname: `/../store/orderStore`; params?: Router.UnknownInputParams; } | { pathname: `/../components/ui/Button`; params?: Router.UnknownInputParams; } | { pathname: `/../components/ui/Input`; params?: Router.UnknownInputParams; } | { pathname: `/../components/maps/MapView`; params?: Router.UnknownInputParams; } | { pathname: `/../hooks/utils/useNotifications`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/login` | `/login`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/explore` | `/explore`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | `/+not-found` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
