/**
 * @license
 * web-streams-polyfill v4.1.0
 * Copyright 2024 <PERSON><PERSON>, <PERSON><PERSON><PERSON> and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */
!function(){"use strict";function e(){}function t(e){return"object"==typeof e&&null!==e||"function"==typeof e}const r=e;function o(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}const n=Promise,a=Promise.resolve.bind(n),i=Promise.prototype.then,l=Promise.reject.bind(n),s=a;function u(e){return new n(e)}function c(e){return u((t=>t(e)))}function d(e){return l(e)}function f(e,t,r){return i.call(e,t,r)}function b(e,t,o){f(f(e,t,o),void 0,r)}function h(e,t){b(e,t)}function m(e,t){b(e,void 0,t)}function _(e,t,r){return f(e,t,r)}function p(e){f(e,void 0,r)}let y=e=>{if("function"==typeof queueMicrotask)y=queueMicrotask;else{const e=c(void 0);y=t=>f(e,t)}return y(e)};function S(e,t,r){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function g(e,t,r){try{return c(S(e,t,r))}catch(e){return d(e)}}class v{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let o=r+1;const n=e._elements,a=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,a}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(r=r._next,o=r._elements,t=0,0===o.length));)e(o[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}const w=Symbol("[[AbortSteps]]"),R=Symbol("[[ErrorSteps]]"),T=Symbol("[[CancelSteps]]"),C=Symbol("[[PullSteps]]"),P=Symbol("[[ReleaseSteps]]");function q(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?O(e):"closed"===t._state?function(e){O(e),A(e)}(e):j(e,t._storedError)}function E(e,t){return Br(e._ownerReadableStream,t)}function W(e){const t=e._ownerReadableStream;"readable"===t._state?k(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,t){j(e,t)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),t._readableStreamController[P](),t._reader=void 0,e._ownerReadableStream=void 0}function B(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function O(e){e._closedPromise=u(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function j(e,t){O(e),k(e,t)}function k(e,t){void 0!==e._closedPromise_reject&&(p(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function A(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const z=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},D=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function L(e,t){if(void 0!==e&&("object"!=typeof(r=e)&&"function"!=typeof r))throw new TypeError(`${t} is not an object.`);var r}function F(e,t){if("function"!=typeof e)throw new TypeError(`${t} is not a function.`)}function I(e,t){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function $(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function M(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function Y(e){return Number(e)}function Q(e){return 0===e?0:e}function x(e,t){const r=Number.MAX_SAFE_INTEGER;let o=Number(e);if(o=Q(o),!z(o))throw new TypeError(`${t} is not a finite number`);if(o=function(e){return Q(D(e))}(o),o<0||o>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return z(o)&&0!==o?o:0}function N(e,t){if(!Er(e))throw new TypeError(`${t} is not a ReadableStream.`)}function H(e){return new ReadableStreamDefaultReader(e)}function V(e,t){e._reader._readRequests.push(t)}function U(e,t,r){const o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function G(e){return e._reader._readRequests.length}function X(e){const t=e._reader;return void 0!==t&&!!J(t)}class ReadableStreamDefaultReader{constructor(e){if($(e,1,"ReadableStreamDefaultReader"),N(e,"First parameter"),Wr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");q(this,e),this._readRequests=new v}get closed(){return J(this)?this._closedPromise:d(ee("closed"))}cancel(e=void 0){return J(this)?void 0===this._ownerReadableStream?d(B("cancel")):E(this,e):d(ee("cancel"))}read(){if(!J(this))return d(ee("read"));if(void 0===this._ownerReadableStream)return d(B("read from"));let e,t;const r=u(((r,o)=>{e=r,t=o}));return K(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!J(this))throw ee("releaseLock");void 0!==this._ownerReadableStream&&function(e){W(e);const t=new TypeError("Reader was released");Z(e,t)}(this)}}function J(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof ReadableStreamDefaultReader)}function K(e,t){const r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[C](t)}function Z(e,t){const r=e._readRequests;e._readRequests=new v,r.forEach((e=>{e._errorSteps(t)}))}function ee(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}var te,re,oe;function ne(e){return e.slice()}function ae(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}Object.defineProperties(ReadableStreamDefaultReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),o(ReadableStreamDefaultReader.prototype.cancel,"cancel"),o(ReadableStreamDefaultReader.prototype.read,"read"),o(ReadableStreamDefaultReader.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamDefaultReader.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let ie=e=>(ie="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e,ie(e)),le=e=>(le="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength,le(e));function se(e,t,r){if(e.slice)return e.slice(t,r);const o=r-t,n=new ArrayBuffer(o);return ae(n,0,e,t,o),n}function ue(e,t){const r=e[t];if(null!=r){if("function"!=typeof r)throw new TypeError(`${String(t)} is not a function`);return r}}function ce(e){try{const t=e.done,r=e.value;return f(s(r),(e=>({done:t,value:e})))}catch(e){return d(e)}}const de=null!==(oe=null!==(te=Symbol.asyncIterator)&&void 0!==te?te:null===(re=Symbol.for)||void 0===re?void 0:re.call(Symbol,"Symbol.asyncIterator"))&&void 0!==oe?oe:"@@asyncIterator";function fe(e,r="sync",o){if(void 0===o)if("async"===r){if(void 0===(o=ue(e,de))){return function(e){const r={next(){let t;try{t=be(e)}catch(e){return d(e)}return ce(t)},return(r){let o;try{const t=ue(e.iterator,"return");if(void 0===t)return c({done:!0,value:r});o=S(t,e.iterator,[r])}catch(e){return d(e)}return t(o)?ce(o):d(new TypeError("The iterator.return() method must return an object"))}};return{iterator:r,nextMethod:r.next,done:!1}}(fe(e,"sync",ue(e,Symbol.iterator)))}}else o=ue(e,Symbol.iterator);if(void 0===o)throw new TypeError("The object is not iterable");const n=S(o,e,[]);if(!t(n))throw new TypeError("The iterator method must return an object");return{iterator:n,nextMethod:n.next,done:!1}}function be(e){const r=S(e.nextMethod,e.iterator,[]);if(!t(r))throw new TypeError("The iterator.next() method must return an object");return r}class he{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?_(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise=this._ongoingPromise?_(this._ongoingPromise,t,t):t(),this._ongoingPromise}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;let t,r;const o=u(((e,o)=>{t=e,r=o}));return K(e,{_chunkSteps:e=>{this._ongoingPromise=void 0,y((()=>t({value:e,done:!1})))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,W(e),t({value:void 0,done:!0})},_errorSteps:t=>{this._ongoingPromise=void 0,this._isFinished=!0,W(e),r(t)}}),o}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(!this._preventCancel){const r=E(t,e);return W(t),_(r,(()=>({value:e,done:!0})))}return W(t),c({value:e,done:!0})}}const me={next(){return _e(this)?this._asyncIteratorImpl.next():d(pe("next"))},return(e){return _e(this)?this._asyncIteratorImpl.return(e):d(pe("return"))},[de](){return this}};function _e(e){if(!t(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof he}catch(e){return!1}}function pe(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.defineProperty(me,de,{enumerable:!1});const ye=Number.isNaN||function(e){return e!=e};function Se(e){const t=se(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function ge(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function ve(e,t,r){if("number"!=typeof(o=r)||ye(o)||o<0||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var o;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function we(e){e._queue=new v,e._queueTotalSize=0}function Re(e){return e===DataView}class ReadableStreamBYOBRequest{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Ce(this))throw Ke("view");return this._view}respond(e){if(!Ce(this))throw Ke("respond");if($(e,1,"respond"),e=x(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(le(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");Ge(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!Ce(this))throw Ke("respondWithNewView");if($(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(le(e.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");Xe(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ReadableStreamBYOBRequest.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),o(ReadableStreamBYOBRequest.prototype.respond,"respond"),o(ReadableStreamBYOBRequest.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamBYOBRequest.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class ReadableByteStreamController{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!Te(this))throw Ze("byobRequest");return Ve(this)}get desiredSize(){if(!Te(this))throw Ze("desiredSize");return Ue(this)}close(){if(!Te(this))throw Ze("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);Qe(this)}enqueue(e){if(!Te(this))throw Ze("enqueue");if($(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const t=this._controlledReadableByteStream._state;if("readable"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);xe(this,e)}error(e=void 0){if(!Te(this))throw Ze("error");Ne(this,e)}[T](e){qe(this),we(this);const t=this._cancelAlgorithm(e);return Ye(this),t}[C](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void He(this,e);const r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}V(t,e),Pe(this)}[P](){if(this._pendingPullIntos.length>0){const e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new v,this._pendingPullIntos.push(e)}}}function Te(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof ReadableByteStreamController)}function Ce(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof ReadableStreamBYOBRequest)}function Pe(e){const t=function(e){const t=e._controlledReadableByteStream;if("readable"!==t._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(X(t)&&G(t)>0)return!0;if(nt(t)&&ot(t)>0)return!0;const r=Ue(e);if(r>0)return!0;return!1}(e);if(!t)return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;b(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Pe(e)),null)),(t=>(Ne(e,t),null)))}function qe(e){Le(e),e._pendingPullIntos=new v}function Ee(e,t){let r=!1;"closed"===e._state&&(r=!0);const o=Be(t);"default"===t.readerType?U(e,o,r):function(e,t,r){const o=e._reader,n=o._readIntoRequests.shift();r?n._closeSteps(t):n._chunkSteps(t)}(e,o,r)}function We(e,t){for(let r=0;r<t.length;++r)Ee(e,t[r])}function Be(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function Oe(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function je(e,t,r,o){let n;try{n=se(t,r,r+o)}catch(t){throw Ne(e,t),t}Oe(e,n,0,o)}function ke(e,t){t.bytesFilled>0&&je(e,t.buffer,t.byteOffset,t.bytesFilled),Me(e)}function Ae(e,t){const r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),o=t.bytesFilled+r;let n=r,a=!1;const i=o-o%t.elementSize;i>=t.minimumFill&&(n=i-t.bytesFilled,a=!0);const l=e._queue;for(;n>0;){const r=l.peek(),o=Math.min(n,r.byteLength),a=t.byteOffset+t.bytesFilled;ae(t.buffer,a,r.buffer,r.byteOffset,o),r.byteLength===o?l.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,ze(e,o,t),n-=o}return a}function ze(e,t,r){r.bytesFilled+=t}function De(e){0===e._queueTotalSize&&e._closeRequested?(Ye(e),Or(e._controlledReadableByteStream)):Pe(e)}function Le(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Fe(e){const t=[];for(;e._pendingPullIntos.length>0&&0!==e._queueTotalSize;){const r=e._pendingPullIntos.peek();Ae(e,r)&&(Me(e),t.push(r))}return t}function Ie(e,t,r,o){const n=e._controlledReadableByteStream,a=t.constructor,i=function(e){return Re(e)?1:e.BYTES_PER_ELEMENT}(a),{byteOffset:l,byteLength:s}=t,u=r*i;let c;try{c=ie(t.buffer)}catch(e){return void o._errorSteps(e)}const d={buffer:c,bufferByteLength:c.byteLength,byteOffset:l,byteLength:s,bytesFilled:0,minimumFill:u,elementSize:i,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(d),void rt(n,o);if("closed"!==n._state){if(e._queueTotalSize>0){if(Ae(e,d)){const t=Be(d);return De(e),void o._chunkSteps(t)}if(e._closeRequested){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");return Ne(e,t),void o._errorSteps(t)}}e._pendingPullIntos.push(d),rt(n,o),Pe(e)}else{const e=new a(d.buffer,d.byteOffset,0);o._closeSteps(e)}}function $e(e,t){const r=e._pendingPullIntos.peek();Le(e);"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&Me(e);const r=e._controlledReadableByteStream;if(nt(r)){const t=[];for(let o=0;o<ot(r);++o)t.push(Me(e));We(r,t)}}(e,r):function(e,t,r){if(ze(0,t,r),"none"===r.readerType){ke(e,r);const t=Fe(e);return void We(e._controlledReadableByteStream,t)}if(r.bytesFilled<r.minimumFill)return;Me(e);const o=r.bytesFilled%r.elementSize;if(o>0){const t=r.byteOffset+r.bytesFilled;je(e,r.buffer,t-o,o)}r.bytesFilled-=o;const n=Fe(e);Ee(e._controlledReadableByteStream,r),We(e._controlledReadableByteStream,n)}(e,t,r),Pe(e)}function Me(e){return e._pendingPullIntos.shift()}function Ye(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Qe(e){const t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Ne(e,t),t}}Ye(e),Or(t)}}function xe(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;const{buffer:o,byteOffset:n,byteLength:a}=t;if(le(o))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");const i=ie(o);if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(le(t.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");Le(e),t.buffer=ie(t.buffer),"none"===t.readerType&&ke(e,t)}if(X(r))if(function(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;He(e,t._readRequests.shift())}}(e),0===G(r))Oe(e,i,n,a);else{e._pendingPullIntos.length>0&&Me(e);U(r,new Uint8Array(i,n,a),!1)}else if(nt(r)){Oe(e,i,n,a);const t=Fe(e);We(e._controlledReadableByteStream,t)}else Oe(e,i,n,a);Pe(e)}function Ne(e,t){const r=e._controlledReadableByteStream;"readable"===r._state&&(qe(e),we(e),Ye(e),jr(r,t))}function He(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,De(e);const o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function Ve(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(ReadableStreamBYOBRequest.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(o,e,r),e._byobRequest=o}return e._byobRequest}function Ue(e){const t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function Ge(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=ie(r.buffer),$e(e,t)}function Xe(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const o=t.byteLength;r.buffer=ie(t.buffer),$e(e,o)}function Je(e,t,r,o,n,a,i){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,we(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=i,t._pendingPullIntos=new v,e._readableStreamController=t;b(c(r()),(()=>(t._started=!0,Pe(t),null)),(e=>(Ne(t,e),null)))}function Ke(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function Ze(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function et(e,t){if("byob"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function tt(e){return new ReadableStreamBYOBReader(e)}function rt(e,t){e._reader._readIntoRequests.push(t)}function ot(e){return e._reader._readIntoRequests.length}function nt(e){const t=e._reader;return void 0!==t&&!!at(t)}Object.defineProperties(ReadableByteStreamController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),o(ReadableByteStreamController.prototype.close,"close"),o(ReadableByteStreamController.prototype.enqueue,"enqueue"),o(ReadableByteStreamController.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableByteStreamController.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class ReadableStreamBYOBReader{constructor(e){if($(e,1,"ReadableStreamBYOBReader"),N(e,"First parameter"),Wr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Te(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");q(this,e),this._readIntoRequests=new v}get closed(){return at(this)?this._closedPromise:d(st("closed"))}cancel(e=void 0){return at(this)?void 0===this._ownerReadableStream?d(B("cancel")):E(this,e):d(st("cancel"))}read(e,t={}){if(!at(this))return d(st("read"));if(!ArrayBuffer.isView(e))return d(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return d(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return d(new TypeError("view's buffer must have non-zero byteLength"));if(le(e.buffer))return d(new TypeError("view's buffer has been detached"));let r;try{r=function(e,t){var r;return L(e,t),{min:x(null!==(r=null==e?void 0:e.min)&&void 0!==r?r:1,`${t} has member 'min' that`)}}(t,"options")}catch(e){return d(e)}const o=r.min;if(0===o)return d(new TypeError("options.min must be greater than 0"));if(function(e){return Re(e.constructor)}(e)){if(o>e.byteLength)return d(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(o>e.length)return d(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return d(B("read from"));let n,a;const i=u(((e,t)=>{n=e,a=t}));return it(this,e,o,{_chunkSteps:e=>n({value:e,done:!1}),_closeSteps:e=>n({value:e,done:!0}),_errorSteps:e=>a(e)}),i}releaseLock(){if(!at(this))throw st("releaseLock");void 0!==this._ownerReadableStream&&function(e){W(e);const t=new TypeError("Reader was released");lt(e,t)}(this)}}function at(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof ReadableStreamBYOBReader)}function it(e,t,r,o){const n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?o._errorSteps(n._storedError):Ie(n._readableStreamController,t,r,o)}function lt(e,t){const r=e._readIntoRequests;e._readIntoRequests=new v,r.forEach((e=>{e._errorSteps(t)}))}function st(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function ut(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(ye(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}function ct(e){const{size:t}=e;return t||(()=>1)}function dt(e,t){L(e,t);const r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:Y(r),size:void 0===o?void 0:ft(o,`${t} has member 'size' that`)}}function ft(e,t){return F(e,t),t=>Y(e(t))}function bt(e,t,r){return F(e,r),r=>g(e,t,[r])}function ht(e,t,r){return F(e,r),()=>g(e,t,[])}function mt(e,t,r){return F(e,r),r=>S(e,t,[r])}function _t(e,t,r){return F(e,r),(r,o)=>g(e,t,[r,o])}function pt(e,t){if(!gt(e))throw new TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(ReadableStreamBYOBReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),o(ReadableStreamBYOBReader.prototype.cancel,"cancel"),o(ReadableStreamBYOBReader.prototype.read,"read"),o(ReadableStreamBYOBReader.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamBYOBReader.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});class WritableStream{constructor(e={},t={}){void 0===e?e=null:I(e,"First parameter");const r=dt(t,"Second parameter"),o=function(e,t){L(e,t);const r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===r?void 0:bt(r,e,`${t} has member 'abort' that`),close:void 0===o?void 0:ht(o,e,`${t} has member 'close' that`),start:void 0===n?void 0:mt(n,e,`${t} has member 'start' that`),write:void 0===i?void 0:_t(i,e,`${t} has member 'write' that`),type:a}}(e,"First parameter");St(this);if(void 0!==o.type)throw new RangeError("Invalid type is specified");const n=ct(r);!function(e,t,r,o){const n=Object.create(WritableStreamDefaultController.prototype);let a,i,l,s;a=void 0!==t.start?()=>t.start(n):()=>{};i=void 0!==t.write?e=>t.write(e,n):()=>c(void 0);l=void 0!==t.close?()=>t.close():()=>c(void 0);s=void 0!==t.abort?e=>t.abort(e):()=>c(void 0);Ft(e,n,a,i,l,s,r,o)}(this,o,ut(r,1),n)}get locked(){if(!gt(this))throw Nt("locked");return vt(this)}abort(e=void 0){return gt(this)?vt(this)?d(new TypeError("Cannot abort a stream that already has a writer")):wt(this,e):d(Nt("abort"))}close(){return gt(this)?vt(this)?d(new TypeError("Cannot close a stream that already has a writer")):qt(this)?d(new TypeError("Cannot close an already-closing stream")):Rt(this):d(Nt("close"))}getWriter(){if(!gt(this))throw Nt("getWriter");return yt(this)}}function yt(e){return new WritableStreamDefaultWriter(e)}function St(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new v,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function gt(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof WritableStream)}function vt(e){return void 0!==e._writer}function wt(e,t){var r;if("closed"===e._state||"errored"===e._state)return c(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);const o=e._state;if("closed"===o||"errored"===o)return c(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;"erroring"===o&&(n=!0,t=void 0);const a=u(((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=a,n||Ct(e,t),a}function Rt(e){const t=e._state;if("closed"===t||"errored"===t)return d(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=u(((t,r)=>{const o={_resolve:t,_reject:r};e._closeRequest=o})),o=e._writer;var n;return void 0!==o&&e._backpressure&&"writable"===t&&or(o),ve(n=e._writableStreamController,Dt,0),Mt(n),r}function Tt(e,t){"writable"!==e._state?Pt(e):Ct(e,t)}function Ct(e,t){const r=e._writableStreamController;e._state="erroring",e._storedError=t;const o=e._writer;void 0!==o&&kt(o,t),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&r._started&&Pt(e)}function Pt(e){e._state="errored",e._writableStreamController[R]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new v,void 0===e._pendingAbortRequest)return void Et(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void Et(e);b(e._writableStreamController[w](r._reason),(()=>(r._resolve(),Et(e),null)),(t=>(r._reject(t),Et(e),null)))}function qt(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function Et(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&Jt(t,e._storedError)}function Wt(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){Zt(e)}(r):or(r)),e._backpressure=t}Object.defineProperties(WritableStream.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),o(WritableStream.prototype.abort,"abort"),o(WritableStream.prototype.close,"close"),o(WritableStream.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStream.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class WritableStreamDefaultWriter{constructor(e){if($(e,1,"WritableStreamDefaultWriter"),pt(e,"First parameter"),vt(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;const t=e._state;if("writable"===t)!qt(e)&&e._backpressure?Zt(this):tr(this),Gt(this);else if("erroring"===t)er(this,e._storedError),Gt(this);else if("closed"===t)tr(this),Gt(r=this),Kt(r);else{const t=e._storedError;er(this,t),Xt(this,t)}var r}get closed(){return Bt(this)?this._closedPromise:d(Vt("closed"))}get desiredSize(){if(!Bt(this))throw Vt("desiredSize");if(void 0===this._ownerWritableStream)throw Ut("desiredSize");return function(e){const t=e._ownerWritableStream,r=t._state;if("errored"===r||"erroring"===r)return null;if("closed"===r)return 0;return $t(t._writableStreamController)}(this)}get ready(){return Bt(this)?this._readyPromise:d(Vt("ready"))}abort(e=void 0){return Bt(this)?void 0===this._ownerWritableStream?d(Ut("abort")):function(e,t){return wt(e._ownerWritableStream,t)}(this,e):d(Vt("abort"))}close(){if(!Bt(this))return d(Vt("close"));const e=this._ownerWritableStream;return void 0===e?d(Ut("close")):qt(e)?d(new TypeError("Cannot close an already-closing stream")):Ot(this)}releaseLock(){if(!Bt(this))throw Vt("releaseLock");void 0!==this._ownerWritableStream&&At(this)}write(e=void 0){return Bt(this)?void 0===this._ownerWritableStream?d(Ut("write to")):zt(this,e):d(Vt("write"))}}function Bt(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof WritableStreamDefaultWriter)}function Ot(e){return Rt(e._ownerWritableStream)}function jt(e,t){"pending"===e._closedPromiseState?Jt(e,t):function(e,t){Xt(e,t)}(e,t)}function kt(e,t){"pending"===e._readyPromiseState?rr(e,t):function(e,t){er(e,t)}(e,t)}function At(e){const t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");kt(e,r),jt(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function zt(e,t){const r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){if(void 0===e._strategySizeAlgorithm)return 1;try{return e._strategySizeAlgorithm(t)}catch(t){return Yt(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return d(Ut("write to"));const a=r._state;if("errored"===a)return d(r._storedError);if(qt(r)||"closed"===a)return d(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return d(r._storedError);const i=function(e){return u(((t,r)=>{const o={_resolve:t,_reject:r};e._writeRequests.push(o)}))}(r);return function(e,t,r){try{ve(e,t,r)}catch(t){return void Yt(e,t)}const o=e._controlledWritableStream;if(!qt(o)&&"writable"===o._state){Wt(o,Qt(e))}Mt(e)}(o,t,n),i}Object.defineProperties(WritableStreamDefaultWriter.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),o(WritableStreamDefaultWriter.prototype.abort,"abort"),o(WritableStreamDefaultWriter.prototype.close,"close"),o(WritableStreamDefaultWriter.prototype.releaseLock,"releaseLock"),o(WritableStreamDefaultWriter.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStreamDefaultWriter.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});const Dt={};class WritableStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Lt(this))throw Ht("abortReason");return this._abortReason}get signal(){if(!Lt(this))throw Ht("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e=void 0){if(!Lt(this))throw Ht("error");"writable"===this._controlledWritableStream._state&&xt(this,e)}[w](e){const t=this._abortAlgorithm(e);return It(this),t}[R](){we(this)}}function Lt(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof WritableStreamDefaultController)}function Ft(e,t,r,o,n,a,i,l){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,we(t),t._abortReason=void 0,t._abortController=function(){if("function"==typeof AbortController)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=l,t._strategyHWM=i,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=a;const s=Qt(t);Wt(e,s);b(c(r()),(()=>(t._started=!0,Mt(t),null)),(r=>(t._started=!0,Tt(e,r),null)))}function It(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function $t(e){return e._strategyHWM-e._queueTotalSize}function Mt(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void Pt(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===Dt?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),ge(e);const r=e._closeAlgorithm();It(e),b(r,(()=>(function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";const t=e._writer;void 0!==t&&Kt(t)}(t),null)),(e=>(function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Tt(e,t)}(t,e),null)))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r);const o=e._writeAlgorithm(t);b(o,(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(ge(e),!qt(r)&&"writable"===t){const t=Qt(e);Wt(r,t)}return Mt(e),null}),(t=>("writable"===r._state&&It(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Tt(e,t)}(r,t),null)))}(e,r)}function Yt(e,t){"writable"===e._controlledWritableStream._state&&xt(e,t)}function Qt(e){return $t(e)<=0}function xt(e,t){const r=e._controlledWritableStream;It(e),Ct(r,t)}function Nt(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function Ht(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function Vt(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function Ut(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Gt(e){e._closedPromise=u(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function Xt(e,t){Gt(e),Jt(e,t)}function Jt(e,t){void 0!==e._closedPromise_reject&&(p(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Kt(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function Zt(e){e._readyPromise=u(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function er(e,t){Zt(e),rr(e,t)}function tr(e){Zt(e),or(e)}function rr(e,t){void 0!==e._readyPromise_reject&&(p(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function or(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(WritableStreamDefaultController.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStreamDefaultController.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});const nr="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0;const ar=function(){const e=null==nr?void 0:nr.DOMException;return function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;if("DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?e:void 0}()||function(){const e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return o(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function ir(t,r,o,n,a,i){const l=H(t),s=yt(r);t._disturbed=!0;let _=!1,y=c(void 0);return u(((S,g)=>{let v;if(void 0!==i){if(v=()=>{const e=void 0!==i.reason?i.reason:new ar("Aborted","AbortError"),o=[];n||o.push((()=>"writable"===r._state?wt(r,e):c(void 0))),a||o.push((()=>"readable"===t._state?Br(t,e):c(void 0))),q((()=>Promise.all(o.map((e=>e())))),!0,e)},i.aborted)return void v();i.addEventListener("abort",v)}var w,R,T;if(P(t,l._closedPromise,(e=>(n?E(!0,e):q((()=>wt(r,e)),!0,e),null))),P(r,s._closedPromise,(e=>(a?E(!0,e):q((()=>Br(t,e)),!0,e),null))),w=t,R=l._closedPromise,T=()=>(o?E():q((()=>function(e){const t=e._ownerWritableStream,r=t._state;return qt(t)||"closed"===r?c(void 0):"errored"===r?d(t._storedError):Ot(e)}(s))),null),"closed"===w._state?T():h(R,T),qt(r)||"closed"===r._state){const e=new TypeError("the destination writable stream closed before all data could be piped to it");a?E(!0,e):q((()=>Br(t,e)),!0,e)}function C(){const e=y;return f(y,(()=>e!==y?C():void 0))}function P(e,t,r){"errored"===e._state?r(e._storedError):m(t,r)}function q(e,t,o){function n(){return b(e(),(()=>B(t,o)),(e=>B(!0,e))),null}_||(_=!0,"writable"!==r._state||qt(r)?n():h(C(),n))}function E(e,t){_||(_=!0,"writable"!==r._state||qt(r)?B(e,t):h(C(),(()=>B(e,t))))}function B(e,t){return At(s),W(l),void 0!==i&&i.removeEventListener("abort",v),e?g(t):S(void 0),null}p(u(((t,r)=>{!function o(n){n?t():f(_?c(!0):f(s._readyPromise,(()=>u(((t,r)=>{K(l,{_chunkSteps:r=>{y=f(zt(s,r),void 0,e),t(!1)},_closeSteps:()=>t(!0),_errorSteps:r})})))),o,r)}(!1)})))}))}class ReadableStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!lr(this))throw pr("desiredSize");return hr(this)}close(){if(!lr(this))throw pr("close");if(!mr(this))throw new TypeError("The stream is not in a state that permits close");dr(this)}enqueue(e=void 0){if(!lr(this))throw pr("enqueue");if(!mr(this))throw new TypeError("The stream is not in a state that permits enqueue");return fr(this,e)}error(e=void 0){if(!lr(this))throw pr("error");br(this,e)}[T](e){we(this);const t=this._cancelAlgorithm(e);return cr(this),t}[C](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=ge(this);this._closeRequested&&0===this._queue.length?(cr(this),Or(t)):sr(this),e._chunkSteps(r)}else V(t,e),sr(this)}[P](){}}function lr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof ReadableStreamDefaultController)}function sr(e){if(!ur(e))return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;b(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,sr(e)),null)),(t=>(br(e,t),null)))}function ur(e){const t=e._controlledReadableStream;if(!mr(e))return!1;if(!e._started)return!1;if(Wr(t)&&G(t)>0)return!0;return hr(e)>0}function cr(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function dr(e){if(!mr(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(cr(e),Or(t))}function fr(e,t){if(!mr(e))return;const r=e._controlledReadableStream;if(Wr(r)&&G(r)>0)U(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw br(e,t),t}try{ve(e,t,r)}catch(t){throw br(e,t),t}}sr(e)}function br(e,t){const r=e._controlledReadableStream;"readable"===r._state&&(we(e),cr(e),jr(r,t))}function hr(e){const t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function mr(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function _r(e,t,r,o,n,a,i){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,we(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=i,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t;b(c(r()),(()=>(t._started=!0,sr(t),null)),(e=>(br(t,e),null)))}function pr(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function yr(e,t){return Te(e._readableStreamController)?function(e){let t,r,o,n,a,i=H(e),l=!1,s=!1,d=!1,f=!1,b=!1;const h=u((e=>{a=e}));function _(e){m(e._closedPromise,(t=>(e!==i||(Ne(o._readableStreamController,t),Ne(n._readableStreamController,t),f&&b||a(void 0)),null)))}function p(){at(i)&&(W(i),i=H(e),_(i));K(i,{_chunkSteps:t=>{y((()=>{s=!1,d=!1;const r=t;let i=t;if(!f&&!b)try{i=Se(t)}catch(t){return Ne(o._readableStreamController,t),Ne(n._readableStreamController,t),void a(Br(e,t))}f||xe(o._readableStreamController,r),b||xe(n._readableStreamController,i),l=!1,s?g():d&&v()}))},_closeSteps:()=>{l=!1,f||Qe(o._readableStreamController),b||Qe(n._readableStreamController),o._readableStreamController._pendingPullIntos.length>0&&Ge(o._readableStreamController,0),n._readableStreamController._pendingPullIntos.length>0&&Ge(n._readableStreamController,0),f&&b||a(void 0)},_errorSteps:()=>{l=!1}})}function S(t,r){J(i)&&(W(i),i=tt(e),_(i));const u=r?n:o,c=r?o:n;it(i,t,1,{_chunkSteps:t=>{y((()=>{s=!1,d=!1;const o=r?b:f;if(r?f:b)o||Xe(u._readableStreamController,t);else{let r;try{r=Se(t)}catch(t){return Ne(u._readableStreamController,t),Ne(c._readableStreamController,t),void a(Br(e,t))}o||Xe(u._readableStreamController,t),xe(c._readableStreamController,r)}l=!1,s?g():d&&v()}))},_closeSteps:e=>{l=!1;const t=r?b:f,o=r?f:b;t||Qe(u._readableStreamController),o||Qe(c._readableStreamController),void 0!==e&&(t||Xe(u._readableStreamController,e),!o&&c._readableStreamController._pendingPullIntos.length>0&&Ge(c._readableStreamController,0)),t&&o||a(void 0)},_errorSteps:()=>{l=!1}})}function g(){if(l)return s=!0,c(void 0);l=!0;const e=Ve(o._readableStreamController);return null===e?p():S(e._view,!1),c(void 0)}function v(){if(l)return d=!0,c(void 0);l=!0;const e=Ve(n._readableStreamController);return null===e?p():S(e._view,!0),c(void 0)}function w(o){if(f=!0,t=o,b){const o=ne([t,r]),n=Br(e,o);a(n)}return h}function R(o){if(b=!0,r=o,f){const o=ne([t,r]),n=Br(e,o);a(n)}return h}function T(){}return o=Pr(T,g,w),n=Pr(T,v,R),_(i),[o,n]}(e):function(e,t){const r=H(e);let o,n,a,i,l,s=!1,d=!1,f=!1,b=!1;const h=u((e=>{l=e}));function _(){if(s)return d=!0,c(void 0);s=!0;return K(r,{_chunkSteps:e=>{y((()=>{d=!1;const t=e,r=e;f||fr(a._readableStreamController,t),b||fr(i._readableStreamController,r),s=!1,d&&_()}))},_closeSteps:()=>{s=!1,f||dr(a._readableStreamController),b||dr(i._readableStreamController),f&&b||l(void 0)},_errorSteps:()=>{s=!1}}),c(void 0)}function p(t){if(f=!0,o=t,b){const t=ne([o,n]),r=Br(e,t);l(r)}return h}function S(t){if(b=!0,n=t,f){const t=ne([o,n]),r=Br(e,t);l(r)}return h}function g(){}return a=Cr(g,_,p),i=Cr(g,_,S),m(r._closedPromise,(e=>(br(a._readableStreamController,e),br(i._readableStreamController,e),f&&b||l(void 0),null))),[a,i]}(e)}function Sr(r){return t(o=r)&&void 0!==o.getReader?function(r){let o;function n(){let e;try{e=r.read()}catch(e){return d(e)}return _(e,(e=>{if(!t(e))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)dr(o._readableStreamController);else{const t=e.value;fr(o._readableStreamController,t)}}))}function a(e){try{return c(r.cancel(e))}catch(e){return d(e)}}return o=Cr(e,n,a,0),o}(r.getReader()):function(r){let o;const n=fe(r,"async");function a(){let e;try{e=be(n)}catch(e){return d(e)}return _(c(e),(e=>{if(!t(e))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)dr(o._readableStreamController);else{const t=e.value;fr(o._readableStreamController,t)}}))}function i(e){const r=n.iterator;let o;try{o=ue(r,"return")}catch(e){return d(e)}if(void 0===o)return c(void 0);return _(g(o,r,[e]),(e=>{if(!t(e))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}return o=Cr(e,a,i,0),o}(r);var o}function gr(e,t,r){return F(e,r),r=>g(e,t,[r])}function vr(e,t,r){return F(e,r),r=>g(e,t,[r])}function wr(e,t,r){return F(e,r),r=>S(e,t,[r])}function Rr(e,t){if("bytes"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function Tr(e,t){L(e,t);const r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(a,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(o),preventClose:Boolean(n),signal:a}}Object.defineProperties(ReadableStreamDefaultController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),o(ReadableStreamDefaultController.prototype.close,"close"),o(ReadableStreamDefaultController.prototype.enqueue,"enqueue"),o(ReadableStreamDefaultController.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamDefaultController.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class ReadableStream{constructor(e={},t={}){void 0===e?e=null:I(e,"First parameter");const r=dt(t,"Second parameter"),o=function(e,t){L(e,t);const r=e,o=null==r?void 0:r.autoAllocateChunkSize,n=null==r?void 0:r.cancel,a=null==r?void 0:r.pull,i=null==r?void 0:r.start,l=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===o?void 0:x(o,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:gr(n,r,`${t} has member 'cancel' that`),pull:void 0===a?void 0:vr(a,r,`${t} has member 'pull' that`),start:void 0===i?void 0:wr(i,r,`${t} has member 'start' that`),type:void 0===l?void 0:Rr(l,`${t} has member 'type' that`)}}(e,"First parameter");if(qr(this),"bytes"===o.type){if(void 0!==r.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){const o=Object.create(ReadableByteStreamController.prototype);let n,a,i;n=void 0!==t.start?()=>t.start(o):()=>{},a=void 0!==t.pull?()=>t.pull(o):()=>c(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>c(void 0);const l=t.autoAllocateChunkSize;if(0===l)throw new TypeError("autoAllocateChunkSize must be greater than 0");Je(e,o,n,a,i,r,l)}(this,o,ut(r,0))}else{const e=ct(r);!function(e,t,r,o){const n=Object.create(ReadableStreamDefaultController.prototype);let a,i,l;a=void 0!==t.start?()=>t.start(n):()=>{},i=void 0!==t.pull?()=>t.pull(n):()=>c(void 0),l=void 0!==t.cancel?e=>t.cancel(e):()=>c(void 0),_r(e,n,a,i,l,r,o)}(this,o,ut(r,1),e)}}get locked(){if(!Er(this))throw kr("locked");return Wr(this)}cancel(e=void 0){return Er(this)?Wr(this)?d(new TypeError("Cannot cancel a stream that already has a reader")):Br(this,e):d(kr("cancel"))}getReader(e=void 0){if(!Er(this))throw kr("getReader");return void 0===function(e,t){L(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:et(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?H(this):tt(this)}pipeThrough(e,t={}){if(!Er(this))throw kr("pipeThrough");$(e,1,"pipeThrough");const r=function(e,t){L(e,t);const r=null==e?void 0:e.readable;M(r,"readable","ReadableWritablePair"),N(r,`${t} has member 'readable' that`);const o=null==e?void 0:e.writable;return M(o,"writable","ReadableWritablePair"),pt(o,`${t} has member 'writable' that`),{readable:r,writable:o}}(e,"First parameter"),o=Tr(t,"Second parameter");if(Wr(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(vt(r.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return p(ir(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){if(!Er(this))return d(kr("pipeTo"));if(void 0===e)return d("Parameter 1 is required in 'pipeTo'.");if(!gt(e))return d(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let r;try{r=Tr(t,"Second parameter")}catch(e){return d(e)}return Wr(this)?d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):vt(e)?d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):ir(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!Er(this))throw kr("tee");return ne(yr(this))}values(e=void 0){if(!Er(this))throw kr("values");return function(e,t){const r=H(e),o=new he(r,t),n=Object.create(me);return n._asyncIteratorImpl=o,n}(this,function(e,t){L(e,t);const r=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(r)}}(e,"First parameter").preventCancel)}[de](e){return this.values(e)}static from(e){return Sr(e)}}function Cr(e,t,r,o=1,n=(()=>1)){const a=Object.create(ReadableStream.prototype);qr(a);return _r(a,Object.create(ReadableStreamDefaultController.prototype),e,t,r,o,n),a}function Pr(e,t,r){const o=Object.create(ReadableStream.prototype);qr(o);return Je(o,Object.create(ReadableByteStreamController.prototype),e,t,r,0,void 0),o}function qr(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Er(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof ReadableStream)}function Wr(e){return void 0!==e._reader}function Br(t,r){if(t._disturbed=!0,"closed"===t._state)return c(void 0);if("errored"===t._state)return d(t._storedError);Or(t);const o=t._reader;if(void 0!==o&&at(o)){const e=o._readIntoRequests;o._readIntoRequests=new v,e.forEach((e=>{e._closeSteps(void 0)}))}return _(t._readableStreamController[T](r),e)}function Or(e){e._state="closed";const t=e._reader;if(void 0!==t&&(A(t),J(t))){const e=t._readRequests;t._readRequests=new v,e.forEach((e=>{e._closeSteps()}))}}function jr(e,t){e._state="errored",e._storedError=t;const r=e._reader;void 0!==r&&(k(r,t),J(r)?Z(r,t):lt(r,t))}function kr(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function Ar(e,t){L(e,t);const r=null==e?void 0:e.highWaterMark;return M(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Y(r)}}Object.defineProperties(ReadableStream,{from:{enumerable:!0}}),Object.defineProperties(ReadableStream.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),o(ReadableStream.from,"from"),o(ReadableStream.prototype.cancel,"cancel"),o(ReadableStream.prototype.getReader,"getReader"),o(ReadableStream.prototype.pipeThrough,"pipeThrough"),o(ReadableStream.prototype.pipeTo,"pipeTo"),o(ReadableStream.prototype.tee,"tee"),o(ReadableStream.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStream.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(ReadableStream.prototype,de,{value:ReadableStream.prototype.values,writable:!0,configurable:!0});const zr=e=>e.byteLength;o(zr,"size");class ByteLengthQueuingStrategy{constructor(e){$(e,1,"ByteLengthQueuingStrategy"),e=Ar(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Lr(this))throw Dr("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!Lr(this))throw Dr("size");return zr}}function Dr(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function Lr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof ByteLengthQueuingStrategy)}Object.defineProperties(ByteLengthQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ByteLengthQueuingStrategy.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});const Fr=()=>1;o(Fr,"size");class CountQueuingStrategy{constructor(e){$(e,1,"CountQueuingStrategy"),e=Ar(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!$r(this))throw Ir("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!$r(this))throw Ir("size");return Fr}}function Ir(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function $r(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof CountQueuingStrategy)}function Mr(e,t,r){return F(e,r),r=>g(e,t,[r])}function Yr(e,t,r){return F(e,r),r=>S(e,t,[r])}function Qr(e,t,r){return F(e,r),(r,o)=>g(e,t,[r,o])}function xr(e,t,r){return F(e,r),r=>g(e,t,[r])}Object.defineProperties(CountQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(CountQueuingStrategy.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class TransformStream{constructor(e={},t={},r={}){void 0===e&&(e=null);const o=dt(t,"Second parameter"),n=dt(r,"Third parameter"),a=function(e,t){L(e,t);const r=null==e?void 0:e.cancel,o=null==e?void 0:e.flush,n=null==e?void 0:e.readableType,a=null==e?void 0:e.start,i=null==e?void 0:e.transform,l=null==e?void 0:e.writableType;return{cancel:void 0===r?void 0:xr(r,e,`${t} has member 'cancel' that`),flush:void 0===o?void 0:Mr(o,e,`${t} has member 'flush' that`),readableType:n,start:void 0===a?void 0:Yr(a,e,`${t} has member 'start' that`),transform:void 0===i?void 0:Qr(i,e,`${t} has member 'transform' that`),writableType:l}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");const i=ut(n,0),l=ct(n),s=ut(o,1),f=ct(o);let h;!function(e,t,r,o,n,a){function i(){return t}function l(t){return function(e,t){const r=e._transformStreamController;if(e._backpressure){return _(e._backpressureChangePromise,(()=>{const o=e._writable;if("erroring"===o._state)throw o._storedError;return Zr(r,t)}))}return Zr(r,t)}(e,t)}function s(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const o=e._readable;r._finishPromise=u(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const n=r._cancelAlgorithm(t);return Jr(r),b(n,(()=>("errored"===o._state?ro(r,o._storedError):(br(o._readableStreamController,t),to(r)),null)),(e=>(br(o._readableStreamController,e),ro(r,e),null))),r._finishPromise}(e,t)}function c(){return function(e){const t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;const r=e._readable;t._finishPromise=u(((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r}));const o=t._flushAlgorithm();return Jr(t),b(o,(()=>("errored"===r._state?ro(t,r._storedError):(dr(r._readableStreamController),to(t)),null)),(e=>(br(r._readableStreamController,e),ro(t,e),null))),t._finishPromise}(e)}function d(){return function(e){return Gr(e,!1),e._backpressureChangePromise}(e)}function f(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const o=e._writable;r._finishPromise=u(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const n=r._cancelAlgorithm(t);return Jr(r),b(n,(()=>("errored"===o._state?ro(r,o._storedError):(Yt(o._writableStreamController,t),Ur(e),to(r)),null)),(t=>(Yt(o._writableStreamController,t),Ur(e),ro(r,t),null))),r._finishPromise}(e,t)}e._writable=function(e,t,r,o,n=1,a=(()=>1)){const i=Object.create(WritableStream.prototype);return St(i),Ft(i,Object.create(WritableStreamDefaultController.prototype),e,t,r,o,n,a),i}(i,l,c,s,r,o),e._readable=Cr(i,d,f,n,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Gr(e,!0),e._transformStreamController=void 0}(this,u((e=>{h=e})),s,f,i,l),function(e,t){const r=Object.create(TransformStreamDefaultController.prototype);let o,n,a;o=void 0!==t.transform?e=>t.transform(e,r):e=>{try{return Kr(r,e),c(void 0)}catch(e){return d(e)}};n=void 0!==t.flush?()=>t.flush(r):()=>c(void 0);a=void 0!==t.cancel?e=>t.cancel(e):()=>c(void 0);!function(e,t,r,o,n){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=o,t._cancelAlgorithm=n,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0}(e,r,o,n,a)}(this,a),void 0!==a.start?h(a.start(this._transformStreamController)):h(void 0)}get readable(){if(!Nr(this))throw oo("readable");return this._readable}get writable(){if(!Nr(this))throw oo("writable");return this._writable}}function Nr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof TransformStream)}function Hr(e,t){br(e._readable._readableStreamController,t),Vr(e,t)}function Vr(e,t){Jr(e._transformStreamController),Yt(e._writable._writableStreamController,t),Ur(e)}function Ur(e){e._backpressure&&Gr(e,!1)}function Gr(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=u((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(TransformStream.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(TransformStream.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class TransformStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Xr(this))throw eo("desiredSize");return hr(this._controlledTransformStream._readable._readableStreamController)}enqueue(e=void 0){if(!Xr(this))throw eo("enqueue");Kr(this,e)}error(e=void 0){if(!Xr(this))throw eo("error");var t;t=e,Hr(this._controlledTransformStream,t)}terminate(){if(!Xr(this))throw eo("terminate");!function(e){const t=e._controlledTransformStream;dr(t._readable._readableStreamController);const r=new TypeError("TransformStream terminated");Vr(t,r)}(this)}}function Xr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof TransformStreamDefaultController)}function Jr(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function Kr(e,t){const r=e._controlledTransformStream,o=r._readable._readableStreamController;if(!mr(o))throw new TypeError("Readable side is not in a state that permits enqueue");try{fr(o,t)}catch(e){throw Vr(r,e),r._readable._storedError}const n=function(e){return!ur(e)}(o);n!==r._backpressure&&Gr(r,!0)}function Zr(e,t){return _(e._transformAlgorithm(t),void 0,(t=>{throw Hr(e._controlledTransformStream,t),t}))}function eo(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function to(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function ro(e,t){void 0!==e._finishPromise_reject&&(p(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function oo(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(TransformStreamDefaultController.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),o(TransformStreamDefaultController.prototype.enqueue,"enqueue"),o(TransformStreamDefaultController.prototype.error,"error"),o(TransformStreamDefaultController.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(TransformStreamDefaultController.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});const no={ReadableStream:ReadableStream,ReadableStreamDefaultController:ReadableStreamDefaultController,ReadableByteStreamController:ReadableByteStreamController,ReadableStreamBYOBRequest:ReadableStreamBYOBRequest,ReadableStreamDefaultReader:ReadableStreamDefaultReader,ReadableStreamBYOBReader:ReadableStreamBYOBReader,WritableStream:WritableStream,WritableStreamDefaultController:WritableStreamDefaultController,WritableStreamDefaultWriter:WritableStreamDefaultWriter,ByteLengthQueuingStrategy:ByteLengthQueuingStrategy,CountQueuingStrategy:CountQueuingStrategy,TransformStream:TransformStream,TransformStreamDefaultController:TransformStreamDefaultController};for(const e in no)Object.prototype.hasOwnProperty.call(no,e)&&Object.defineProperty(nr,e,{value:no[e],writable:!0,configurable:!0})}();
