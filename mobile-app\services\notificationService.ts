// =====================================================
// EXPO NATIVE NOTIFICATION SERVICE
// =====================================================

import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface NotificationData {
  orderId?: string;
  type: 'order_update' | 'promotion' | 'delivery' | 'general';
  title: string;
  body: string;
  data?: any;
}

class ExpoNotificationService {
  private static instance: ExpoNotificationService;
  private expoPushToken: string | null = null;

  static getInstance(): ExpoNotificationService {
    if (!ExpoNotificationService.instance) {
      ExpoNotificationService.instance = new ExpoNotificationService();
    }
    return ExpoNotificationService.instance;
  }

  /**
   * Initialize notification service and request permissions
   */
  async initialize(): Promise<boolean> {
    try {
      if (!Device.isDevice) {
        console.log('Must use physical device for Push Notifications');
        return false;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return false;
      }

      // Get push token
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: 'your-expo-project-id', // Replace with your actual project ID
      });

      this.expoPushToken = token.data;
      await AsyncStorage.setItem('expo_push_token', token.data);

      console.log('✅ Notification service initialized:', token.data);

      // Configure notification channels for Android
      if (Platform.OS === 'android') {
        await this.setupAndroidChannels();
      }

      return true;
    } catch (error) {
      console.error('❌ Error initializing notifications:', error);
      return false;
    }
  }

  /**
   * Setup Android notification channels
   */
  private async setupAndroidChannels() {
    await Notifications.setNotificationChannelAsync('orders', {
      name: 'Commandes',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF6B6B',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync('promotions', {
      name: 'Promotions',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250],
      lightColor: '#4ECDC4',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync('delivery', {
      name: 'Livraison',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#45B7D1',
      sound: 'default',
    });
  }

  /**
   * Get the current push token
   */
  async getPushToken(): Promise<string | null> {
    if (this.expoPushToken) {
      return this.expoPushToken;
    }

    try {
      const stored = await AsyncStorage.getItem('expo_push_token');
      if (stored) {
        this.expoPushToken = stored;
        return stored;
      }
    } catch (error) {
      console.error('Error getting stored push token:', error);
    }

    return null;
  }

  /**
   * Schedule a local notification
   */
  async scheduleLocalNotification(
    notification: NotificationData,
    trigger?: Notifications.NotificationTriggerInput
  ): Promise<string | null> {
    try {
      const channelId = this.getChannelId(notification.type);
      
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          sound: 'default',
          ...(Platform.OS === 'android' && { channelId }),
        },
        trigger: trigger || null, // null means show immediately
      });

      console.log('✅ Local notification scheduled:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('❌ Error scheduling local notification:', error);
      return null;
    }
  }

  /**
   * Send push notification (for testing - in production this would be done server-side)
   */
  async sendPushNotification(
    notification: NotificationData,
    targetToken?: string
  ): Promise<boolean> {
    try {
      const token = targetToken || this.expoPushToken;
      if (!token) {
        console.error('No push token available');
        return false;
      }

      // In a real app, this would be done server-side
      const message = {
        to: token,
        sound: 'default',
        title: notification.title,
        body: notification.body,
        data: notification.data || {},
        channelId: this.getChannelId(notification.type),
      };

      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      const result = await response.json();
      console.log('✅ Push notification sent:', result);
      return true;
    } catch (error) {
      console.error('❌ Error sending push notification:', error);
      return false;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      console.log('✅ Notification cancelled:', notificationId);
    } catch (error) {
      console.error('❌ Error cancelling notification:', error);
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('✅ All notifications cancelled');
    } catch (error) {
      console.error('❌ Error cancelling all notifications:', error);
    }
  }

  /**
   * Get notification channel ID based on type
   */
  private getChannelId(type: NotificationData['type']): string {
    switch (type) {
      case 'order_update':
        return 'orders';
      case 'promotion':
        return 'promotions';
      case 'delivery':
        return 'delivery';
      default:
        return 'orders';
    }
  }

  /**
   * Handle notification received while app is in foreground
   */
  addNotificationReceivedListener(
    listener: (notification: Notifications.Notification) => void
  ) {
    return Notifications.addNotificationReceivedListener(listener);
  }

  /**
   * Handle notification response (when user taps notification)
   */
  addNotificationResponseReceivedListener(
    listener: (response: Notifications.NotificationResponse) => void
  ) {
    return Notifications.addNotificationResponseReceivedListener(listener);
  }

  /**
   * Utility methods for common notification scenarios
   */
  async notifyOrderStatusUpdate(orderId: string, status: string, merchantName: string) {
    const statusMessages: Record<string, string> = {
      confirmed: `Votre commande chez ${merchantName} a été confirmée`,
      preparing: `${merchantName} prépare votre commande`,
      ready: `Votre commande chez ${merchantName} est prête`,
      picked_up: 'Votre commande a été récupérée par le livreur',
      in_transit: 'Votre commande est en cours de livraison',
      delivered: 'Votre commande a été livrée avec succès',
      cancelled: 'Votre commande a été annulée',
    };

    await this.scheduleLocalNotification({
      type: 'order_update',
      title: 'Mise à jour de commande',
      body: statusMessages[status] || 'Statut de commande mis à jour',
      data: { orderId, status },
    });
  }

  async notifyPromotion(title: string, description: string, merchantId?: string) {
    await this.scheduleLocalNotification({
      type: 'promotion',
      title: title,
      body: description,
      data: { merchantId },
    });
  }

  async notifyDeliveryArrival(estimatedMinutes: number) {
    await this.scheduleLocalNotification({
      type: 'delivery',
      title: 'Livraison en approche',
      body: `Votre livreur arrivera dans environ ${estimatedMinutes} minutes`,
      data: { estimatedMinutes },
    });
  }
}

export const notificationService = ExpoNotificationService.getInstance();
export default notificationService;
