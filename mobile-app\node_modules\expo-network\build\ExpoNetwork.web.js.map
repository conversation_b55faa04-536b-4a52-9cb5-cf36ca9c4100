{"version": 3, "file": "ExpoNetwork.web.js", "sourceRoot": "", "sources": ["../src/ExpoNetwork.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAEpE,OAAO,EAA+B,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AAEhF,MAAM,uBAAuB,GAAG,uBAAuB,CAAC;AAExD,SAAS,eAAe;IACtB,MAAM,QAAQ,GAAG,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,MAAM,CAAC;IACtE,OAAO;QACL,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI;QACjE,WAAW,EAAE,QAAQ;QACrB,mBAAmB,EAAE,QAAQ;KAC9B,CAAC;AACJ,CAAC;AAED,MAAM,iBAAkB,SAAQ,YAA2B;IACzD,aAAa,CAAc;IAC3B,kBAAkB;QAChB,MAAM,KAAK,GAAG,eAAe,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,EAAE,CAAC;QACjB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IACD,KAAK,CAAC,oBAAoB;QACxB,OAAO,eAAe,EAAE,CAAC;IAC3B,CAAC;IACD,KAAK,CAAC,0BAA0B;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,cAAc;QACZ,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACrD,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACtD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACzD,CAAC;IACD,aAAa;QACX,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACzD,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;CACF;AAED,eAAe,iBAAiB,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC", "sourcesContent": ["import { NativeModule, registerWebModule } from 'expo-modules-core';\n\nimport { NetworkEvents, NetworkState, NetworkStateType } from './Network.types';\n\nconst onNetworkStateEventName = 'onNetworkStateChanged';\n\nfunction getNetworkState(): NetworkState {\n  const isOnline = typeof navigator !== 'undefined' && navigator.onLine;\n  return {\n    type: isOnline ? NetworkStateType.UNKNOWN : NetworkStateType.NONE,\n    isConnected: isOnline,\n    isInternetReachable: isOnline,\n  };\n}\n\nclass ExpoNetworkModule extends NativeModule<NetworkEvents> {\n  eventListener?: () => void;\n  updateNetworkState() {\n    const state = getNetworkState();\n    this.emit(onNetworkStateEventName, state);\n  }\n\n  async getIpAddressAsync(): Promise<string> {\n    try {\n      const resp = await fetch('https://api.ipify.org?format=json');\n      const data = await resp.json();\n      return data.ip;\n    } catch (e) {\n      throw e;\n    }\n  }\n  async getNetworkStateAsync(): Promise<NetworkState> {\n    return getNetworkState();\n  }\n  async isAirplaneModeEnabledAsync(): Promise<boolean> {\n    return false;\n  }\n  startObserving() {\n    this.eventListener = () => this.updateNetworkState();\n    window.addEventListener('online', this.eventListener);\n    window.addEventListener('offline', this.eventListener);\n  }\n  stopObserving() {\n    if (this.eventListener) {\n      window.removeEventListener('online', this.eventListener);\n      window.removeEventListener('offline', this.eventListener);\n    }\n  }\n}\n\nexport default registerWebModule(ExpoNetworkModule, 'ExpoNetwork');\n"]}