// =====================================================
// REACT QUERY HOOKS FOR SUPABASE - AFRICAN DELIVERY APP
// =====================================================

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabaseService } from '../services/supabaseService';
import type { Category, MerchantProfile, Product, Order } from '../services/supabaseService';

// Query keys for consistent caching
export const queryKeys = {
  categories: ['categories'],
  merchants: ['merchants'],
  nearbyMerchants: (lat: number, lng: number, radius: number) => 
    ['merchants', 'nearby', lat, lng, radius],
  merchantsByCategory: (categoryId: string) => 
    ['merchants', 'category', categoryId],
  merchant: (id: string) => ['merchants', id],
  products: (merchantId: string) => ['products', merchantId],
  featuredProducts: ['products', 'featured'],
  orders: (userId?: string) => ['orders', userId],
  order: (id: string) => ['orders', id],
  promotions: ['promotions'],
  user: ['user'],
};

// =====================================================
// CATEGORIES HOOKS
// =====================================================

export const useCategories = () => {
  return useQuery({
    queryKey: queryKeys.categories,
    queryFn: () => supabaseService.getCategories(),
    staleTime: 1000 * 60 * 30, // 30 minutes
    cacheTime: 1000 * 60 * 60 * 24, // 24 hours
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// =====================================================
// MERCHANTS HOOKS
// =====================================================

export const useNearbyMerchants = (
  latitude: number, 
  longitude: number, 
  radius: number = 10,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: queryKeys.nearbyMerchants(latitude, longitude, radius),
    queryFn: () => supabaseService.getNearbyMerchants(latitude, longitude, radius),
    enabled: enabled && !!latitude && !!longitude,
    staleTime: 1000 * 60 * 5, // 5 minutes
    cacheTime: 1000 * 60 * 30, // 30 minutes
    retry: 2,
  });
};

export const useAllMerchants = () => {
  return useQuery({
    queryKey: queryKeys.merchants,
    queryFn: () => supabaseService.getAllMerchants(),
    staleTime: 1000 * 60 * 10, // 10 minutes
    cacheTime: 1000 * 60 * 60, // 1 hour
    retry: 3,
  });
};

export const useMerchantsByCategory = (categoryId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.merchantsByCategory(categoryId),
    queryFn: () => supabaseService.getMerchantsByCategory(categoryId),
    enabled: enabled && !!categoryId,
    staleTime: 1000 * 60 * 10, // 10 minutes
    cacheTime: 1000 * 60 * 30, // 30 minutes
    retry: 2,
  });
};

export const useMerchant = (merchantId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.merchant(merchantId),
    queryFn: () => supabaseService.getMerchantById(merchantId),
    enabled: enabled && !!merchantId,
    staleTime: 1000 * 60 * 15, // 15 minutes
    cacheTime: 1000 * 60 * 60, // 1 hour
    retry: 2,
  });
};

// =====================================================
// PRODUCTS HOOKS
// =====================================================

export const useProductsByMerchant = (merchantId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.products(merchantId),
    queryFn: () => supabaseService.getProductsByMerchant(merchantId),
    enabled: enabled && !!merchantId,
    staleTime: 1000 * 60 * 10, // 10 minutes
    cacheTime: 1000 * 60 * 30, // 30 minutes
    retry: 2,
  });
};

export const useFeaturedProducts = () => {
  return useQuery({
    queryKey: queryKeys.featuredProducts,
    queryFn: () => supabaseService.getFeaturedProducts(),
    staleTime: 1000 * 60 * 15, // 15 minutes
    cacheTime: 1000 * 60 * 60, // 1 hour
    retry: 2,
  });
};

// =====================================================
// ORDERS HOOKS
// =====================================================

export const useOrders = (userId?: string) => {
  return useQuery({
    queryKey: queryKeys.orders(userId),
    queryFn: () => supabaseService.getOrdersByUser(userId),
    staleTime: 1000 * 60 * 2, // 2 minutes
    cacheTime: 1000 * 60 * 10, // 10 minutes
    retry: 3,
  });
};

export const useOrder = (orderId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: queryKeys.order(orderId),
    queryFn: () => supabaseService.getOrderById(orderId),
    enabled: enabled && !!orderId,
    staleTime: 1000 * 30, // 30 seconds
    cacheTime: 1000 * 60 * 5, // 5 minutes
    retry: 2,
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
  });
};

// =====================================================
// PROMOTIONS HOOKS
// =====================================================

export const usePromotions = () => {
  return useQuery({
    queryKey: queryKeys.promotions,
    queryFn: () => supabaseService.getActivePromotions(),
    staleTime: 1000 * 60 * 10, // 10 minutes
    cacheTime: 1000 * 60 * 60, // 1 hour
    retry: 2,
  });
};

// =====================================================
// USER HOOKS
// =====================================================

export const useCurrentUser = () => {
  return useQuery({
    queryKey: queryKeys.user,
    queryFn: () => supabaseService.fetchCurrentUser(),
    staleTime: 1000 * 60 * 5, // 5 minutes
    cacheTime: 1000 * 60 * 30, // 30 minutes
    retry: 1,
  });
};

// =====================================================
// MUTATION HOOKS
// =====================================================

export const useCreateOrder = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (orderData: Partial<Order>) => supabaseService.createOrder(orderData),
    onSuccess: (data, variables) => {
      // Invalidate orders cache
      queryClient.invalidateQueries({ queryKey: queryKeys.orders() });
      
      // Add the new order to cache
      if (data) {
        queryClient.setQueryData(queryKeys.order(data.id), data);
      }
    },
    onError: (error) => {
      console.error('❌ Create order mutation error:', error);
    },
  });
};

export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ orderId, status }: { orderId: string; status: string }) => 
      supabaseService.updateOrderStatus(orderId, status),
    onSuccess: (data, variables) => {
      // Invalidate and refetch order data
      queryClient.invalidateQueries({ queryKey: queryKeys.order(variables.orderId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.orders() });
    },
    onError: (error) => {
      console.error('❌ Update order status mutation error:', error);
    },
  });
};

export const useCancelOrder = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ orderId, reason }: { orderId: string; reason: string }) => 
      supabaseService.cancelOrder(orderId, reason),
    onSuccess: (data, variables) => {
      // Invalidate and refetch order data
      queryClient.invalidateQueries({ queryKey: queryKeys.order(variables.orderId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.orders() });
    },
    onError: (error) => {
      console.error('❌ Cancel order mutation error:', error);
    },
  });
};

// =====================================================
// AUTHENTICATION MUTATIONS
// =====================================================

export const useSignUp = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ phone, password, userData }: { 
      phone: string; 
      password: string; 
      userData?: any 
    }) => supabaseService.signUp(phone, password, userData),
    onSuccess: () => {
      // Refetch user data
      queryClient.invalidateQueries({ queryKey: queryKeys.user });
    },
    onError: (error) => {
      console.error('❌ Sign up mutation error:', error);
    },
  });
};

export const useSignIn = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ phone, password }: { phone: string; password: string }) => 
      supabaseService.signIn(phone, password),
    onSuccess: () => {
      // Refetch user data
      queryClient.invalidateQueries({ queryKey: queryKeys.user });
    },
    onError: (error) => {
      console.error('❌ Sign in mutation error:', error);
    },
  });
};

export const useSignOut = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => supabaseService.signOut(),
    onSuccess: () => {
      // Clear all cached data
      queryClient.clear();
    },
    onError: (error) => {
      console.error('❌ Sign out mutation error:', error);
    },
  });
};

// =====================================================
// OFFLINE SUPPORT UTILITIES
// =====================================================

export const usePrefetchData = () => {
  const queryClient = useQueryClient();
  
  const prefetchCategories = () => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.categories,
      queryFn: () => supabaseService.getCategories(),
      staleTime: 1000 * 60 * 30,
    });
  };

  const prefetchMerchants = () => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.merchants,
      queryFn: () => supabaseService.getAllMerchants(),
      staleTime: 1000 * 60 * 10,
    });
  };

  const prefetchPromotions = () => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.promotions,
      queryFn: () => supabaseService.getActivePromotions(),
      staleTime: 1000 * 60 * 10,
    });
  };

  return {
    prefetchCategories,
    prefetchMerchants,
    prefetchPromotions,
    prefetchAll: () => {
      prefetchCategories();
      prefetchMerchants();
      prefetchPromotions();
    },
  };
};
