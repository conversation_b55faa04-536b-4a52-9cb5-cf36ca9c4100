{"name": "expo-network", "version": "7.1.5", "description": "Provides useful information about the device's network such as its IP address, MAC address, and airplane mode status", "main": "build/Network.js", "types": "build/Network.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-network"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-network"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/network/", "dependencies": {}, "devDependencies": {"expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*", "react": "*"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}