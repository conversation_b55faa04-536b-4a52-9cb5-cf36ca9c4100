export { L as AnyDataTag, b3 as CancelOptions, P as DataTag, E as DefaultError, b2 as DefaultOptions, ah as DefaultedInfiniteQueryObserverOptions, af as DefaultedQueryObserverOptions, aM as DefinedInfiniteQueryObserverResult, aD as DefinedQueryObserverResult, A as DistributiveOmit, Y as Enabled, ak as EnsureInfiniteQueryDataOptions, aj as EnsureQueryDataOptions, al as FetchInfiniteQueryOptions, as as FetchNextPageOptions, at as FetchPreviousPageOptions, ai as FetchQueryOptions, av as FetchStatus, a4 as GetNextPageParamFunction, a3 as GetPreviousPageParamFunction, T as InferDataFromTag, V as InferErrorFromTag, a5 as InfiniteData, aF as InfiniteQueryObserverBaseResult, aI as InfiniteQueryObserverLoadingErrorResult, aH as InfiniteQueryObserverLoadingResult, ag as InfiniteQueryObserverOptions, aG as InfiniteQueryObserverPendingResult, aL as InfiniteQueryObserverPlaceholderResult, aJ as InfiniteQueryObserverRefetchErrorResult, aN as InfiniteQueryObserverResult, aK as InfiniteQueryObserverSuccessResult, ab as InfiniteQueryPageParamsOptions, $ as InitialDataFunction, aa as InitialPageParam, aq as InvalidateOptions, ao as InvalidateQueryFilters, aW as MutateFunction, aV as MutateOptions, aS as MutationFunction, aO as MutationKey, aR as MutationMeta, aX as MutationObserverBaseResult, a_ as MutationObserverErrorResult, aY as MutationObserverIdleResult, aZ as MutationObserverLoadingResult, aU as MutationObserverOptions, b0 as MutationObserverResult, a$ as MutationObserverSuccessResult, aT as MutationOptions, aQ as MutationScope, aP as MutationStatus, a7 as NetworkMode, N as NoInfer, b6 as NotifyEvent, b5 as NotifyEventType, a8 as NotifyOnChangeProps, O as OmitKeyof, B as Override, a0 as PlaceholderDataFunction, a1 as QueriesPlaceholderDataFunction, b1 as QueryClientConfig, W as QueryFunction, _ as QueryFunctionContext, F as QueryKey, a2 as QueryKeyHashFunction, a6 as QueryMeta, aw as QueryObserverBaseResult, az as QueryObserverLoadingErrorResult, ay as QueryObserverLoadingResult, ad as QueryObserverOptions, ax as QueryObserverPendingResult, aC as QueryObserverPlaceholderResult, aA as QueryObserverRefetchErrorResult, aE as QueryObserverResult, aB as QueryObserverSuccessResult, a9 as QueryOptions, Z as QueryPersister, au as QueryStatus, an as RefetchOptions, ap as RefetchQueryFilters, R as Register, ar as ResetOptions, am as ResultOptions, b4 as SetDataOptions, X as StaleTime, ac as ThrowOnError, K as UnsetMarker, ae as WithRequired, I as dataTagErrorSymbol, G as dataTagSymbol, J as unsetMarker } from './hydration-GTiXepW_.cjs';
import './removable.cjs';
import './subscribable.cjs';
