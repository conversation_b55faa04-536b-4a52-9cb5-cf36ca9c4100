import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import ClientHomeScreen from '../screens/ClientHomeScreen'; // Import the screen

const Tab = createBottomTabNavigator();

const ClientTabNavigator = () => {
  return (
    <Tab.Navigator>
      {/* TODO: Add Client screens here */}
      <Tab.Screen name="Home" component={ClientHomeScreen} />
      {/* Examples: */}
      {/* <Tab.Screen name="Search" component={ClientSearchScreen} /> */}
      {/* <Tab.Screen name="Cart" component={ClientCartScreen} /> */}
      {/* <Tab.Screen name="Orders" component={ClientOrdersScreen} /> */}
      {/* <Tab.Screen name="Profile" component={ClientProfileScreen} /> */}
    </Tab.Navigator>
  );
};

export default ClientTabNavigator;