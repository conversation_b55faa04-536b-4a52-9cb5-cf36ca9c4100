-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- Application de Livraison Afrique
-- =====================================================

-- Activer RLS sur toutes les tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE merchants ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE delivery_riders ENABLE ROW LEVEL SECURITY;
ALTER TABLE delivery_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE mobile_money_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE card_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE refunds ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE promotions ENABLE ROW LEVEL SECURITY;
ALTER TABLE promotion_uses ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- POLITIQUES POUR PROFILES
-- =====================================================

-- Les utilisateurs peuvent voir leur propre profil
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

-- Les utilisateurs peuvent mettre à jour leur propre profil
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Les admins peuvent voir tous les profils
CREATE POLICY "Admins can view all profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- Les livreurs peuvent voir les profils des clients de leurs commandes
CREATE POLICY "Riders can view customer profiles for their orders" ON profiles
    FOR SELECT USING (
        role = 'customer' AND
        EXISTS (
            SELECT 1 FROM orders o
            JOIN profiles p ON p.id = auth.uid()
            WHERE o.customer_id = profiles.id
            AND o.delivery_rider_id = auth.uid()
            AND p.role = 'delivery_rider'
        )
    );

-- Les commerçants peuvent voir les profils des clients de leurs commandes
CREATE POLICY "Merchants can view customer profiles for their orders" ON profiles
    FOR SELECT USING (
        role = 'customer' AND
        EXISTS (
            SELECT 1 FROM orders o
            JOIN merchants m ON m.id = o.merchant_id
            WHERE o.customer_id = profiles.id
            AND m.owner_id = auth.uid()
        )
    );

-- =====================================================
-- POLITIQUES POUR ADDRESSES
-- =====================================================

-- Les utilisateurs peuvent gérer leurs propres adresses
CREATE POLICY "Users can manage own addresses" ON addresses
    FOR ALL USING (auth.uid() = user_id);

-- Les livreurs peuvent voir les adresses de livraison de leurs commandes
CREATE POLICY "Riders can view delivery addresses for their orders" ON addresses
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.delivery_address_id = addresses.id
            AND o.delivery_rider_id = auth.uid()
        )
    );

-- =====================================================
-- POLITIQUES POUR MERCHANTS
-- =====================================================

-- Les propriétaires peuvent gérer leur commerce
CREATE POLICY "Owners can manage their merchant" ON merchants
    FOR ALL USING (auth.uid() = owner_id);

-- Tout le monde peut voir les commerçants actifs
CREATE POLICY "Anyone can view active merchants" ON merchants
    FOR SELECT USING (is_active = true AND is_verified = true);

-- Les admins peuvent voir tous les commerçants
CREATE POLICY "Admins can view all merchants" ON merchants
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- =====================================================
-- POLITIQUES POUR PRODUCTS
-- =====================================================

-- Les propriétaires de commerce peuvent gérer leurs produits
CREATE POLICY "Merchant owners can manage their products" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM merchants m
            WHERE m.id = products.merchant_id
            AND m.owner_id = auth.uid()
        )
    );

-- Tout le monde peut voir les produits disponibles
CREATE POLICY "Anyone can view available products" ON products
    FOR SELECT USING (
        is_available = true AND
        EXISTS (
            SELECT 1 FROM merchants m
            WHERE m.id = products.merchant_id
            AND m.is_active = true
            AND m.is_accepting_orders = true
        )
    );

-- =====================================================
-- POLITIQUES POUR ORDERS
-- =====================================================

-- Les clients peuvent voir et créer leurs commandes
CREATE POLICY "Customers can manage own orders" ON orders
    FOR ALL USING (auth.uid() = customer_id);

-- Les commerçants peuvent voir les commandes de leur commerce
CREATE POLICY "Merchants can view their orders" ON orders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM merchants m
            WHERE m.id = orders.merchant_id
            AND m.owner_id = auth.uid()
        )
    );

-- Les commerçants peuvent mettre à jour le statut de leurs commandes
CREATE POLICY "Merchants can update their order status" ON orders
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM merchants m
            WHERE m.id = orders.merchant_id
            AND m.owner_id = auth.uid()
        )
    );

-- Les livreurs peuvent voir leurs commandes assignées
CREATE POLICY "Riders can view assigned orders" ON orders
    FOR SELECT USING (auth.uid() = delivery_rider_id);

-- Les livreurs peuvent mettre à jour le statut de leurs commandes
CREATE POLICY "Riders can update assigned order status" ON orders
    FOR UPDATE USING (auth.uid() = delivery_rider_id);

-- Les admins peuvent voir toutes les commandes
CREATE POLICY "Admins can view all orders" ON orders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- =====================================================
-- POLITIQUES POUR ORDER_ITEMS
-- =====================================================

-- Les clients peuvent voir les articles de leurs commandes
CREATE POLICY "Customers can view own order items" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.id = order_items.order_id
            AND o.customer_id = auth.uid()
        )
    );

-- Les commerçants peuvent voir les articles de leurs commandes
CREATE POLICY "Merchants can view their order items" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            JOIN merchants m ON m.id = o.merchant_id
            WHERE o.id = order_items.order_id
            AND m.owner_id = auth.uid()
        )
    );

-- Les livreurs peuvent voir les articles des commandes assignées
CREATE POLICY "Riders can view assigned order items" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.id = order_items.order_id
            AND o.delivery_rider_id = auth.uid()
        )
    );

-- =====================================================
-- POLITIQUES POUR DELIVERY_RIDERS
-- =====================================================

-- Les livreurs peuvent gérer leur propre profil
CREATE POLICY "Riders can manage own profile" ON delivery_riders
    FOR ALL USING (auth.uid() = id);

-- Les clients peuvent voir les livreurs de leurs commandes
CREATE POLICY "Customers can view their order riders" ON delivery_riders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.delivery_rider_id = delivery_riders.id
            AND o.customer_id = auth.uid()
        )
    );

-- Les admins peuvent voir tous les livreurs
CREATE POLICY "Admins can view all riders" ON delivery_riders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- =====================================================
-- POLITIQUES POUR PAYMENTS
-- =====================================================

-- Les clients peuvent voir leurs paiements
CREATE POLICY "Customers can view own payments" ON payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.id = payments.order_id
            AND o.customer_id = auth.uid()
        )
    );

-- Les commerçants peuvent voir les paiements de leurs commandes
CREATE POLICY "Merchants can view their order payments" ON payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            JOIN merchants m ON m.id = o.merchant_id
            WHERE o.id = payments.order_id
            AND m.owner_id = auth.uid()
        )
    );

-- Les admins peuvent voir tous les paiements
CREATE POLICY "Admins can view all payments" ON payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'super_admin')
        )
    );

-- =====================================================
-- POLITIQUES POUR USER_WALLETS
-- =====================================================

-- Les utilisateurs peuvent gérer leur propre portefeuille
CREATE POLICY "Users can manage own wallet" ON user_wallets
    FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- POLITIQUES POUR NOTIFICATIONS
-- =====================================================

-- Les utilisateurs peuvent voir leurs propres notifications
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (auth.uid() = user_id);

-- Les utilisateurs peuvent marquer leurs notifications comme lues
CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- POLITIQUES POUR RATINGS
-- =====================================================

-- Les utilisateurs peuvent voir les évaluations qui les concernent
CREATE POLICY "Users can view ratings about them" ON ratings
    FOR SELECT USING (auth.uid() = rated_id OR auth.uid() = rater_id);

-- Les utilisateurs peuvent créer des évaluations
CREATE POLICY "Users can create ratings" ON ratings
    FOR INSERT WITH CHECK (auth.uid() = rater_id);

-- =====================================================
-- POLITIQUES POUR DELIVERY_TRACKING
-- =====================================================

-- Les clients peuvent voir le tracking de leurs commandes
CREATE POLICY "Customers can view own order tracking" ON delivery_tracking
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.id = delivery_tracking.order_id
            AND o.customer_id = auth.uid()
        )
    );

-- Les livreurs peuvent voir et créer le tracking de leurs commandes
CREATE POLICY "Riders can manage assigned order tracking" ON delivery_tracking
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.id = delivery_tracking.order_id
            AND o.delivery_rider_id = auth.uid()
        )
    );

-- Les commerçants peuvent voir le tracking de leurs commandes
CREATE POLICY "Merchants can view their order tracking" ON delivery_tracking
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            JOIN merchants m ON m.id = o.merchant_id
            WHERE o.id = delivery_tracking.order_id
            AND m.owner_id = auth.uid()
        )
    );

-- =====================================================
-- POLITIQUES POUR USER_SESSIONS
-- =====================================================

-- Les utilisateurs peuvent voir leurs propres sessions
CREATE POLICY "Users can view own sessions" ON user_sessions
    FOR SELECT USING (auth.uid() = user_id);

-- Les utilisateurs peuvent mettre à jour leurs propres sessions
CREATE POLICY "Users can update own sessions" ON user_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- POLITIQUES POUR LES TABLES PUBLIQUES
-- =====================================================

-- Tables en lecture seule pour tous les utilisateurs authentifiés
CREATE POLICY "Authenticated users can view countries" ON countries
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view cities" ON cities
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view delivery zones" ON delivery_zones
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view payment configurations" ON payment_configurations
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view active promotions" ON promotions
    FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);
