{"version": 3, "file": "NotificationPermissionsModule.types.js", "sourceRoot": "", "sources": ["../src/NotificationPermissionsModule.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\nimport {\n  NotificationPermissionsStatus,\n  NativeNotificationPermissionsRequest,\n} from './NotificationPermissions.types';\n\nexport interface NotificationPermissionsModule extends ProxyNativeModule {\n  getPermissionsAsync?: () => Promise<NotificationPermissionsStatus>;\n  requestPermissionsAsync?: (\n    request: NativeNotificationPermissionsRequest\n  ) => Promise<NotificationPermissionsStatus>;\n}\n"]}