import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import ClientTabNavigator from './ClientTabNavigator';
import DelivererTabNavigator from './DelivererTabNavigator';
import MerchantTabNavigator from './MerchantTabNavigator';
import AuthScreen from '../screens/AuthScreen';
import { useAuth } from '../context/AuthContext';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const { isAuthenticated, userRole, loading } = useAuth();

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!isAuthenticated ? (
          // Authentication flow
          <Stack.Screen name="Auth" component={AuthScreen} />
        ) : (
          // Conditional rendering based on user role
          userRole === 'client' ? (
            <Stack.Screen name="Client" component={ClientTabNavigator} />
          ) : userRole === 'deliverer' ? (
            <Stack.Screen name="Deliverer" component={DelivererTabNavigator} />
          ) : userRole === 'merchant' ? (
            <Stack.Screen name="Merchant" component={MerchantTabNavigator} />
          ) : (
            // Fallback or loading screen
            // TODO: Implement a proper loading/error screen
            <Stack.Screen name="Loading" component={() => null} />
          )
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;