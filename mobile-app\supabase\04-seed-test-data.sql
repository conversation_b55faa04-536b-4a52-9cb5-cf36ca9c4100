-- =====================================================
-- DONNÉES DE TEST POUR DÉVELOPPEMENT
-- =====================================================

-- Insérer des utilisateurs de test
-- Note: Les mots de passe doivent être créés via Supabase Auth

-- Créer des profils de test (à exécuter après création des utilisateurs via Auth)
INSERT INTO profiles (id, role, phone_number, country_id, first_name, last_name, preferred_language, is_verified, is_active) VALUES
-- Clients
('11111111-1111-1111-1111-111111111111', 'customer', '+221701234567', (SELECT id FROM countries WHERE code = 'SEN'), 'Aminata', 'Diallo', 'fr', true, true),
('22222222-2222-2222-2222-222222222222', 'customer', '+254701234567', (SELECT id FROM countries WHERE code = 'KEN'), 'John', 'Kamau', 'en', true, true),
('33333333-3333-3333-3333-333333333333', 'customer', '+233201234567', (SELECT id FROM countries WHERE code = 'GHA'), 'Akosua', 'Mensah', 'en', true, true),

-- Commerçants
('44444444-4444-4444-4444-444444444444', 'merchant', '+221701234568', (SELECT id FROM countries WHERE code = 'SEN'), 'Moussa', 'Ba', 'fr', true, true),
('55555555-5555-5555-5555-555555555555', 'merchant', '+254701234568', (SELECT id FROM countries WHERE code = 'KEN'), 'Grace', 'Wanjiku', 'en', true, true),

-- Livreurs
('66666666-6666-6666-6666-666666666666', 'delivery_rider', '+221701234569', (SELECT id FROM countries WHERE code = 'SEN'), 'Ibrahima', 'Ndiaye', 'fr', true, true),
('77777777-7777-7777-7777-777777777777', 'delivery_rider', '+254701234569', (SELECT id FROM countries WHERE code = 'KEN'), 'Peter', 'Mwangi', 'en', true, true),

-- Administrateurs
('88888888-8888-8888-8888-888888888888', 'admin', '+221701234570', (SELECT id FROM countries WHERE code = 'SEN'), 'Fatou', 'Sall', 'fr', true, true);

-- Insérer des adresses de test
INSERT INTO addresses (user_id, city_id, delivery_zone_id, label, street_address, landmark, location, is_default) VALUES
-- Adresses clients Dakar
('11111111-1111-1111-1111-111111111111', (SELECT id FROM cities WHERE name = 'Dakar'), (SELECT id FROM delivery_zones WHERE name = 'Plateau'), 'Domicile', 'Rue 10, Plateau', 'Près de la Place de l''Indépendance', ST_Point(-17.4450, 14.6900, 4326), true),
('11111111-1111-1111-1111-111111111111', (SELECT id FROM cities WHERE name = 'Dakar'), (SELECT id FROM delivery_zones WHERE name = 'Almadies'), 'Bureau', 'Route des Almadies', 'Immeuble Fahd', ST_Point(-17.5000, 14.7350, 4326), false),

-- Adresses clients Nairobi
('22222222-2222-2222-2222-222222222222', (SELECT id FROM cities WHERE name = 'Nairobi'), (SELECT id FROM delivery_zones WHERE name = 'CBD'), 'Home', 'Kenyatta Avenue', 'Near Hilton Hotel', ST_Point(36.8200, -1.2900, 4326), true),
('22222222-2222-2222-2222-222222222222', (SELECT id FROM cities WHERE name = 'Nairobi'), (SELECT id FROM delivery_zones WHERE name = 'Westlands'), 'Office', 'Westlands Road', 'Sarit Centre', ST_Point(36.8000, -1.2600, 4326), false);

-- Insérer des commerçants de test
INSERT INTO merchants (id, owner_id, business_name, business_type, description, phone_number, email, city_id, delivery_zone_id, address, location, business_hours, delivery_radius_km, min_order_amount, delivery_fee, estimated_prep_time, is_verified, is_active, is_accepting_orders) VALUES
-- Restaurant sénégalais
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '44444444-4444-4444-4444-444444444444', 'Chez Fatou Restaurant', 'restaurant', 'Restaurant traditionnel sénégalais spécialisé dans le thieboudienne et les grillades', '+221701234568', '<EMAIL>', (SELECT id FROM cities WHERE name = 'Dakar'), (SELECT id FROM delivery_zones WHERE name = 'Plateau'), 'Avenue Pompidou, Plateau', ST_Point(-17.4460, 14.6910, 4326), '{"monday": {"open": "11:00", "close": "23:00"}, "tuesday": {"open": "11:00", "close": "23:00"}, "wednesday": {"open": "11:00", "close": "23:00"}, "thursday": {"open": "11:00", "close": "23:00"}, "friday": {"open": "11:00", "close": "23:00"}, "saturday": {"open": "11:00", "close": "23:00"}, "sunday": {"open": "12:00", "close": "22:00"}}', 10, 1500, 500, 30, true, true, true),

-- Restaurant kenyan
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '55555555-5555-5555-5555-555555555555', 'Mama Grace Kitchen', 'restaurant', 'Authentic Kenyan cuisine with nyama choma and ugali', '+254701234568', '<EMAIL>', (SELECT id FROM cities WHERE name = 'Nairobi'), (SELECT id FROM delivery_zones WHERE name = 'CBD'), 'Tom Mboya Street, CBD', ST_Point(36.8210, -1.2890, 4326), '{"monday": {"open": "10:00", "close": "22:00"}, "tuesday": {"open": "10:00", "close": "22:00"}, "wednesday": {"open": "10:00", "close": "22:00"}, "thursday": {"open": "10:00", "close": "22:00"}, "friday": {"open": "10:00", "close": "22:00"}, "saturday": {"open": "10:00", "close": "22:00"}, "sunday": {"open": "11:00", "close": "21:00"}}', 15, 200, 150, 25, true, true, true),

-- Épicerie sénégalaise
('cccccccc-cccc-cccc-cccc-cccccccccccc', '44444444-4444-4444-4444-444444444444', 'Épicerie du Coin', 'grocery', 'Épicerie de quartier avec produits frais et articles ménagers', '+221701234568', '<EMAIL>', (SELECT id FROM cities WHERE name = 'Dakar'), (SELECT id FROM delivery_zones WHERE name = 'Pikine'), 'Marché de Pikine', ST_Point(-17.3750, 14.7750, 4326), '{"monday": {"open": "07:00", "close": "20:00"}, "tuesday": {"open": "07:00", "close": "20:00"}, "wednesday": {"open": "07:00", "close": "20:00"}, "thursday": {"open": "07:00", "close": "20:00"}, "friday": {"open": "07:00", "close": "20:00"}, "saturday": {"open": "07:00", "close": "20:00"}, "sunday": {"open": "08:00", "close": "18:00"}}', 8, 500, 300, 15, true, true, true);

-- Insérer des catégories de produits
INSERT INTO product_categories (merchant_id, name, name_translations, description, sort_order) VALUES
-- Restaurant sénégalais
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Plats principaux', '{"en": "Main dishes", "wo": "Lekk bu mag"}', 'Nos spécialités sénégalaises', 1),
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Grillades', '{"en": "Grilled meat", "wo": "Yàpp bu sotti"}', 'Viandes grillées au feu de bois', 2),
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Boissons', '{"en": "Beverages", "wo": "Naan"}', 'Boissons fraîches et chaudes', 3),

-- Restaurant kenyan
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Main Courses', '{"sw": "Vyakula vikuu"}', 'Traditional Kenyan main dishes', 1),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Beverages', '{"sw": "Vinywaji"}', 'Fresh drinks and traditional beverages', 2),

-- Épicerie
('cccccccc-cccc-cccc-cccc-cccccccccccc', 'Fruits et Légumes', '{"en": "Fruits and Vegetables", "wo": "Wënd ak legim"}', 'Produits frais du marché', 1),
('cccccccc-cccc-cccc-cccc-cccccccccccc', 'Épicerie sèche', '{"en": "Dry goods", "wo": "Lekk bu wees"}', 'Riz, pâtes, conserves', 2);

-- Insérer des produits de test
INSERT INTO products (merchant_id, category_id, name, name_translations, description, description_translations, price, image_url, is_available, is_featured, sort_order) VALUES
-- Restaurant sénégalais
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', (SELECT id FROM product_categories WHERE name = 'Plats principaux' AND merchant_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'), 'Thieboudienne rouge', '{"en": "Red thieboudienne", "wo": "Ceebu jën u xonq"}', 'Riz au poisson avec légumes, sauce tomate', '{"en": "Rice with fish and vegetables in tomato sauce", "wo": "Ceeb ak jën ak legim ci sauce tomate"}', 2500, 'https://example.com/thieb-rouge.jpg', true, true, 1),

('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', (SELECT id FROM product_categories WHERE name = 'Plats principaux' AND merchant_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'), 'Thieboudienne blanc', '{"en": "White thieboudienne", "wo": "Ceebu jën u weex"}', 'Riz au poisson sans sauce tomate', '{"en": "Rice with fish without tomato sauce", "wo": "Ceeb ak jën amul sauce tomate"}', 2000, 'https://example.com/thieb-blanc.jpg', true, false, 2),

('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', (SELECT id FROM product_categories WHERE name = 'Grillades' AND merchant_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'), 'Yassa Poulet', '{"en": "Chicken Yassa", "wo": "Yassa ginaar"}', 'Poulet mariné aux oignons et citron', '{"en": "Marinated chicken with onions and lemon", "wo": "Ginaar bu mariné ak jaxase ak citron"}', 3000, 'https://example.com/yassa-poulet.jpg', true, true, 1),

-- Restaurant kenyan
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', (SELECT id FROM product_categories WHERE name = 'Main Courses' AND merchant_id = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'), 'Nyama Choma with Ugali', '{"sw": "Nyama choma na ugali"}', 'Grilled meat served with ugali and vegetables', '{"sw": "Nyama ya mchuzi na ugali na mboga"}', 800, 'https://example.com/nyama-choma.jpg', true, true, 1),

('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', (SELECT id FROM product_categories WHERE name = 'Main Courses' AND merchant_id = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'), 'Pilau Rice', '{"sw": "Wali wa pilau"}', 'Spiced rice with meat and vegetables', '{"sw": "Wali wa viungo na nyama na mboga"}', 600, 'https://example.com/pilau.jpg', true, false, 2),

-- Épicerie
('cccccccc-cccc-cccc-cccc-cccccccccccc', (SELECT id FROM product_categories WHERE name = 'Fruits et Légumes' AND merchant_id = 'cccccccc-cccc-cccc-cccc-cccccccccccc'), 'Tomates (1kg)', '{"en": "Tomatoes (1kg)", "wo": "Tomat (1kg)"}', 'Tomates fraîches du marché', '{"en": "Fresh market tomatoes", "wo": "Tomat bu bees ci marché"}', 800, 'https://example.com/tomates.jpg', true, false, 1),

('cccccccc-cccc-cccc-cccc-cccccccccccc', (SELECT id FROM product_categories WHERE name = 'Épicerie sèche' AND merchant_id = 'cccccccc-cccc-cccc-cccc-cccccccccccc'), 'Riz brisé (5kg)', '{"en": "Broken rice (5kg)", "wo": "Ceeb bu bàyyi (5kg)"}', 'Riz brisé de qualité supérieure', '{"en": "Premium quality broken rice", "wo": "Ceeb bu bàyyi bu baax"}', 3500, 'https://example.com/riz-brise.jpg', true, true, 1);

-- Insérer des profils de livreurs
INSERT INTO delivery_riders (id, service_city_id, vehicle_type, vehicle_make, vehicle_model, vehicle_plate_number, vehicle_color, current_status, is_verified, is_active, max_delivery_distance_km, preferred_delivery_types) VALUES
('66666666-6666-6666-6666-666666666666', (SELECT id FROM cities WHERE name = 'Dakar'), 'motorcycle', 'Yamaha', 'DT 125', 'DK-1234-AB', 'Rouge', 'available', true, true, 15, ARRAY['food', 'package']),
('77777777-7777-7777-7777-777777777777', (SELECT id FROM cities WHERE name = 'Nairobi'), 'motorcycle', 'Honda', 'CB 150', 'KCA-567B', 'Blue', 'available', true, true, 20, ARRAY['food', 'grocery']);

-- Insérer des commandes de test
INSERT INTO orders (id, order_number, customer_id, merchant_id, delivery_type, status, pickup_address, pickup_location, delivery_address_id, delivery_address, delivery_location, delivery_zone_id, subtotal_amount, delivery_fee, service_fee, total_amount, currency_code, delivery_instructions, created_at) VALUES
-- Commande livrée
('dddddddd-dddd-dddd-dddd-dddddddddddd', 'ORD-20241201-0001', '11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'food', 'delivered', 'Avenue Pompidou, Plateau', ST_Point(-17.4460, 14.6910, 4326), (SELECT id FROM addresses WHERE user_id = '11111111-1111-1111-1111-111111111111' AND is_default = true), 'Rue 10, Plateau', ST_Point(-17.4450, 14.6900, 4326), (SELECT id FROM delivery_zones WHERE name = 'Plateau'), 2500, 500, 100, 3100, 'XOF', 'Sonner à l''interphone', NOW() - INTERVAL '2 hours'),

-- Commande en cours
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'ORD-20241201-0002', '22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'food', 'confirmed', 'Tom Mboya Street, CBD', ST_Point(36.8210, -1.2890, 4326), (SELECT id FROM addresses WHERE user_id = '22222222-2222-2222-2222-222222222222' AND is_default = true), 'Kenyatta Avenue', ST_Point(36.8200, -1.2900, 4326), (SELECT id FROM delivery_zones WHERE name = 'CBD'), 800, 150, 50, 1000, 'KES', 'Call when you arrive', NOW() - INTERVAL '30 minutes');

-- Insérer des articles de commande
INSERT INTO order_items (order_id, product_id, product_name, unit_price, quantity, total_price) VALUES
-- Commande 1
('dddddddd-dddd-dddd-dddd-dddddddddddd', (SELECT id FROM products WHERE name = 'Thieboudienne rouge'), 'Thieboudienne rouge', 2500, 1, 2500),

-- Commande 2
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', (SELECT id FROM products WHERE name = 'Nyama Choma with Ugali'), 'Nyama Choma with Ugali', 800, 1, 800);

-- Insérer des paiements de test
INSERT INTO payments (order_id, payment_method, provider_name, amount, currency_code, status, external_transaction_id, completed_at) VALUES
('dddddddd-dddd-dddd-dddd-dddddddddddd', 'mobile_money', 'orange_money', 3100, 'XOF', 'completed', 'OM123456789', NOW() - INTERVAL '2 hours'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'mobile_money', 'mpesa', 1000, 'KES', 'completed', 'MP987654321', NOW() - INTERVAL '25 minutes');

-- Insérer des évaluations de test
INSERT INTO ratings (order_id, rater_id, rated_id, rated_type, rating, comment) VALUES
('dddddddd-dddd-dddd-dddd-dddddddddddd', '11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'merchant', 5, 'Excellent thieboudienne, très savoureux et livraison rapide!'),
('dddddddd-dddd-dddd-dddd-dddddddddddd', '11111111-1111-1111-1111-111111111111', '66666666-6666-6666-6666-666666666666', 'rider', 4, 'Livreur très poli et ponctuel');

-- Insérer des notifications de test
INSERT INTO notifications (user_id, notification_type, title, body, data, channels, status, sent_at) VALUES
('11111111-1111-1111-1111-111111111111', 'order_delivered', 'Commande livrée', 'Votre commande ORD-20241201-0001 a été livrée avec succès!', '{"order_id": "dddddddd-dddd-dddd-dddd-dddddddddddd"}', ARRAY['push', 'sms'], 'delivered', NOW() - INTERVAL '2 hours'),
('22222222-2222-2222-2222-222222222222', 'order_confirmed', 'Order confirmed', 'Your order ORD-20241201-0002 has been confirmed and is being prepared', '{"order_id": "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee"}', ARRAY['push'], 'delivered', NOW() - INTERVAL '25 minutes');

-- Insérer des portefeuilles de test
INSERT INTO user_wallets (user_id, balance, currency_code) VALUES
('11111111-1111-1111-1111-111111111111', 5000, 'XOF'), -- 50 XOF de solde
('22222222-2222-2222-2222-222222222222', 500, 'KES'),  -- 5 KES de solde
('33333333-3333-3333-3333-333333333333', 1000, 'GHS'); -- 10 GHS de solde

-- Insérer des transactions de portefeuille de test
INSERT INTO wallet_transactions (wallet_id, transaction_type, amount, balance_before, balance_after, reference_type, reference_id, description) VALUES
((SELECT id FROM user_wallets WHERE user_id = '11111111-1111-1111-1111-111111111111'), 'credit', 10000, 0, 10000, 'topup', NULL, 'Rechargement initial'),
((SELECT id FROM user_wallets WHERE user_id = '11111111-1111-1111-1111-111111111111'), 'debit', 5000, 10000, 5000, 'order', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 'Paiement commande ORD-20241201-0001');

-- Mettre à jour les statistiques des commerçants
UPDATE merchants SET 
    average_rating = 5.0,
    total_ratings = 1
WHERE id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

-- Mettre à jour les statistiques des livreurs
UPDATE delivery_riders SET 
    total_deliveries = 1,
    successful_deliveries = 1,
    average_rating = 4.0,
    total_ratings = 1
WHERE id = '66666666-6666-6666-6666-666666666666';

-- Insérer des sessions utilisateur de test
INSERT INTO user_sessions (user_id, session_token, device_id, device_type, device_name, os_name, app_version, ip_address, expires_at) VALUES
('11111111-1111-1111-1111-111111111111', 'test_session_token_1', 'device_001', 'mobile', 'iPhone 12', 'iOS', '1.0.0', '*************', NOW() + INTERVAL '30 days'),
('22222222-2222-2222-2222-222222222222', 'test_session_token_2', 'device_002', 'mobile', 'Samsung Galaxy', 'Android', '1.0.0', '*************', NOW() + INTERVAL '30 days');

-- Insérer des promotions de test
INSERT INTO promotions (name, description, code, discount_type, discount_value, min_order_amount, max_uses_total, max_uses_per_user, valid_from, valid_until, applicable_delivery_types, first_order_only) VALUES
('Test Promo 20%', 'Promotion de test 20% de réduction', 'TEST20', 'percentage', 20, 1000, 100, 1, NOW() - INTERVAL '1 day', NOW() + INTERVAL '30 days', ARRAY['food'], false),
('Livraison Gratuite', 'Livraison gratuite pour commandes > 2000', 'FREEDELIVERY', 'free_delivery', 100, 2000, 500, 3, NOW() - INTERVAL '1 day', NOW() + INTERVAL '60 days', ARRAY['food', 'grocery'], false);

-- Insérer des logs d'audit de test
INSERT INTO audit_logs (user_id, user_role, action, resource_type, resource_id, new_values, ip_address) VALUES
('11111111-1111-1111-1111-111111111111', 'customer', 'create', 'order', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '{"status": "pending", "total_amount": 3100}', '*************'),
('44444444-4444-4444-4444-444444444444', 'merchant', 'update', 'order', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '{"status": "confirmed"}', '*************');

-- Rafraîchir les vues matérialisées si elles existent
-- REFRESH MATERIALIZED VIEW merchant_stats;
-- REFRESH MATERIALIZED VIEW rider_stats;
-- REFRESH MATERIALIZED VIEW popular_delivery_zones;
