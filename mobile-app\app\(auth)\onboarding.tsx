// =====================================================
// ÉCRAN ONBOARDING AFRICAIN - US-C001
// =====================================================

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Animated,
  ScrollView,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, {
  Path,
  Circle,
  Rect,
  Ellipse,
  G,
  Defs,
  LinearGradient as SvgLinearGradient,
  Stop
} from 'react-native-svg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../../constants/Colors';

const { width, height } = Dimensions.get('window');

interface OnboardingSlide {
  id: string;
  title: string;
  description: string;
  illustration: React.ReactNode;
  backgroundColor: string;
}

// Composant pour l'illustration du restaurant/food truck
const RestaurantIllustration = () => (
  <Svg width={width * 0.8} height={height * 0.4} viewBox="0 0 320 240">
    {/* Arrière-plan avec dégradé */}
    <Defs>
      <SvgLinearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <Stop offset="0%" stopColor="#F4E4BC" />
        <Stop offset="100%" stopColor="#E8D5A3" />
      </SvgLinearGradient>
      <SvgLinearGradient id="truckGradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <Stop offset="0%" stopColor="#F4A261" />
        <Stop offset="100%" stopColor="#E76F51" />
      </SvgLinearGradient>
    </Defs>

    {/* Arrière-plan */}
    <Rect width="320" height="240" fill="url(#skyGradient)" />

    {/* Soleil */}
    <Circle cx="280" cy="40" r="25" fill="#FFF3CD" opacity="0.8" />

    {/* Arbre */}
    <Circle cx="50" cy="120" r="35" fill="#2D5016" />
    <Rect x="45" y="140" width="10" height="30" fill="#8B4513" />

    {/* Food truck */}
    <G transform="translate(80, 120)">
      {/* Corps du camion */}
      <Rect x="0" y="20" width="160" height="60" rx="8" fill="url(#truckGradient)" />

      {/* Cabine */}
      <Rect x="0" y="0" width="40" height="50" rx="6" fill="#E76F51" />

      {/* Fenêtres */}
      <Rect x="5" y="5" width="30" height="20" rx="3" fill="#87CEEB" />
      <Rect x="50" y="25" width="25" height="15" rx="2" fill="#87CEEB" />
      <Rect x="85" y="25" width="25" height="15" rx="2" fill="#87CEEB" />
      <Rect x="120" y="25" width="25" height="15" rx="2" fill="#87CEEB" />

      {/* Toit avec équipement */}
      <Rect x="45" y="15" width="110" height="8" fill="#2F4F4F" />
      <Rect x="60" y="10" width="15" height="8" fill="#696969" />
      <Rect x="90" y="8" width="20" height="10" fill="#FF6347" />

      {/* Roues */}
      <Circle cx="25" cy="85" r="12" fill="#2F2F2F" />
      <Circle cx="25" cy="85" r="8" fill="#C0C0C0" />
      <Circle cx="135" cy="85" r="12" fill="#2F2F2F" />
      <Circle cx="135" cy="85" r="8" fill="#C0C0C0" />

      {/* Détails décoratifs */}
      <Rect x="45" y="45" width="100" height="25" fill="#FFE4B5" />
      <Text x="95" y="62" textAnchor="middle" fontSize="12" fill="#8B4513">FOOD</Text>
    </G>

    {/* Sol */}
    <Rect x="0" y="200" width="320" height="40" fill="#DEB887" />
  </Svg>
);

// Composant pour l'illustration de livraison
const DeliveryIllustration = () => (
  <Svg width={width * 0.8} height={height * 0.4} viewBox="0 0 320 240">
    <Defs>
      <SvgLinearGradient id="deliveryBg" x1="0%" y1="0%" x2="0%" y2="100%">
        <Stop offset="0%" stopColor="#F8F9FA" />
        <Stop offset="100%" stopColor="#E9ECEF" />
      </SvgLinearGradient>
    </Defs>

    {/* Arrière-plan */}
    <Rect width="320" height="240" fill="url(#deliveryBg)" />

    {/* Sol */}
    <Ellipse cx="160" cy="220" rx="120" ry="15" fill="#DDD" opacity="0.6" />

    {/* Plantes décoratives */}
    <G transform="translate(50, 180)">
      <Path d="M0 20 Q5 10 10 20 Q15 10 20 20 Q25 15 30 25" fill="#4CAF50" />
      <Path d="M35 25 Q40 15 45 25 Q50 10 55 25" fill="#66BB6A" />
    </G>

    <G transform="translate(220, 185)">
      <Path d="M0 15 Q8 5 16 15 Q24 0 32 15" fill="#4CAF50" />
      <Path d="M36 15 Q44 8 52 18" fill="#66BB6A" />
    </G>

    {/* Livreur */}
    <G transform="translate(120, 80)">
      {/* Corps */}
      <Ellipse cx="40" cy="80" rx="25" ry="35" fill="#2C3E50" />

      {/* Sac à dos */}
      <Ellipse cx="25" cy="70" rx="15" ry="25" fill="#34495E" />
      <Rect x="20" y="60" width="10" height="20" fill="#2C3E50" />

      {/* Tête */}
      <Circle cx="40" cy="35" r="18" fill="#D4A574" />

      {/* Casquette */}
      <Path d="M22 25 Q40 15 58 25 Q58 30 40 32 Q22 30 22 25" fill="#2C3E50" />
      <Ellipse cx="40" cy="25" rx="20" ry="8" fill="#34495E" />

      {/* Bras gauche */}
      <Ellipse cx="15" cy="65" rx="8" ry="20" fill="#D4A574" transform="rotate(-20 15 65)" />

      {/* Bras droit tenant les colis */}
      <Ellipse cx="65" cy="65" rx="8" ry="20" fill="#D4A574" transform="rotate(20 65 65)" />

      {/* Jambes */}
      <Ellipse cx="30" cy="125" rx="8" ry="25" fill="#2C3E50" transform="rotate(10 30 125)" />
      <Ellipse cx="50" cy="125" rx="8" ry="25" fill="#2C3E50" transform="rotate(-10 50 125)" />

      {/* Chaussures */}
      <Ellipse cx="25" cy="145" rx="12" ry="6" fill="#1A1A1A" />
      <Ellipse cx="55" cy="145" rx="12" ry="6" fill="#1A1A1A" />
    </G>

    {/* Colis dans les bras */}
    <G transform="translate(170, 100)">
      <Rect x="0" y="0" width="25" height="20" rx="2" fill="#D4A574" />
      <Rect x="2" y="8" width="21" height="4" fill="#8B4513" />
      <Rect x="0" y="25" width="20" height="18" rx="2" fill="#DEB887" />
      <Rect x="2" y="32" width="16" height="3" fill="#8B4513" />
    </G>
  </Svg>
);

// Composant pour l'illustration de satisfaction à domicile
const HomeDeliveryIllustration = () => (
  <Svg width={width * 0.8} height={height * 0.4} viewBox="0 0 320 240">
    <Defs>
      <SvgLinearGradient id="homeBg" x1="0%" y1="0%" x2="0%" y2="100%">
        <Stop offset="0%" stopColor="#F5F5DC" />
        <Stop offset="100%" stopColor="#F0E68C" />
      </SvgLinearGradient>
      <SvgLinearGradient id="wallGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <Stop offset="0%" stopColor="#F5DEB3" />
        <Stop offset="100%" stopColor="#DDD" />
      </SvgLinearGradient>
    </Defs>

    {/* Arrière-plan */}
    <Rect width="320" height="240" fill="url(#homeBg)" />

    {/* Mur de la maison */}
    <Rect x="20" y="60" width="280" height="140" fill="url(#wallGradient)" />

    {/* Fenêtre */}
    <Rect x="40" y="80" width="60" height="80" fill="#87CEEB" stroke="#8B4513" strokeWidth="3" />
    <Rect x="67" y="80" width="3" height="80" fill="#8B4513" />
    <Rect x="40" y="117" width="60" height="3" fill="#8B4513" />

    {/* Lampe suspendue */}
    <Circle cx="200" cy="100" r="15" fill="#98FB98" opacity="0.8" />
    <Rect x="198" y="85" width="4" height="15" fill="#8B4513" />
    <Rect x="195" y="82" width="10" height="3" fill="#D2691E" />

    {/* Plante en pot */}
    <Ellipse cx="270" cy="180" rx="15" ry="10" fill="#8B4513" />
    <Path d="M255 170 Q265 150 275 170 Q280 155 285 175" fill="#228B22" />

    {/* Personnages */}
    {/* Femme (à gauche) */}
    <G transform="translate(120, 120)">
      {/* Corps */}
      <Ellipse cx="0" cy="40" rx="20" ry="30" fill="#FFFFFF" />

      {/* Tête */}
      <Circle cx="0" cy="0" r="15" fill="#D4A574" />

      {/* Cheveux */}
      <Path d="M-15 -10 Q0 -20 15 -10 Q15 0 0 5 Q-15 0 -15 -10" fill="#2F1B14" />

      {/* Bras gauche */}
      <Ellipse cx="-25" cy="25" rx="6" ry="15" fill="#D4A574" transform="rotate(-30 -25 25)" />

      {/* Bras droit tenant le colis */}
      <Ellipse cx="25" cy="25" rx="6" ry="15" fill="#D4A574" transform="rotate(30 25 25)" />

      {/* Pantalon */}
      <Ellipse cx="0" cy="65" rx="18" ry="25" fill="#4169E1" />

      {/* Jambes */}
      <Ellipse cx="-8" cy="85" rx="5" ry="15" fill="#4169E1" />
      <Ellipse cx="8" cy="85" rx="5" ry="15" fill="#4169E1" />
    </G>

    {/* Homme (à droite) */}
    <G transform="translate(180, 120)">
      {/* Corps */}
      <Ellipse cx="0" cy="40" rx="22" ry="32" fill="#FF6347" />

      {/* Tête */}
      <Circle cx="0" cy="0" r="16" fill="#D4A574" />

      {/* Cheveux */}
      <Path d="M-16 -12 Q0 -22 16 -12 Q16 -5 8 0 Q0 -2 -8 0 Q-16 -5 -16 -12" fill="#2F1B14" />

      {/* Bras gauche tenant le colis */}
      <Ellipse cx="-28" cy="25" rx="7" ry="16" fill="#D4A574" transform="rotate(-30 -28 25)" />

      {/* Bras droit */}
      <Ellipse cx="28" cy="25" rx="7" ry="16" fill="#D4A574" transform="rotate(30 28 25)" />

      {/* Pantalon */}
      <Ellipse cx="0" cy="68" rx="20" ry="28" fill="#2F4F4F" />

      {/* Jambes */}
      <Ellipse cx="-10" cy="90" rx="6" ry="18" fill="#2F4F4F" />
      <Ellipse cx="10" cy="90" rx="6" ry="18" fill="#2F4F4F" />
    </G>

    {/* Colis partagé */}
    <Rect x="145" y="145" width="30" height="25" rx="3" fill="#D4A574" />
    <Rect x="147" y="155" width="26" height="4" fill="#228B22" />
    <Circle cx="160" cy="157" r="3" fill="#228B22" />
    <Path d="M157 154 Q160 150 163 154" fill="#228B22" strokeWidth="1" stroke="#228B22" />
  </Svg>
);

const slides: OnboardingSlide[] = [
  {
    id: '1',
    title: 'Commandez vos repas favoris',
    description: 'Commandez dans vos restaurants préférés et faites-vous livrer à votre porte',
    illustration: <RestaurantIllustration />,
    backgroundColor: '#FFF8E7',
  },
  {
    id: '2',
    title: 'Envoyez vos colis rapidement',
    description: 'Envoyez des colis dans votre ville et au-delà. Nous nous occupons de tout, de l\'enlèvement à la livraison.',
    illustration: <DeliveryIllustration />,
    backgroundColor: '#F8F9FA',
  },
  {
    id: '3',
    title: 'Recevez vos achats à domicile',
    description: 'Profitez de la commodité de recevoir vos achats directement chez vous, sans tracas ni déplacements.',
    illustration: <HomeDeliveryIllustration />,
    backgroundColor: '#F5F5DC',
  },
];

export default function OnboardingScreen() {
  const [currentPage, setCurrentPage] = useState(0);
  const slideAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(1)).current;

  const handleNext = () => {
    if (currentPage < slides.length - 1) {
      // Animation de sortie
      Animated.timing(fadeAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start(() => {
        const nextPage = currentPage + 1;
        setCurrentPage(nextPage);

        // Animation d'entrée
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      });
    } else {
      handleGetStarted();
    }
  };

  const handleSkip = async () => {
    await AsyncStorage.setItem('hasSeenOnboarding', 'true');
    router.replace('/(auth)/register');
  };

  const handleGetStarted = async () => {
    await AsyncStorage.setItem('hasSeenOnboarding', 'true');
    router.replace('/(auth)/register');
  };

  return (
    <View style={[styles.container, { backgroundColor: slides[currentPage].backgroundColor }]}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header avec bouton Skip/Passer */}
        <View style={styles.header}>
          {currentPage < slides.length - 1 && (
            <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
              <Text style={styles.skipText}>Passer</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Contenu principal */}
        <Animated.View style={[styles.content, { opacity: fadeAnimation }]}>
          {/* Illustration */}
          <View style={styles.illustrationContainer}>
            {slides[currentPage].illustration}
          </View>

          {/* Texte */}
          <View style={styles.textContainer}>
            <Text style={styles.title}>{slides[currentPage].title}</Text>
            <Text style={styles.description}>{slides[currentPage].description}</Text>
          </View>

          {/* Indicateurs de page */}
          <View style={styles.pagination}>
            {slides.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.dot,
                  index === currentPage && styles.activeDot,
                ]}
              />
            ))}
          </View>

          {/* Boutons d'action */}
          <View style={styles.actions}>
            {currentPage === slides.length - 1 ? (
              <TouchableOpacity style={styles.primaryButton} onPress={handleGetStarted}>
                <Text style={styles.primaryButtonText}>Commencer</Text>
              </TouchableOpacity>
            ) : (
              <View style={styles.buttonRow}>
                <TouchableOpacity style={styles.secondaryButton} onPress={handleNext}>
                  <Text style={styles.secondaryButtonText}>Suivant</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={handleSkip}>
                  <Text style={styles.skipBottomText}>Passer</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </Animated.View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 24,
    paddingTop: 16,
    height: 60,
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  skipText: {
    fontSize: 16,
    color: Colors.neutral[600],
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  illustrationContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  textContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral[900],
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 32,
  },
  description: {
    fontSize: 16,
    color: Colors.neutral[600],
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: width * 0.8,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.neutral[300],
  },
  activeDot: {
    backgroundColor: Colors.secondary[500],
    width: 24,
  },
  actions: {
    paddingBottom: 32,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: Colors.secondary[500],
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
  },
  primaryButtonText: {
    color: Colors.neutral[0],
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: Colors.secondary[500],
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  secondaryButtonText: {
    color: Colors.neutral[0],
    fontSize: 16,
    fontWeight: '600',
  },
  skipBottomText: {
    fontSize: 16,
    color: Colors.neutral[600],
    fontWeight: '500',
  },
});
