// =====================================================
// ÉCRAN ONBOARDING AFRICAIN - US-C001
// =====================================================

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Animated,
  ScrollView,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, {
  Path,
  Circle,
  Rect,
  Ellipse,
  G,
  Defs,
  LinearGradient as SvgLinearGradient,
  Stop
} from 'react-native-svg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../../constants/Colors';

const { width, height } = Dimensions.get('window');

interface OnboardingSlide {
  id: string;
  title: string;
  description: string;
  illustration: React.ReactNode;
  backgroundColor: string;
}

// Composant pour l'illustration du restaurant/food truck avec animations
const RestaurantIllustration = () => {
  const sunRotation = useRef(new Animated.Value(0)).current;
  const truckBounce = useRef(new Animated.Value(0)).current;
  const smokeOpacity = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    // Animation du soleil qui tourne lentement
    Animated.loop(
      Animated.timing(sunRotation, {
        toValue: 1,
        duration: 20000,
        useNativeDriver: true,
      })
    ).start();

    // Animation du camion qui rebondit légèrement
    Animated.loop(
      Animated.sequence([
        Animated.timing(truckBounce, {
          toValue: -2,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(truckBounce, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Animation de la fumée qui pulse
    Animated.loop(
      Animated.sequence([
        Animated.timing(smokeOpacity, {
          toValue: 0.8,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(smokeOpacity, {
          toValue: 0.3,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const sunRotationInterpolate = sunRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Svg width={width * 0.8} height={height * 0.4} viewBox="0 0 320 240">
      {/* Arrière-plan avec dégradé */}
      <Defs>
        <SvgLinearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <Stop offset="0%" stopColor="#F4E4BC" />
          <Stop offset="100%" stopColor="#E8D5A3" />
        </SvgLinearGradient>
        <SvgLinearGradient id="truckGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <Stop offset="0%" stopColor="#F4A261" />
          <Stop offset="100%" stopColor="#E76F51" />
        </SvgLinearGradient>
      </Defs>

      {/* Arrière-plan */}
      <Rect width="320" height="240" fill="url(#skyGradient)" />

      {/* Nuages flottants */}
      <G opacity="0.6">
        <Ellipse cx="100" cy="50" rx="25" ry="12" fill="#FFFFFF" opacity="0.7" />
        <Ellipse cx="115" cy="45" rx="20" ry="10" fill="#FFFFFF" opacity="0.7" />
        <Ellipse cx="200" cy="35" rx="30" ry="15" fill="#FFFFFF" opacity="0.5" />
        <Ellipse cx="220" cy="30" rx="25" ry="12" fill="#FFFFFF" opacity="0.5" />
      </G>

      {/* Soleil animé */}
      <G transform={`translate(280, 40)`}>
        <Circle cx="0" cy="0" r="25" fill="#FFF3CD" opacity="0.8" />
        {/* Rayons du soleil */}
        <G opacity="0.6">
          <Rect x="-2" y="-35" width="4" height="10" fill="#FFF3CD" />
          <Rect x="-2" y="25" width="4" height="10" fill="#FFF3CD" />
          <Rect x="-35" y="-2" width="10" height="4" fill="#FFF3CD" />
          <Rect x="25" y="-2" width="10" height="4" fill="#FFF3CD" />
          <Rect x="-25" y="-25" width="8" height="4" fill="#FFF3CD" transform="rotate(45)" />
          <Rect x="17" y="-25" width="8" height="4" fill="#FFF3CD" transform="rotate(-45)" />
          <Rect x="-25" y="17" width="8" height="4" fill="#FFF3CD" transform="rotate(-45)" />
          <Rect x="17" y="17" width="8" height="4" fill="#FFF3CD" transform="rotate(45)" />
        </G>
      </G>

      {/* Arbre baobab africain */}
      <G transform="translate(50, 120)">
        {/* Tronc */}
        <Rect x="-5" y="20" width="10" height="30" fill="#8B4513" />
        {/* Feuillage en forme de baobab */}
        <Circle cx="0" cy="0" r="35" fill="#2D5016" />
        <Circle cx="-15" cy="-10" r="20" fill="#228B22" opacity="0.8" />
        <Circle cx="15" cy="-5" r="18" fill="#228B22" opacity="0.7" />
        <Circle cx="0" cy="15" r="25" fill="#2D5016" opacity="0.9" />
      </G>

      {/* Food truck animé */}
      <Animated.View style={{ transform: [{ translateY: truckBounce }] }}>
        <G transform="translate(80, 120)">
          {/* Corps du camion */}
          <Rect x="0" y="20" width="160" height="60" rx="8" fill="url(#truckGradient)" />

          {/* Cabine */}
          <Rect x="0" y="0" width="40" height="50" rx="6" fill="#E76F51" />

          {/* Fenêtres */}
          <Rect x="5" y="5" width="30" height="20" rx="3" fill="#87CEEB" />
          <Rect x="50" y="25" width="25" height="15" rx="2" fill="#87CEEB" />
          <Rect x="85" y="25" width="25" height="15" rx="2" fill="#87CEEB" />
          <Rect x="120" y="25" width="25" height="15" rx="2" fill="#87CEEB" />

          {/* Toit avec équipement */}
          <Rect x="45" y="15" width="110" height="8" fill="#2F4F4F" />
          <Rect x="60" y="10" width="15" height="8" fill="#696969" />
          <Rect x="90" y="8" width="20" height="10" fill="#FF6347" />

          {/* Roues */}
          <Circle cx="25" cy="85" r="12" fill="#2F2F2F" />
          <Circle cx="25" cy="85" r="8" fill="#C0C0C0" />
          <Circle cx="135" cy="85" r="12" fill="#2F2F2F" />
          <Circle cx="135" cy="85" r="8" fill="#C0C0C0" />

          {/* Détails décoratifs */}
          <Rect x="45" y="45" width="100" height="25" fill="#FFE4B5" />
          <Text x="95" y="62" textAnchor="middle" fontSize="12" fill="#8B4513">FOOD</Text>

          {/* Fumée de cuisine animée */}
          <Animated.View style={{ opacity: smokeOpacity }}>
            <G>
              <Circle cx="100" cy="8" r="3" fill="#E0E0E0" opacity="0.6" />
              <Circle cx="105" cy="5" r="4" fill="#E0E0E0" opacity="0.4" />
              <Circle cx="110" cy="2" r="3" fill="#E0E0E0" opacity="0.5" />
              <Circle cx="115" cy="-1" r="2" fill="#E0E0E0" opacity="0.3" />
            </G>
          </Animated.View>

          {/* Motifs africains sur le camion */}
          <G opacity="0.7">
            <Circle cx="70" cy="55" r="3" fill="#8B4513" />
            <Circle cx="80" cy="50" r="2" fill="#8B4513" />
            <Circle cx="90" cy="55" r="3" fill="#8B4513" />
            <Circle cx="100" cy="50" r="2" fill="#8B4513" />
            <Circle cx="110" cy="55" r="3" fill="#8B4513" />
          </G>
        </G>
      </Animated.View>

      {/* Sol avec texture */}
      <Rect x="0" y="200" width="320" height="40" fill="#DEB887" />
      <G opacity="0.3">
        <Ellipse cx="80" cy="210" rx="40" ry="8" fill="#CD853F" />
        <Ellipse cx="200" cy="215" rx="60" ry="10" fill="#CD853F" />
      </G>

      {/* Oiseaux volants */}
      <G opacity="0.4">
        <Path d="M150 60 Q155 55 160 60 Q155 65 150 60" fill="#8B4513" />
        <Path d="M170 50 Q175 45 180 50 Q175 55 170 50" fill="#8B4513" />
      </G>
    </Svg>
  );
};

// Composant pour l'illustration de livraison avec animations
const DeliveryIllustration = () => {
  const walkAnimation = useRef(new Animated.Value(0)).current;
  const packageFloat = useRef(new Animated.Value(0)).current;
  const plantsWave = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animation de marche du livreur
    Animated.loop(
      Animated.sequence([
        Animated.timing(walkAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(walkAnimation, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Animation flottante des colis
    Animated.loop(
      Animated.sequence([
        Animated.timing(packageFloat, {
          toValue: -3,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(packageFloat, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Animation des plantes qui bougent
    Animated.loop(
      Animated.sequence([
        Animated.timing(plantsWave, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(plantsWave, {
          toValue: 0,
          duration: 3000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const walkTranslate = walkAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 2],
  });

  const plantsRotate = plantsWave.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '3deg'],
  });

  return (
    <Svg width={width * 0.8} height={height * 0.4} viewBox="0 0 320 240">
      <Defs>
        <SvgLinearGradient id="deliveryBg" x1="0%" y1="0%" x2="0%" y2="100%">
          <Stop offset="0%" stopColor="#F8F9FA" />
          <Stop offset="100%" stopColor="#E9ECEF" />
        </SvgLinearGradient>
      </Defs>

      {/* Arrière-plan */}
      <Rect width="320" height="240" fill="url(#deliveryBg)" />

      {/* Chemin de livraison */}
      <Path
        d="M20 200 Q160 190 300 200"
        stroke="#DDD"
        strokeWidth="3"
        fill="none"
        strokeDasharray="5,5"
        opacity="0.6"
      />

      {/* Sol avec ombre */}
      <Ellipse cx="160" cy="220" rx="120" ry="15" fill="#DDD" opacity="0.6" />

      {/* Plantes décoratives animées */}
      <Animated.View style={{ transform: [{ rotate: plantsRotate }] }}>
        <G transform="translate(50, 180)">
          <Path d="M0 20 Q5 10 10 20 Q15 10 20 20 Q25 15 30 25" fill="#4CAF50" />
          <Path d="M35 25 Q40 15 45 25 Q50 10 55 25" fill="#66BB6A" />
          {/* Fleurs africaines */}
          <Circle cx="15" cy="15" r="2" fill="#FF6B6B" />
          <Circle cx="25" cy="12" r="1.5" fill="#FFE66D" />
          <Circle cx="40" cy="18" r="2" fill="#FF6B6B" />
        </G>
      </Animated.View>

      <Animated.View style={{ transform: [{ rotate: plantsRotate }] }}>
        <G transform="translate(220, 185)">
          <Path d="M0 15 Q8 5 16 15 Q24 0 32 15" fill="#4CAF50" />
          <Path d="M36 15 Q44 8 52 18" fill="#66BB6A" />
          {/* Fleurs africaines */}
          <Circle cx="10" cy="10" r="1.5" fill="#FFE66D" />
          <Circle cx="20" cy="8" r="2" fill="#FF6B6B" />
          <Circle cx="35" cy="12" r="1.5" fill="#FFE66D" />
        </G>
      </Animated.View>

      {/* Livreur animé */}
      <Animated.View style={{ transform: [{ translateX: walkTranslate }] }}>
        <G transform="translate(120, 80)">
          {/* Corps */}
          <Ellipse cx="40" cy="80" rx="25" ry="35" fill="#2C3E50" />

          {/* Sac à dos avec logo */}
          <Ellipse cx="25" cy="70" rx="15" ry="25" fill="#34495E" />
          <Rect x="20" y="60" width="10" height="20" fill="#2C3E50" />
          <Circle cx="25" cy="65" r="5" fill="#FF6B6B" />
          <Text x="25" y="68" textAnchor="middle" fontSize="6" fill="#FFFFFF">📦</Text>

          {/* Tête */}
          <Circle cx="40" cy="35" r="18" fill="#D4A574" />

          {/* Casquette avec logo */}
          <Path d="M22 25 Q40 15 58 25 Q58 30 40 32 Q22 30 22 25" fill="#2C3E50" />
          <Ellipse cx="40" cy="25" rx="20" ry="8" fill="#34495E" />
          <Circle cx="40" cy="25" r="3" fill="#FF6B6B" />

          {/* Visage souriant */}
          <Circle cx="35" cy="32" r="1" fill="#2C3E50" />
          <Circle cx="45" cy="32" r="1" fill="#2C3E50" />
          <Path d="M35 38 Q40 42 45 38" stroke="#2C3E50" strokeWidth="1" fill="none" />

          {/* Bras gauche */}
          <Ellipse cx="15" cy="65" rx="8" ry="20" fill="#D4A574" transform="rotate(-20 15 65)" />

          {/* Bras droit tenant les colis */}
          <Ellipse cx="65" cy="65" rx="8" ry="20" fill="#D4A574" transform="rotate(20 65 65)" />

          {/* Jambes animées */}
          <Animated.View style={{ transform: [{ rotate: `${walkTranslate * 2}deg` }] }}>
            <Ellipse cx="30" cy="125" rx="8" ry="25" fill="#2C3E50" transform="rotate(10 30 125)" />
          </Animated.View>
          <Animated.View style={{ transform: [{ rotate: `${-walkTranslate * 2}deg` }] }}>
            <Ellipse cx="50" cy="125" rx="8" ry="25" fill="#2C3E50" transform="rotate(-10 50 125)" />
          </Animated.View>

          {/* Chaussures */}
          <Ellipse cx="25" cy="145" rx="12" ry="6" fill="#1A1A1A" />
          <Ellipse cx="55" cy="145" rx="12" ry="6" fill="#1A1A1A" />

          {/* Lignes de mouvement */}
          <G opacity="0.3">
            <Path d="M10 140 Q15 135 20 140" stroke="#DDD" strokeWidth="2" fill="none" />
            <Path d="M5 145 Q10 140 15 145" stroke="#DDD" strokeWidth="1" fill="none" />
          </G>
        </G>
      </Animated.View>

      {/* Colis flottants dans les bras */}
      <Animated.View style={{ transform: [{ translateY: packageFloat }, { translateX: walkTranslate }] }}>
        <G transform="translate(170, 100)">
          <Rect x="0" y="0" width="25" height="20" rx="2" fill="#D4A574" />
          <Rect x="2" y="8" width="21" height="4" fill="#8B4513" />
          <Rect x="0" y="25" width="20" height="18" rx="2" fill="#DEB887" />
          <Rect x="2" y="32" width="16" height="3" fill="#8B4513" />
          {/* Étiquettes de livraison */}
          <Rect x="5" y="5" width="8" height="6" fill="#FFFFFF" opacity="0.8" />
          <Rect x="5" y="28" width="6" height="4" fill="#FFFFFF" opacity="0.8" />
        </G>
      </Animated.View>

      {/* Particules de poussière */}
      <G opacity="0.4">
        <Circle cx="140" cy="200" r="1" fill="#DDD" />
        <Circle cx="145" cy="205" r="0.5" fill="#DDD" />
        <Circle cx="150" cy="202" r="1" fill="#DDD" />
        <Circle cx="155" cy="207" r="0.5" fill="#DDD" />
      </G>
    </Svg>
  );
};

// Composant pour l'illustration de satisfaction à domicile avec animations
const HomeDeliveryIllustration = () => {
  const lampSwing = useRef(new Animated.Value(0)).current;
  const packageGlow = useRef(new Animated.Value(0.5)).current;
  const plantGrow = useRef(new Animated.Value(0)).current;
  const heartFloat = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animation de la lampe qui se balance
    Animated.loop(
      Animated.sequence([
        Animated.timing(lampSwing, {
          toValue: 1,
          duration: 2500,
          useNativeDriver: true,
        }),
        Animated.timing(lampSwing, {
          toValue: 0,
          duration: 2500,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Animation du colis qui brille
    Animated.loop(
      Animated.sequence([
        Animated.timing(packageGlow, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(packageGlow, {
          toValue: 0.5,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Animation de la plante qui grandit
    Animated.loop(
      Animated.sequence([
        Animated.timing(plantGrow, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(plantGrow, {
          toValue: 0,
          duration: 3000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Animation des cœurs flottants
    Animated.loop(
      Animated.sequence([
        Animated.timing(heartFloat, {
          toValue: -10,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(heartFloat, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const lampRotate = lampSwing.interpolate({
    inputRange: [0, 1],
    outputRange: ['-3deg', '3deg'],
  });

  const plantScale = plantGrow.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.1],
  });

  return (
    <Svg width={width * 0.8} height={height * 0.4} viewBox="0 0 320 240">
      <Defs>
        <SvgLinearGradient id="homeBg" x1="0%" y1="0%" x2="0%" y2="100%">
          <Stop offset="0%" stopColor="#F5F5DC" />
          <Stop offset="100%" stopColor="#F0E68C" />
        </SvgLinearGradient>
        <SvgLinearGradient id="wallGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <Stop offset="0%" stopColor="#F5DEB3" />
          <Stop offset="100%" stopColor="#DDD" />
        </SvgLinearGradient>
      </Defs>

      {/* Arrière-plan */}
      <Rect width="320" height="240" fill="url(#homeBg)" />

      {/* Mur de la maison avec texture */}
      <Rect x="20" y="60" width="280" height="140" fill="url(#wallGradient)" />

      {/* Motifs décoratifs africains sur le mur */}
      <G opacity="0.3">
        <Circle cx="250" cy="90" r="8" fill="#D2691E" />
        <Circle cx="260" cy="100" r="6" fill="#D2691E" />
        <Circle cx="240" cy="110" r="7" fill="#D2691E" />
        <Path d="M245 95 L255 95 L250 105 Z" fill="#8B4513" />
      </G>

      {/* Fenêtre avec reflet */}
      <Rect x="40" y="80" width="60" height="80" fill="#87CEEB" stroke="#8B4513" strokeWidth="3" />
      <Rect x="67" y="80" width="3" height="80" fill="#8B4513" />
      <Rect x="40" y="117" width="60" height="3" fill="#8B4513" />
      {/* Reflet dans la fenêtre */}
      <Rect x="45" y="85" width="15" height="25" fill="#FFFFFF" opacity="0.3" />

      {/* Lampe suspendue animée */}
      <Animated.View style={{ transform: [{ rotate: lampRotate }] }}>
        <G transform="translate(200, 100)">
          <Circle cx="0" cy="0" r="15" fill="#98FB98" opacity="0.8" />
          <Rect x="-2" y="-15" width="4" height="15" fill="#8B4513" />
          <Rect x="-5" y="-18" width="10" height="3" fill="#D2691E" />
          {/* Lumière douce */}
          <Circle cx="0" cy="0" r="25" fill="#98FB98" opacity="0.2" />
        </G>
      </Animated.View>

      {/* Plante en pot animée */}
      <Animated.View style={{ transform: [{ scale: plantScale }] }}>
        <G transform="translate(270, 180)">
          <Ellipse cx="0" cy="0" rx="15" ry="10" fill="#8B4513" />
          <Path d="M-15 -10 Q-5 -30 5 -10 Q10 -25 15 -5" fill="#228B22" />
          {/* Fleurs sur la plante */}
          <Circle cx="-8" cy="-15" r="2" fill="#FF6B6B" />
          <Circle cx="5" cy="-18" r="1.5" fill="#FFE66D" />
          <Circle cx="12" cy="-12" r="2" fill="#FF6B6B" />
        </G>
      </Animated.View>

      {/* Personnages animés */}
      {/* Femme (à gauche) */}
      <G transform="translate(120, 120)">
        {/* Corps */}
        <Ellipse cx="0" cy="40" rx="20" ry="30" fill="#FFFFFF" />

        {/* Tête */}
        <Circle cx="0" cy="0" r="15" fill="#D4A574" />

        {/* Cheveux avec style africain */}
        <Path d="M-15 -10 Q0 -20 15 -10 Q15 0 0 5 Q-15 0 -15 -10" fill="#2F1B14" />
        <Circle cx="-8" cy="-8" r="3" fill="#2F1B14" />
        <Circle cx="8" cy="-8" r="3" fill="#2F1B14" />

        {/* Visage souriant */}
        <Circle cx="-4" cy="-2" r="1" fill="#2C3E50" />
        <Circle cx="4" cy="-2" r="1" fill="#2C3E50" />
        <Path d="M-5 3 Q0 7 5 3" stroke="#2C3E50" strokeWidth="1" fill="none" />

        {/* Bras gauche */}
        <Ellipse cx="-25" cy="25" rx="6" ry="15" fill="#D4A574" transform="rotate(-30 -25 25)" />

        {/* Bras droit tenant le colis */}
        <Ellipse cx="25" cy="25" rx="6" ry="15" fill="#D4A574" transform="rotate(30 25 25)" />

        {/* Pantalon avec motifs */}
        <Ellipse cx="0" cy="65" rx="18" ry="25" fill="#4169E1" />
        <Circle cx="-8" cy="60" r="2" fill="#FFFFFF" opacity="0.7" />
        <Circle cx="8" cy="70" r="2" fill="#FFFFFF" opacity="0.7" />

        {/* Jambes */}
        <Ellipse cx="-8" cy="85" rx="5" ry="15" fill="#4169E1" />
        <Ellipse cx="8" cy="85" rx="5" ry="15" fill="#4169E1" />
      </G>

      {/* Homme (à droite) */}
      <G transform="translate(180, 120)">
        {/* Corps */}
        <Ellipse cx="0" cy="40" rx="22" ry="32" fill="#FF6347" />

        {/* Tête */}
        <Circle cx="0" cy="0" r="16" fill="#D4A574" />

        {/* Cheveux */}
        <Path d="M-16 -12 Q0 -22 16 -12 Q16 -5 8 0 Q0 -2 -8 0 Q-16 -5 -16 -12" fill="#2F1B14" />

        {/* Visage souriant */}
        <Circle cx="-5" cy="-2" r="1" fill="#2C3E50" />
        <Circle cx="5" cy="-2" r="1" fill="#2C3E50" />
        <Path d="M-6 4 Q0 8 6 4" stroke="#2C3E50" strokeWidth="1" fill="none" />

        {/* Bras gauche tenant le colis */}
        <Ellipse cx="-28" cy="25" rx="7" ry="16" fill="#D4A574" transform="rotate(-30 -28 25)" />

        {/* Bras droit */}
        <Ellipse cx="28" cy="25" rx="7" ry="16" fill="#D4A574" transform="rotate(30 28 25)" />

        {/* Pantalon */}
        <Ellipse cx="0" cy="68" rx="20" ry="28" fill="#2F4F4F" />

        {/* Jambes */}
        <Ellipse cx="-10" cy="90" rx="6" ry="18" fill="#2F4F4F" />
        <Ellipse cx="10" cy="90" rx="6" ry="18" fill="#2F4F4F" />
      </G>

      {/* Colis partagé avec animation de brillance */}
      <Animated.View style={{ opacity: packageGlow }}>
        <G transform="translate(145, 145)">
          <Rect x="0" y="0" width="30" height="25" rx="3" fill="#D4A574" />
          <Rect x="2" y="10" width="26" height="4" fill="#228B22" />
          <Circle cx="15" cy="12" r="3" fill="#228B22" />
          <Path d="M12 9 Q15 5 18 9" fill="#228B22" strokeWidth="1" stroke="#228B22" />
          {/* Étiquette de livraison */}
          <Rect x="5" y="3" width="12" height="6" fill="#FFFFFF" opacity="0.9" />
          <Text x="11" y="7" textAnchor="middle" fontSize="4" fill="#2C3E50">✓</Text>
        </G>
      </Animated.View>

      {/* Cœurs flottants de satisfaction */}
      <Animated.View style={{ transform: [{ translateY: heartFloat }] }}>
        <G opacity="0.7">
          <Path d="M140 100 Q145 95 150 100 Q155 95 160 100 Q155 110 150 115 Q145 110 140 100" fill="#FF6B6B" />
          <Path d="M170 110 Q173 107 176 110 Q179 107 182 110 Q179 117 176 120 Q173 117 170 110" fill="#FF6B6B" />
          <Path d="M200 95 Q203 92 206 95 Q209 92 212 95 Q209 102 206 105 Q203 102 200 95" fill="#FFE66D" />
        </G>
      </Animated.View>

      {/* Étoiles de satisfaction */}
      <G opacity="0.6">
        <Path d="M130 80 L132 85 L137 85 L133 88 L135 93 L130 90 L125 93 L127 88 L123 85 L128 85 Z" fill="#FFE66D" />
        <Path d="M190 75 L191 78 L194 78 L192 80 L193 83 L190 81 L187 83 L188 80 L186 78 L189 78 Z" fill="#FFE66D" />
      </G>
    </Svg>
  );
};

const slides: OnboardingSlide[] = [
  {
    id: '1',
    title: 'Commandez vos repas favoris',
    description: 'Commandez dans vos restaurants préférés et faites-vous livrer à votre porte',
    illustration: <RestaurantIllustration />,
    backgroundColor: '#FFF8E7',
  },
  {
    id: '2',
    title: 'Envoyez vos colis rapidement',
    description: 'Envoyez des colis dans votre ville et au-delà. Nous nous occupons de tout, de l\'enlèvement à la livraison.',
    illustration: <DeliveryIllustration />,
    backgroundColor: '#F8F9FA',
  },
  {
    id: '3',
    title: 'Recevez vos achats à domicile',
    description: 'Profitez de la commodité de recevoir vos achats directement chez vous, sans tracas ni déplacements.',
    illustration: <HomeDeliveryIllustration />,
    backgroundColor: '#F5F5DC',
  },
];

export default function OnboardingScreen() {
  const [currentPage, setCurrentPage] = useState(0);
  const slideAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(1)).current;
  const slideTranslateX = useRef(new Animated.Value(0)).current;
  const titleAnimation = useRef(new Animated.Value(0)).current;
  const descriptionAnimation = useRef(new Animated.Value(0)).current;
  const progressAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animation initiale
    startSlideAnimations();
  }, [currentPage]);

  const startSlideAnimations = () => {
    // Reset animations
    titleAnimation.setValue(0);
    descriptionAnimation.setValue(0);

    // Séquence d'animations pour le contenu
    Animated.sequence([
      Animated.timing(titleAnimation, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(descriptionAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Animation de la barre de progression
    Animated.timing(progressAnimation, {
      toValue: (currentPage + 1) / slides.length,
      duration: 500,
      useNativeDriver: false,
    }).start();
  };

  const handleNext = () => {
    if (currentPage < slides.length - 1) {
      animateToNextSlide(currentPage + 1);
    } else {
      handleGetStarted();
    }
  };

  const animateToNextSlide = (nextPage: number) => {
    // Animation de slide vers la gauche
    Animated.parallel([
      Animated.timing(slideTranslateX, {
        toValue: -width,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setCurrentPage(nextPage);
      slideTranslateX.setValue(width);

      // Animation d'entrée
      Animated.parallel([
        Animated.timing(slideTranslateX, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    });
  };

  const handleSkip = async () => {
    await AsyncStorage.setItem('hasSeenOnboarding', 'true');
    router.replace('/(auth)/register');
  };

  const handleGetStarted = async () => {
    await AsyncStorage.setItem('hasSeenOnboarding', 'true');
    router.replace('/(auth)/register');
  };

  return (
    <View style={[styles.container, { backgroundColor: slides[currentPage].backgroundColor }]}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header avec barre de progression et bouton Skip */}
        <View style={styles.header}>
          <View style={styles.progressContainer}>
            <View style={styles.progressTrack}>
              <Animated.View
                style={[
                  styles.progressBar,
                  {
                    width: progressAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0%', '100%'],
                    }),
                  },
                ]}
              />
            </View>
            <Text style={styles.progressText}>
              {currentPage + 1} / {slides.length}
            </Text>
          </View>

          {currentPage < slides.length - 1 && (
            <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
              <Text style={styles.skipText}>Passer</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Contenu principal avec animations améliorées */}
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnimation,
              transform: [{ translateX: slideTranslateX }],
            }
          ]}
        >
          {/* Illustration */}
          <View style={styles.illustrationContainer}>
            {slides[currentPage].illustration}
          </View>

          {/* Texte avec animations séquentielles */}
          <View style={styles.textContainer}>
            <Animated.View
              style={{
                opacity: titleAnimation,
                transform: [
                  {
                    translateY: titleAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0],
                    }),
                  },
                ],
              }}
            >
              <Text style={styles.title}>{slides[currentPage].title}</Text>
            </Animated.View>

            <Animated.View
              style={{
                opacity: descriptionAnimation,
                transform: [
                  {
                    translateY: descriptionAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0],
                    }),
                  },
                ],
              }}
            >
              <Text style={styles.description}>{slides[currentPage].description}</Text>
            </Animated.View>
          </View>

          {/* Indicateurs de page améliorés */}
          <View style={styles.pagination}>
            {slides.map((_, index) => (
              <Animated.View
                key={index}
                style={[
                  styles.dot,
                  index === currentPage && styles.activeDot,
                  {
                    transform: [
                      {
                        scale: index === currentPage ? 1.2 : 1,
                      },
                    ],
                  },
                ]}
              />
            ))}
          </View>

          {/* Boutons d'action avec animations */}
          <Animated.View
            style={[
              styles.actions,
              {
                transform: [
                  {
                    translateY: fadeAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [30, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            {currentPage === slides.length - 1 ? (
              <TouchableOpacity
                style={[styles.primaryButton, styles.pulseButton]}
                onPress={handleGetStarted}
              >
                <Text style={styles.primaryButtonText}>🚀 Commencer</Text>
              </TouchableOpacity>
            ) : (
              <View style={styles.buttonRow}>
                <TouchableOpacity style={styles.secondaryButton} onPress={handleNext}>
                  <Text style={styles.secondaryButtonText}>Suivant →</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={handleSkip}>
                  <Text style={styles.skipBottomText}>Passer</Text>
                </TouchableOpacity>
              </View>
            )}
          </Animated.View>
        </Animated.View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 16,
    height: 80,
  },
  progressContainer: {
    flex: 1,
    marginRight: 16,
  },
  progressTrack: {
    height: 4,
    backgroundColor: Colors.neutral[200],
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.secondary[500],
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: Colors.neutral[600],
    fontWeight: '500',
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.neutral[100],
  },
  skipText: {
    fontSize: 14,
    color: Colors.neutral[600],
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  illustrationContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  textContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral[900],
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 32,
  },
  description: {
    fontSize: 16,
    color: Colors.neutral[600],
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: width * 0.8,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.neutral[300],
  },
  activeDot: {
    backgroundColor: Colors.secondary[500],
    width: 24,
  },
  actions: {
    paddingBottom: 32,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: Colors.secondary[500],
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
    shadowColor: Colors.secondary[500],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  pulseButton: {
    transform: [{ scale: 1.02 }],
  },
  primaryButtonText: {
    color: Colors.neutral[0],
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: Colors.secondary[500],
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  secondaryButtonText: {
    color: Colors.neutral[0],
    fontSize: 16,
    fontWeight: '600',
  },
  skipBottomText: {
    fontSize: 16,
    color: Colors.neutral[600],
    fontWeight: '500',
  },
});
