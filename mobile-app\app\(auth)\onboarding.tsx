// =====================================================
// ÉCRAN ONBOARDING - US-C001
// =====================================================

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import Button from '../../components/ui/Button';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width, height } = Dimensions.get('window');

interface OnboardingSlide {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  emoji: string;
  gradient: string[];
}

const slides: OnboardingSlide[] = [
  {
    id: '1',
    title: 'Livraison Rapide',
    subtitle: 'Partout en Afrique',
    description: 'Recevez vos produits préférés en moins de 45 minutes dans toute l\'Afrique',
    emoji: '🚚',
    gradient: ['#007AFF', '#34C759'],
  },
  {
    id: '2',
    title: 'Paiement Mobile',
    subtitle: 'Simple et Sécurisé',
    description: 'Payez facilement avec M-Pesa, Orange Money, MTN MoMo et plus encore',
    emoji: '💰',
    gradient: ['#34C759', '#FF9500'],
  },
  {
    id: '3',
    title: 'Communauté Locale',
    subtitle: 'Soutenez vos Commerçants',
    description: 'Découvrez et soutenez les commerçants locaux de votre quartier',
    emoji: '🏪',
    gradient: ['#FF9500', '#FF3B30'],
  },
];

export default function OnboardingScreen() {
  const [currentPage, setCurrentPage] = useState(0);
  const slideAnimation = useRef(new Animated.Value(0)).current;

  const handleNext = () => {
    if (currentPage < slides.length - 1) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);

      // Animation de transition
      Animated.timing(slideAnimation, {
        toValue: nextPage,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      handleGetStarted();
    }
  };

  const handleSkip = async () => {
    await AsyncStorage.setItem('hasSeenOnboarding', 'true');
    router.replace('/(auth)/register');
  };

  const handleGetStarted = async () => {
    await AsyncStorage.setItem('hasSeenOnboarding', 'true');
    router.replace('/(auth)/register');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header avec bouton Skip */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
          <Text style={styles.skipText}>Passer</Text>
        </TouchableOpacity>
      </View>

      {/* Contenu principal */}
      <View style={styles.content}>
        <View style={styles.slideContainer}>
          <LinearGradient
            colors={slides[currentPage].gradient}
            style={styles.slideGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Animated.View
              style={[
                styles.slideContent,
                {
                  opacity: slideAnimation.interpolate({
                    inputRange: [currentPage - 0.5, currentPage, currentPage + 0.5],
                    outputRange: [0.5, 1, 0.5],
                    extrapolate: 'clamp',
                  }),
                }
              ]}
            >
              {/* Emoji/Icon */}
              <View style={styles.iconContainer}>
                <Text style={styles.emoji}>{slides[currentPage].emoji}</Text>
              </View>

              {/* Texte */}
              <View style={styles.textContainer}>
                <Text style={styles.title}>{slides[currentPage].title}</Text>
                <Text style={styles.subtitle}>{slides[currentPage].subtitle}</Text>
                <Text style={styles.description}>{slides[currentPage].description}</Text>
              </View>
            </Animated.View>
          </LinearGradient>
        </View>

        {/* Indicateurs de page */}
        <View style={styles.pagination}>
          {slides.map((_, index) => (
            <View
              key={index}
              style={[
                styles.dot,
                index === currentPage && styles.activeDot,
              ]}
            />
          ))}
        </View>

        {/* Boutons d'action */}
        <View style={styles.actions}>
          <Button
            title={currentPage === slides.length - 1 ? 'Commencer' : 'Suivant'}
            onPress={handleNext}
            variant="primary"
            size="large"
            fullWidth
            icon={currentPage === slides.length - 1 ? 'rocket' : 'arrow-forward'}
            iconPosition="right"
          />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  skipText: {
    fontSize: 16,
    color: '#8E8E93',
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  slideContainer: {
    flex: 1,
    marginHorizontal: 24,
    marginVertical: 20,
    borderRadius: 24,
    overflow: 'hidden',
  },
  slideGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  slideContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  emoji: {
    fontSize: 60,
  },
  textContainer: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 20,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: 280,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 24,
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E5E5EA',
  },
  activeDot: {
    backgroundColor: '#007AFF',
    width: 24,
  },
  actions: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
});
