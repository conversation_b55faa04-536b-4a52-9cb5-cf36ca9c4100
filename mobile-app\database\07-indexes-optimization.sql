-- =====================================================
-- INDEX AVANCÉS ET OPTIMISATIONS
-- =====================================================

-- Index composites pour les requêtes fréquentes
CREATE INDEX CONCURRENTLY idx_orders_customer_status_created 
    ON orders(customer_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY idx_orders_merchant_status_created 
    ON orders(merchant_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY idx_orders_rider_status_created 
    ON orders(delivery_rider_id, status, created_at DESC) 
    WHERE delivery_rider_id IS NOT NULL;

-- Index pour les recherches géospatiales optimisées
CREATE INDEX CONCURRENTLY idx_delivery_zones_city_boundary 
    ON delivery_zones USING GIST(city_id, boundary) 
    WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_merchants_location_active 
    ON merchants USING GIST(location) 
    WHERE is_active = true AND is_accepting_orders = true;

CREATE INDEX CONCURRENTLY idx_delivery_riders_location_status 
    ON delivery_riders USING GIST(current_location) 
    WHERE current_status IN ('available', 'busy') AND is_active = true;

-- Index pour les recherches de produits
CREATE INDEX CONCURRENTLY idx_products_merchant_available_featured 
    ON products(merchant_id, is_available, is_featured, sort_order);

CREATE INDEX CONCURRENTLY idx_products_category_available_price 
    ON products(category_id, is_available, price);

-- Index pour les recherches textuelles avancées
CREATE INDEX CONCURRENTLY idx_products_search_vector 
    ON products USING GIN(
        to_tsvector('french', 
            COALESCE(name, '') || ' ' || 
            COALESCE(description, '') || ' ' ||
            COALESCE(name_translations->>'en', '') || ' ' ||
            COALESCE(description_translations->>'en', '')
        )
    );

CREATE INDEX CONCURRENTLY idx_merchants_search_vector 
    ON merchants USING GIN(
        to_tsvector('french', 
            COALESCE(business_name, '') || ' ' || 
            COALESCE(description, '') || ' ' ||
            COALESCE(business_type, '')
        )
    );

-- Index pour les notifications
CREATE INDEX CONCURRENTLY idx_notifications_user_status_scheduled 
    ON notifications(user_id, status, scheduled_for) 
    WHERE status IN ('pending', 'sent');

CREATE INDEX CONCURRENTLY idx_notifications_reference_type_id 
    ON notifications(reference_type, reference_id) 
    WHERE reference_type IS NOT NULL;

-- Index pour les paiements
CREATE INDEX CONCURRENTLY idx_payments_order_status_created 
    ON payments(order_id, status, created_at DESC);

CREATE INDEX CONCURRENTLY idx_payments_external_id_provider 
    ON payments(external_transaction_id, provider_name) 
    WHERE external_transaction_id IS NOT NULL;

-- Index pour les sessions utilisateur
CREATE INDEX CONCURRENTLY idx_user_sessions_user_active_expires 
    ON user_sessions(user_id, is_active, expires_at) 
    WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_user_sessions_device_active 
    ON user_sessions(device_id, is_active) 
    WHERE device_id IS NOT NULL AND is_active = true;

-- Index pour l'audit et les logs
CREATE INDEX CONCURRENTLY idx_audit_logs_resource_created 
    ON audit_logs(resource_type, resource_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_audit_logs_user_action_created 
    ON audit_logs(user_id, action, created_at DESC) 
    WHERE user_id IS NOT NULL;

-- Index pour les ratings et évaluations
CREATE INDEX CONCURRENTLY idx_ratings_rated_type_rating 
    ON ratings(rated_id, rated_type, rating, created_at DESC);

CREATE INDEX CONCURRENTLY idx_ratings_order_rater 
    ON ratings(order_id, rater_id);

-- Index pour les promotions
CREATE INDEX CONCURRENTLY idx_promotions_active_validity_code 
    ON promotions(is_active, valid_from, valid_until, code) 
    WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_promotion_uses_user_promotion 
    ON promotion_uses(user_id, promotion_id, created_at DESC);

-- Index pour les portefeuilles
CREATE INDEX CONCURRENTLY idx_wallet_transactions_wallet_created 
    ON wallet_transactions(wallet_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_wallet_transactions_reference 
    ON wallet_transactions(reference_type, reference_id) 
    WHERE reference_type IS NOT NULL;

-- Index pour le tracking de livraison
CREATE INDEX CONCURRENTLY idx_delivery_tracking_order_timestamp 
    ON delivery_tracking(order_id, timestamp DESC);

CREATE INDEX CONCURRENTLY idx_delivery_tracking_rider_timestamp 
    ON delivery_tracking(rider_id, timestamp DESC) 
    WHERE rider_id IS NOT NULL;

-- =====================================================
-- VUES MATÉRIALISÉES POUR PERFORMANCE
-- =====================================================

-- Vue matérialisée pour les statistiques des commerçants
CREATE MATERIALIZED VIEW merchant_stats AS
SELECT 
    m.id,
    m.business_name,
    COUNT(DISTINCT o.id) as total_orders,
    COUNT(DISTINCT CASE WHEN o.status = 'delivered' THEN o.id END) as completed_orders,
    COALESCE(AVG(CASE WHEN o.status = 'delivered' THEN o.total_amount END), 0) as avg_order_value,
    COALESCE(SUM(CASE WHEN o.status = 'delivered' THEN o.total_amount END), 0) as total_revenue,
    m.average_rating,
    m.total_ratings,
    COUNT(DISTINCT p.id) as total_products,
    COUNT(DISTINCT CASE WHEN p.is_available THEN p.id END) as available_products,
    EXTRACT(EPOCH FROM (NOW() - MAX(o.created_at)))/3600 as hours_since_last_order
FROM merchants m
LEFT JOIN orders o ON m.id = o.merchant_id
LEFT JOIN products p ON m.id = p.merchant_id
WHERE m.is_active = true
GROUP BY m.id, m.business_name, m.average_rating, m.total_ratings;

CREATE UNIQUE INDEX idx_merchant_stats_id ON merchant_stats(id);

-- Vue matérialisée pour les statistiques des livreurs
CREATE MATERIALIZED VIEW rider_stats AS
SELECT 
    dr.id,
    p.first_name,
    p.last_name,
    dr.total_deliveries,
    dr.successful_deliveries,
    CASE 
        WHEN dr.total_deliveries > 0 
        THEN ROUND((dr.successful_deliveries::DECIMAL / dr.total_deliveries * 100), 2)
        ELSE 0 
    END as success_rate,
    dr.average_rating,
    dr.total_ratings,
    dr.current_status,
    dr.earnings_balance,
    COUNT(DISTINCT CASE 
        WHEN o.status IN ('assigned', 'picked_up', 'in_transit') 
        THEN o.id 
    END) as active_deliveries,
    EXTRACT(EPOCH FROM (NOW() - dr.last_location_update))/60 as minutes_since_last_update
FROM delivery_riders dr
JOIN profiles p ON dr.id = p.id
LEFT JOIN orders o ON dr.id = o.delivery_rider_id
WHERE dr.is_active = true
GROUP BY dr.id, p.first_name, p.last_name, dr.total_deliveries, 
         dr.successful_deliveries, dr.average_rating, dr.total_ratings,
         dr.current_status, dr.earnings_balance, dr.last_location_update;

CREATE UNIQUE INDEX idx_rider_stats_id ON rider_stats(id);

-- Vue matérialisée pour les zones de livraison populaires
CREATE MATERIALIZED VIEW popular_delivery_zones AS
SELECT 
    dz.id,
    dz.name,
    c.name as city_name,
    COUNT(DISTINCT o.id) as total_orders,
    COUNT(DISTINCT o.customer_id) as unique_customers,
    COALESCE(AVG(o.total_amount), 0) as avg_order_value,
    COALESCE(AVG(EXTRACT(EPOCH FROM (o.delivered_at - o.created_at))/60), 0) as avg_delivery_time_minutes,
    COUNT(DISTINCT o.merchant_id) as active_merchants
FROM delivery_zones dz
JOIN cities c ON dz.city_id = c.id
LEFT JOIN orders o ON dz.id = o.delivery_zone_id 
    AND o.status = 'delivered' 
    AND o.created_at >= NOW() - INTERVAL '30 days'
WHERE dz.is_active = true
GROUP BY dz.id, dz.name, c.name;

CREATE UNIQUE INDEX idx_popular_zones_id ON popular_delivery_zones(id);

-- =====================================================
-- FONCTIONS D'OPTIMISATION
-- =====================================================

-- Fonction pour rafraîchir les vues matérialisées
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY merchant_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY rider_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY popular_delivery_zones;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour nettoyer les anciennes données
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Supprimer les logs d'audit de plus de 2 ans
    DELETE FROM audit_logs 
    WHERE created_at < NOW() - INTERVAL '2 years';
    
    -- Supprimer les sessions expirées de plus de 30 jours
    DELETE FROM user_sessions 
    WHERE expires_at < NOW() - INTERVAL '30 days';
    
    -- Supprimer les notifications lues de plus de 90 jours
    DELETE FROM notifications 
    WHERE status = 'read' AND read_at < NOW() - INTERVAL '90 days';
    
    -- Supprimer les logs de livraison de plus de 1 an
    DELETE FROM delivery_tracking 
    WHERE timestamp < NOW() - INTERVAL '1 year';
    
    -- Supprimer les logs de notification de plus de 6 mois
    DELETE FROM notification_delivery_logs 
    WHERE created_at < NOW() - INTERVAL '6 months';
    
    -- Supprimer les erreurs système résolues de plus de 3 mois
    DELETE FROM system_errors 
    WHERE is_resolved = true AND resolved_at < NOW() - INTERVAL '3 months';
    
    -- Supprimer les logs de paiement de plus de 2 ans
    DELETE FROM payment_logs 
    WHERE created_at < NOW() - INTERVAL '2 years';
END;
$$ LANGUAGE plpgsql;

-- Fonction pour optimiser les performances
CREATE OR REPLACE FUNCTION optimize_database()
RETURNS void AS $$
BEGIN
    -- Analyser les statistiques des tables principales
    ANALYZE orders;
    ANALYZE order_items;
    ANALYZE products;
    ANALYZE merchants;
    ANALYZE delivery_riders;
    ANALYZE payments;
    ANALYZE notifications;
    ANALYZE delivery_tracking;
    
    -- Réindexer les index les plus utilisés si nécessaire
    REINDEX INDEX CONCURRENTLY idx_orders_customer_status_created;
    REINDEX INDEX CONCURRENTLY idx_orders_merchant_status_created;
    REINDEX INDEX CONCURRENTLY idx_products_search_vector;
    REINDEX INDEX CONCURRENTLY idx_merchants_location_active;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TÂCHES DE MAINTENANCE AUTOMATIQUE
-- =====================================================

-- Créer une extension pour les tâches cron si disponible
-- CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Programmer le rafraîchissement des vues matérialisées toutes les heures
-- SELECT cron.schedule('refresh-stats', '0 * * * *', 'SELECT refresh_materialized_views();');

-- Programmer le nettoyage des anciennes données chaque dimanche à 2h
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * 0', 'SELECT cleanup_old_data();');

-- Programmer l'optimisation de la base chaque jour à 3h
-- SELECT cron.schedule('optimize-database', '0 3 * * *', 'SELECT optimize_database();');

-- =====================================================
-- MONITORING ET ALERTES
-- =====================================================

-- Vue pour surveiller les performances des requêtes
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 100 -- Requêtes prenant plus de 100ms en moyenne
ORDER BY mean_time DESC;

-- Vue pour surveiller l'utilisation des index
CREATE OR REPLACE VIEW unused_indexes AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes 
WHERE idx_tup_read = 0 
    AND idx_tup_fetch = 0
    AND schemaname = 'public'
ORDER BY pg_relation_size(indexrelid) DESC;

-- Vue pour surveiller la taille des tables
CREATE OR REPLACE VIEW table_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
