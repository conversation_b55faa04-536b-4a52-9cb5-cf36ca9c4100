{"version": 3, "file": "getNotificationCategoriesAsync.js", "sourceRoot": "", "sources": ["../src/getNotificationCategoriesAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,4BAA4B,MAAM,gCAAgC,CAAC;AAG1E;;;;;;;GAOG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,8BAA8B;IAC1D,IAAI,CAAC,4BAA4B,CAAC,8BAA8B,EAAE,CAAC;QACjE,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,gCAAgC,CAAC,CAAC;IACnF,CAAC;IAED,OAAO,MAAM,4BAA4B,CAAC,8BAA8B,EAAE,CAAC;AAC7E,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationCategoriesModule from './NotificationCategoriesModule';\nimport { NotificationCategory } from './Notifications.types';\n\n/**\n * Fetches information about all known notification categories.\n * @return A Promise which resolves to an array of `NotificationCategory`s. On platforms that do not support notification channels,\n * it will always resolve to an empty array.\n * @platform android\n * @platform ios\n * @header categories\n */\nexport default async function getNotificationCategoriesAsync(): Promise<NotificationCategory[]> {\n  if (!NotificationCategoriesModule.getNotificationCategoriesAsync) {\n    throw new UnavailabilityError('Notifications', 'getNotificationCategoriesAsync');\n  }\n\n  return await NotificationCategoriesModule.getNotificationCategoriesAsync();\n}\n"]}