-- =====================================================
-- DONNÉES D'INITIALISATION
-- =====================================================

-- Insertion des pays africains principaux
INSERT INTO countries (code, name_en, name_fr, name_local, currency_code, currency_symbol, phone_prefix, timezone, default_language, supported_languages) VALUES
-- Afrique de l'Ouest
('SEN', 'Senegal', 'Sénégal', 'Senegaal', 'XOF', 'CFA', '+221', 'Africa/Dakar', 'fr', ARRAY['fr', 'wo']),
('CIV', 'Ivory Coast', 'Côte d''Ivoire', 'Côte d''Ivoire', 'XOF', 'CFA', '+225', 'Africa/Abidjan', 'fr', ARRAY['fr']),
('GHA', 'Ghana', 'Ghana', 'Ghana', 'GHS', '₵', '+233', 'Africa/Accra', 'en', ARRAY['en']),
('NGA', 'Nigeria', 'Nigeria', 'Nigeria', 'NGN', '₦', '+234', 'Africa/Lagos', 'en', ARRAY['en', 'ha', 'yo', 'ig']),
('MLI', 'Mali', 'Mali', 'Mali', 'XOF', 'CFA', '+223', 'Africa/Bamako', 'fr', ARRAY['fr']),
('BFA', 'Burkina Faso', 'Burkina Faso', 'Burkina Faso', 'XOF', 'CFA', '+226', 'Africa/Ouagadougou', 'fr', ARRAY['fr']),

-- Afrique de l'Est
('KEN', 'Kenya', 'Kenya', 'Kenya', 'KES', 'KSh', '+254', 'Africa/Nairobi', 'en', ARRAY['en', 'sw']),
('TZA', 'Tanzania', 'Tanzanie', 'Tanzania', 'TZS', 'TSh', '+255', 'Africa/Dar_es_Salaam', 'sw', ARRAY['sw', 'en']),
('UGA', 'Uganda', 'Ouganda', 'Uganda', 'UGX', 'USh', '+256', 'Africa/Kampala', 'en', ARRAY['en']),
('RWA', 'Rwanda', 'Rwanda', 'Rwanda', 'RWF', 'RF', '+250', 'Africa/Kigali', 'en', ARRAY['en', 'fr']),
('ETH', 'Ethiopia', 'Éthiopie', 'ኢትዮጵያ', 'ETB', 'Br', '+251', 'Africa/Addis_Ababa', 'am', ARRAY['am', 'en']),

-- Afrique du Nord
('MAR', 'Morocco', 'Maroc', 'المغرب', 'MAD', 'DH', '+212', 'Africa/Casablanca', 'ar', ARRAY['ar', 'fr']),
('TUN', 'Tunisia', 'Tunisie', 'تونس', 'TND', 'DT', '+216', 'Africa/Tunis', 'ar', ARRAY['ar', 'fr']),
('EGY', 'Egypt', 'Égypte', 'مصر', 'EGP', '£', '+20', 'Africa/Cairo', 'ar', ARRAY['ar', 'en']),

-- Afrique Centrale
('CMR', 'Cameroon', 'Cameroun', 'Cameroun', 'XAF', 'FCFA', '+237', 'Africa/Douala', 'fr', ARRAY['fr', 'en']),
('GAB', 'Gabon', 'Gabon', 'Gabon', 'XAF', 'FCFA', '+241', 'Africa/Libreville', 'fr', ARRAY['fr']),

-- Afrique Australe
('ZAF', 'South Africa', 'Afrique du Sud', 'South Africa', 'ZAR', 'R', '+27', 'Africa/Johannesburg', 'en', ARRAY['en']),
('ZWE', 'Zimbabwe', 'Zimbabwe', 'Zimbabwe', 'ZWL', 'Z$', '+263', 'Africa/Harare', 'en', ARRAY['en']);

-- Insertion des villes principales
INSERT INTO cities (country_id, name, name_local, latitude, longitude, timezone, service_radius_km) VALUES
-- Sénégal
((SELECT id FROM countries WHERE code = 'SEN'), 'Dakar', 'Dakar', 14.6928, -17.4467, 'Africa/Dakar', 30),
((SELECT id FROM countries WHERE code = 'SEN'), 'Thiès', 'Thiès', 14.7886, -16.9246, 'Africa/Dakar', 20),
((SELECT id FROM countries WHERE code = 'SEN'), 'Saint-Louis', 'Saint-Louis', 16.0199, -16.4896, 'Africa/Dakar', 15),

-- Ghana
((SELECT id FROM countries WHERE code = 'GHA'), 'Accra', 'Accra', 5.6037, -0.1870, 'Africa/Accra', 40),
((SELECT id FROM countries WHERE code = 'GHA'), 'Kumasi', 'Kumasi', 6.6885, -1.6244, 'Africa/Accra', 25),
((SELECT id FROM countries WHERE code = 'GHA'), 'Tamale', 'Tamale', 9.4008, -0.8393, 'Africa/Accra', 20),

-- Nigeria
((SELECT id FROM countries WHERE code = 'NGA'), 'Lagos', 'Lagos', 6.5244, 3.3792, 'Africa/Lagos', 50),
((SELECT id FROM countries WHERE code = 'NGA'), 'Abuja', 'Abuja', 9.0765, 7.3986, 'Africa/Lagos', 35),
((SELECT id FROM countries WHERE code = 'NGA'), 'Kano', 'Kano', 12.0022, 8.5920, 'Africa/Lagos', 30),
((SELECT id FROM countries WHERE code = 'NGA'), 'Ibadan', 'Ibadan', 7.3775, 3.9470, 'Africa/Lagos', 30),

-- Kenya
((SELECT id FROM countries WHERE code = 'KEN'), 'Nairobi', 'Nairobi', -1.2921, 36.8219, 'Africa/Nairobi', 40),
((SELECT id FROM countries WHERE code = 'KEN'), 'Mombasa', 'Mombasa', -4.0435, 39.6682, 'Africa/Nairobi', 25),
((SELECT id FROM countries WHERE code = 'KEN'), 'Kisumu', 'Kisumu', -0.0917, 34.7680, 'Africa/Nairobi', 20),

-- Tanzanie
((SELECT id FROM countries WHERE code = 'TZA'), 'Dar es Salaam', 'Dar es Salaam', -6.7924, 39.2083, 'Africa/Dar_es_Salaam', 45),
((SELECT id FROM countries WHERE code = 'TZA'), 'Dodoma', 'Dodoma', -6.1630, 35.7516, 'Africa/Dar_es_Salaam', 20),
((SELECT id FROM countries WHERE code = 'TZA'), 'Arusha', 'Arusha', -3.3869, 36.6830, 'Africa/Dar_es_Salaam', 25),

-- Rwanda
((SELECT id FROM countries WHERE code = 'RWA'), 'Kigali', 'Kigali', -1.9441, 30.0619, 'Africa/Kigali', 25),

-- Côte d'Ivoire
((SELECT id FROM countries WHERE code = 'CIV'), 'Abidjan', 'Abidjan', 5.3600, -4.0083, 'Africa/Abidjan', 35),
((SELECT id FROM countries WHERE code = 'CIV'), 'Yamoussoukro', 'Yamoussoukro', 6.8276, -5.2893, 'Africa/Abidjan', 20);

-- Insertion des zones de livraison pour Dakar
INSERT INTO delivery_zones (city_id, name, description, boundary, center_point, base_delivery_fee, estimated_delivery_time) VALUES
((SELECT id FROM cities WHERE name = 'Dakar'), 
 'Plateau', 
 'Centre-ville de Dakar',
 ST_GeomFromText('POLYGON((-17.4500 14.6850, -17.4400 14.6850, -17.4400 14.6950, -17.4500 14.6950, -17.4500 14.6850))', 4326),
 ST_Point(-17.4450, 14.6900, 4326),
 500, 20),

((SELECT id FROM cities WHERE name = 'Dakar'), 
 'Almadies', 
 'Zone résidentielle Almadies',
 ST_GeomFromText('POLYGON((-17.5200 14.7200, -17.4800 14.7200, -17.4800 14.7500, -17.5200 14.7500, -17.5200 14.7200))', 4326),
 ST_Point(-17.5000, 14.7350, 4326),
 800, 35),

((SELECT id FROM cities WHERE name = 'Dakar'), 
 'Pikine', 
 'Banlieue de Pikine',
 ST_GeomFromText('POLYGON((-17.4000 14.7500, -17.3500 14.7500, -17.3500 14.8000, -17.4000 14.8000, -17.4000 14.7500))', 4326),
 ST_Point(-17.3750, 14.7750, 4326),
 600, 40);

-- Insertion des zones de livraison pour Nairobi
INSERT INTO delivery_zones (city_id, name, description, boundary, center_point, base_delivery_fee, estimated_delivery_time) VALUES
((SELECT id FROM cities WHERE name = 'Nairobi'), 
 'CBD', 
 'Central Business District',
 ST_GeomFromText('POLYGON((36.8100 -1.3000, 36.8300 -1.3000, 36.8300 -1.2800, 36.8100 -1.2800, 36.8100 -1.3000))', 4326),
 ST_Point(36.8200, -1.2900, 4326),
 150, 25),

((SELECT id FROM cities WHERE name = 'Nairobi'), 
 'Westlands', 
 'Westlands Area',
 ST_GeomFromText('POLYGON((36.7900 -1.2700, 36.8100 -1.2700, 36.8100 -1.2500, 36.7900 -1.2500, 36.7900 -1.2700))', 4326),
 ST_Point(36.8000, -1.2600, 4326),
 200, 30),

((SELECT id FROM cities WHERE name = 'Nairobi'), 
 'Kibera', 
 'Kibera Area',
 ST_GeomFromText('POLYGON((36.7800 -1.3200, 36.8000 -1.3200, 36.8000 -1.3000, 36.7800 -1.3000, 36.7800 -1.3200))', 4326),
 ST_Point(36.7900, -1.3100, 4326),
 100, 35);

-- Configuration des paiements par pays
INSERT INTO payment_configurations (country_id, payment_method, provider_name, is_enabled, is_default, min_amount, max_amount, fixed_fee, percentage_fee, api_config, sort_order) VALUES
-- Sénégal
((SELECT id FROM countries WHERE code = 'SEN'), 'mobile_money', 'orange_money', true, true, 100, 500000, 0, 0.015, '{"api_url": "https://api.orange.com/", "merchant_id": "SN_MERCHANT"}', 1),
((SELECT id FROM countries WHERE code = 'SEN'), 'mobile_money', 'wave', true, false, 100, 1000000, 0, 0.01, '{"api_url": "https://api.wave.com/", "merchant_id": "WAVE_SN"}', 2),
((SELECT id FROM countries WHERE code = 'SEN'), 'cash', 'cash_on_delivery', true, false, 100, 100000, 0, 0, '{}', 3),

-- Kenya
((SELECT id FROM countries WHERE code = 'KEN'), 'mobile_money', 'mpesa', true, true, 10, 7000000, 0, 0.01, '{"api_url": "https://sandbox.safaricom.co.ke/", "shortcode": "174379"}', 1),
((SELECT id FROM countries WHERE code = 'KEN'), 'mobile_money', 'airtel_money', true, false, 10, 5000000, 0, 0.015, '{"api_url": "https://openapi.airtel.africa/", "merchant_id": "KE_AIRTEL"}', 2),
((SELECT id FROM countries WHERE code = 'KEN'), 'cash', 'cash_on_delivery', true, false, 10, 50000, 0, 0, '{}', 3),

-- Ghana
((SELECT id FROM countries WHERE code = 'GHA'), 'mobile_money', 'mtn_momo', true, true, 1, 500000, 0, 0.02, '{"api_url": "https://sandbox.momodeveloper.mtn.com/", "subscription_key": "GH_MTN_KEY"}', 1),
((SELECT id FROM countries WHERE code = 'GHA'), 'mobile_money', 'airtel_money', true, false, 1, 300000, 0, 0.025, '{"api_url": "https://openapi.airtel.africa/", "merchant_id": "GH_AIRTEL"}', 2),
((SELECT id FROM countries WHERE code = 'GHA'), 'cash', 'cash_on_delivery', true, false, 1, 10000, 0, 0, '{}', 3),

-- Nigeria
((SELECT id FROM countries WHERE code = 'NGA'), 'card', 'paystack', true, true, 100, ********, 0, 0.015, '{"api_url": "https://api.paystack.co/", "public_key": "pk_test_xxx"}', 1),
((SELECT id FROM countries WHERE code = 'NGA'), 'bank_transfer', 'paystack_transfer', true, false, 100, ********, 0, 0.01, '{"api_url": "https://api.paystack.co/", "account_number": "**********"}', 2),
((SELECT id FROM countries WHERE code = 'NGA'), 'cash', 'cash_on_delivery', true, false, 100, 500000, 0, 0, '{}', 3);

-- Templates de notifications
INSERT INTO notification_templates (template_key, notification_type, target_role, title_translations, body_translations, channels, priority) VALUES
('order_confirmed_customer', 'order_confirmed', 'customer', 
 '{"fr": "Commande confirmée", "en": "Order confirmed", "wo": "Commande bi nekk na"}',
 '{"fr": "Votre commande #{order_number} a été confirmée et sera préparée sous peu.", "en": "Your order #{order_number} has been confirmed and will be prepared shortly.", "wo": "Sa commande #{order_number} nekk na te dina def."}',
 ARRAY['push', 'sms'], 5),

('order_preparing_customer', 'order_preparing', 'customer',
 '{"fr": "Commande en préparation", "en": "Order being prepared", "sw": "Agizo linatengenezwa"}',
 '{"fr": "Votre commande #{order_number} est en cours de préparation.", "en": "Your order #{order_number} is being prepared.", "sw": "Agizo lako #{order_number} linatengenezwa."}',
 ARRAY['push'], 3),

('rider_assigned_customer', 'rider_assigned', 'customer',
 '{"fr": "Livreur assigné", "en": "Rider assigned", "ha": "An ba da mai kawo kaya"}',
 '{"fr": "{rider_name} va livrer votre commande #{order_number}.", "en": "{rider_name} will deliver your order #{order_number}.", "ha": "{rider_name} zai kawo muku odar #{order_number}."}',
 ARRAY['push'], 4),

('order_delivered_customer', 'order_delivered', 'customer',
 '{"fr": "Commande livrée", "en": "Order delivered", "yo": "Ohun ti o bere ti de"}',
 '{"fr": "Votre commande #{order_number} a été livrée avec succès!", "en": "Your order #{order_number} has been delivered successfully!", "yo": "Ohun ti o bere #{order_number} ti de daradara!"}',
 ARRAY['push', 'sms'], 5),

('new_order_merchant', 'order_confirmed', 'merchant',
 '{"fr": "Nouvelle commande", "en": "New order", "sw": "Agizo jipya"}',
 '{"fr": "Nouvelle commande #{order_number} reçue pour {total_amount}.", "en": "New order #{order_number} received for {total_amount}.", "sw": "Agizo jipya #{order_number} limepokelewa kwa {total_amount}."}',
 ARRAY['push', 'sms'], 8),

('order_assigned_rider', 'rider_assigned', 'delivery_rider',
 '{"fr": "Nouvelle livraison", "en": "New delivery", "sw": "Uwasilishaji mpya"}',
 '{"fr": "Nouvelle livraison assignée: #{order_number} - {pickup_address} vers {delivery_address}.", "en": "New delivery assigned: #{order_number} - {pickup_address} to {delivery_address}.", "sw": "Uwasilishaji mpya umepangiwa: #{order_number} - {pickup_address} hadi {delivery_address}."}',
 ARRAY['push', 'sms'], 9);

-- Données de test pour les profils utilisateurs
-- Note: Ces données seront créées via l'application lors de l'inscription
-- Ici on ne fait que préparer la structure

-- Promotions de lancement
INSERT INTO promotions (name, description, code, discount_type, discount_value, min_order_amount, max_uses_total, max_uses_per_user, valid_from, valid_until, applicable_delivery_types, first_order_only, is_active) VALUES
('Bienvenue Afrique', 'Première livraison gratuite pour tous les nouveaux utilisateurs', 'WELCOME2024', 'free_delivery', 100, 1000, 10000, 1, NOW(), NOW() + INTERVAL '6 months', ARRAY['food', 'grocery'], true, true),
('Réduction Étudiant', '20% de réduction pour les étudiants', 'STUDENT20', 'percentage', 20, 500, 5000, 5, NOW(), NOW() + INTERVAL '1 year', ARRAY['food'], false, true),
('Livraison Express', 'Livraison gratuite pour commandes > 5000 FCFA', NULL, 'free_delivery', 100, 5000, NULL, NULL, NOW(), NOW() + INTERVAL '3 months', ARRAY['food', 'grocery'], false, true);

-- Insertion des données de base terminée
-- Les autres données (utilisateurs, commandes, etc.) seront créées dynamiquement par l'application
