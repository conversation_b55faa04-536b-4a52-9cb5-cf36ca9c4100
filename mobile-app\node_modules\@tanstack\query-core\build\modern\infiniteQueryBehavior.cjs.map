{"version": 3, "sources": ["../../src/infiniteQueryBehavior.ts"], "sourcesContent": ["import { addToEnd, addToStart, ensureQueryFn } from './utils'\nimport type { QueryBehavior } from './query'\nimport type {\n  InfiniteData,\n  InfiniteQueryPageParamsOptions,\n  OmitKeyof,\n  QueryFunctionContext,\n  QueryKey,\n} from './types'\n\nexport function infiniteQueryBehavior<TQueryFnData, TError, TData, TPageParam>(\n  pages?: number,\n): QueryBehavior<TQueryFnData, TError, InfiniteData<TData, TPageParam>> {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options as InfiniteQueryPageParamsOptions<TData>\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction\n      const oldPages = context.state.data?.pages || []\n      const oldPageParams = context.state.data?.pageParams || []\n      let result: InfiniteData<unknown> = { pages: [], pageParams: [] }\n      let currentPage = 0\n\n      const fetchFn = async () => {\n        let cancelled = false\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true\n              } else {\n                context.signal.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions)\n\n        // Create function to fetch a page\n        const fetchPage = async (\n          data: InfiniteData<unknown>,\n          param: unknown,\n          previous?: boolean,\n        ): Promise<InfiniteData<unknown>> => {\n          if (cancelled) {\n            return Promise.reject()\n          }\n\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data)\n          }\n\n          const queryFnContext: OmitKeyof<\n            QueryFunctionContext<QueryKey, unknown>,\n            'signal'\n          > = {\n            client: context.client,\n            queryKey: context.queryKey,\n            pageParam: param,\n            direction: previous ? 'backward' : 'forward',\n            meta: context.options.meta,\n          }\n\n          addSignalProperty(queryFnContext)\n\n          const page = await queryFn(\n            queryFnContext as QueryFunctionContext<QueryKey, unknown>,\n          )\n\n          const { maxPages } = context.options\n          const addTo = previous ? addToStart : addToEnd\n\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages),\n          }\n        }\n\n        // fetch next / previous page?\n        if (direction && oldPages.length) {\n          const previous = direction === 'backward'\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams,\n          }\n          const param = pageParamFn(options, oldData)\n\n          result = await fetchPage(oldData, param, previous)\n        } else {\n          const remainingPages = pages ?? oldPages.length\n\n          // Fetch all pages\n          do {\n            const param =\n              currentPage === 0\n                ? (oldPageParams[0] ?? options.initialPageParam)\n                : getNextPageParam(options, result)\n            if (currentPage > 0 && param == null) {\n              break\n            }\n            result = await fetchPage(result, param)\n            currentPage++\n          } while (currentPage < remainingPages)\n        }\n\n        return result\n      }\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn as any,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal,\n            },\n            query,\n          )\n        }\n      } else {\n        context.fetchFn = fetchFn\n      }\n    },\n  }\n}\n\nfunction getNextPageParam(\n  options: InfiniteQueryPageParamsOptions<any>,\n  { pages, pageParams }: InfiniteData<unknown>,\n): unknown | undefined {\n  const lastIndex = pages.length - 1\n  return pages.length > 0\n    ? options.getNextPageParam(\n        pages[lastIndex],\n        pages,\n        pageParams[lastIndex],\n        pageParams,\n      )\n    : undefined\n}\n\nfunction getPreviousPageParam(\n  options: InfiniteQueryPageParamsOptions<any>,\n  { pages, pageParams }: InfiniteData<unknown>,\n): unknown | undefined {\n  return pages.length > 0\n    ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams)\n    : undefined\n}\n\n/**\n * Checks if there is a next page.\n */\nexport function hasNextPage(\n  options: InfiniteQueryPageParamsOptions<any, any>,\n  data?: InfiniteData<unknown>,\n): boolean {\n  if (!data) return false\n  return getNextPageParam(options, data) != null\n}\n\n/**\n * Checks if there is a previous page.\n */\nexport function hasPreviousPage(\n  options: InfiniteQueryPageParamsOptions<any, any>,\n  data?: InfiniteData<unknown>,\n): boolean {\n  if (!data || !options.getPreviousPageParam) return false\n  return getPreviousPageParam(options, data) != null\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAoD;AAU7C,SAAS,sBACd,OACsE;AACtE,SAAO;AAAA,IACL,SAAS,CAAC,SAAS,UAAU;AAC3B,YAAM,UAAU,QAAQ;AACxB,YAAM,YAAY,QAAQ,cAAc,MAAM,WAAW;AACzD,YAAM,WAAW,QAAQ,MAAM,MAAM,SAAS,CAAC;AAC/C,YAAM,gBAAgB,QAAQ,MAAM,MAAM,cAAc,CAAC;AACzD,UAAI,SAAgC,EAAE,OAAO,CAAC,GAAG,YAAY,CAAC,EAAE;AAChE,UAAI,cAAc;AAElB,YAAM,UAAU,YAAY;AAC1B,YAAI,YAAY;AAChB,cAAM,oBAAoB,CAAC,WAAoB;AAC7C,iBAAO,eAAe,QAAQ,UAAU;AAAA,YACtC,YAAY;AAAA,YACZ,KAAK,MAAM;AACT,kBAAI,QAAQ,OAAO,SAAS;AAC1B,4BAAY;AAAA,cACd,OAAO;AACL,wBAAQ,OAAO,iBAAiB,SAAS,MAAM;AAC7C,8BAAY;AAAA,gBACd,CAAC;AAAA,cACH;AACA,qBAAO,QAAQ;AAAA,YACjB;AAAA,UACF,CAAC;AAAA,QACH;AAEA,cAAM,cAAU,4BAAc,QAAQ,SAAS,QAAQ,YAAY;AAGnE,cAAM,YAAY,OAChB,MACA,OACA,aACmC;AACnC,cAAI,WAAW;AACb,mBAAO,QAAQ,OAAO;AAAA,UACxB;AAEA,cAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AACtC,mBAAO,QAAQ,QAAQ,IAAI;AAAA,UAC7B;AAEA,gBAAM,iBAGF;AAAA,YACF,QAAQ,QAAQ;AAAA,YAChB,UAAU,QAAQ;AAAA,YAClB,WAAW;AAAA,YACX,WAAW,WAAW,aAAa;AAAA,YACnC,MAAM,QAAQ,QAAQ;AAAA,UACxB;AAEA,4BAAkB,cAAc;AAEhC,gBAAM,OAAO,MAAM;AAAA,YACjB;AAAA,UACF;AAEA,gBAAM,EAAE,SAAS,IAAI,QAAQ;AAC7B,gBAAM,QAAQ,WAAW,0BAAa;AAEtC,iBAAO;AAAA,YACL,OAAO,MAAM,KAAK,OAAO,MAAM,QAAQ;AAAA,YACvC,YAAY,MAAM,KAAK,YAAY,OAAO,QAAQ;AAAA,UACpD;AAAA,QACF;AAGA,YAAI,aAAa,SAAS,QAAQ;AAChC,gBAAM,WAAW,cAAc;AAC/B,gBAAM,cAAc,WAAW,uBAAuB;AACtD,gBAAM,UAAU;AAAA,YACd,OAAO;AAAA,YACP,YAAY;AAAA,UACd;AACA,gBAAM,QAAQ,YAAY,SAAS,OAAO;AAE1C,mBAAS,MAAM,UAAU,SAAS,OAAO,QAAQ;AAAA,QACnD,OAAO;AACL,gBAAM,iBAAiB,SAAS,SAAS;AAGzC,aAAG;AACD,kBAAM,QACJ,gBAAgB,IACX,cAAc,CAAC,KAAK,QAAQ,mBAC7B,iBAAiB,SAAS,MAAM;AACtC,gBAAI,cAAc,KAAK,SAAS,MAAM;AACpC;AAAA,YACF;AACA,qBAAS,MAAM,UAAU,QAAQ,KAAK;AACtC;AAAA,UACF,SAAS,cAAc;AAAA,QACzB;AAEA,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,QAAQ,WAAW;AAC7B,gBAAQ,UAAU,MAAM;AACtB,iBAAO,QAAQ,QAAQ;AAAA,YACrB;AAAA,YACA;AAAA,cACE,QAAQ,QAAQ;AAAA,cAChB,UAAU,QAAQ;AAAA,cAClB,MAAM,QAAQ,QAAQ;AAAA,cACtB,QAAQ,QAAQ;AAAA,YAClB;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF,OAAO;AACL,gBAAQ,UAAU;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,iBACP,SACA,EAAE,OAAO,WAAW,GACC;AACrB,QAAM,YAAY,MAAM,SAAS;AACjC,SAAO,MAAM,SAAS,IAClB,QAAQ;AAAA,IACN,MAAM,SAAS;AAAA,IACf;AAAA,IACA,WAAW,SAAS;AAAA,IACpB;AAAA,EACF,IACA;AACN;AAEA,SAAS,qBACP,SACA,EAAE,OAAO,WAAW,GACC;AACrB,SAAO,MAAM,SAAS,IAClB,QAAQ,uBAAuB,MAAM,CAAC,GAAG,OAAO,WAAW,CAAC,GAAG,UAAU,IACzE;AACN;AAKO,SAAS,YACd,SACA,MACS;AACT,MAAI,CAAC,KAAM,QAAO;AAClB,SAAO,iBAAiB,SAAS,IAAI,KAAK;AAC5C;AAKO,SAAS,gBACd,SACA,MACS;AACT,MAAI,CAAC,QAAQ,CAAC,QAAQ,qBAAsB,QAAO;AACnD,SAAO,qBAAqB,SAAS,IAAI,KAAK;AAChD;", "names": []}