{"version": 3, "file": "DevicePushTokenAutoRegistration.fx.js", "sourceRoot": "", "sources": ["../src/DevicePushTokenAutoRegistration.fx.ts"], "names": [], "mappings": "AAAA,OAAO,2BAA2B,CAAC;AACnC,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,wBAAwB,MAAM,4BAA4B,CAAC;AAClE,OAAO,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AAEtD,OAAO,uBAAuB,MAAM,2BAA2B,CAAC;AAChE,OAAO,EAAE,0BAA0B,IAAI,oCAAoC,EAAE,MAAM,oCAAoC,CAAC;AAExH,IAAI,mBAAmB,GAA2B,IAAI,CAAC;AACvD,KAAK,UAAU,oBAAoB,CAAC,KAAsB;IACxD,+BAA+B;IAC/B,mBAAmB,EAAE,KAAK,EAAE,CAAC;IAC7B,mBAAmB,GAAG,IAAI,eAAe,EAAE,CAAC;IAC5C,OAAO,MAAM,oCAAoC,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACvF,CAAC;AASD;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,qCAAqC,CAAC,OAAgB;IAC1E,uDAAuD;IACvD,gCAAgC;IAChC,mBAAmB,EAAE,KAAK,EAAE,CAAC;IAE7B,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,EAAE,CAAC;QACvD,MAAM,IAAI,mBAAmB,CAAC,0BAA0B,EAAE,0BAA0B,CAAC,CAAC;IACxF,CAAC;IAED,MAAM,wBAAwB,CAAC,wBAAwB,CACrD,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CACxD,CAAC;AACJ,CAAC;AAED,sEAAsE;AACtE,MAAM,CAAC,KAAK,UAAU,sCAAsC,CAC1D,gBAA2C;IAE3C,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,sCAAsC;QACtC,OAAO;IACT,CAAC;IAED,IAAI,YAAY,GAAuC,IAAI,CAAC;IAC5D,IAAI,CAAC;QACH,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC9C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CACV,wGAAwG,EACxG,CAAC,CACF,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,CAAC;QAC7B,6DAA6D;QAC7D,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,mEAAmE;QACnE,0BAA0B;QAC1B,MAAM,qBAAqB,GAAG,MAAM,uBAAuB,EAAE,CAAC;QAC9D,MAAM,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;IACpD,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CACV,0GAA0G,EAC1G,CAAC,CACF,CAAC;IACJ,CAAC;AACH,CAAC;AAED,IAAI,wBAAwB,CAAC,wBAAwB,EAAE,CAAC;IACtD,4DAA4D;IAC5D,+BAA+B;IAC/B,oBAAoB,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QACnC,IAAI,CAAC;YACH,wEAAwE;YACxE,yEAAyE;YACzE,2BAA2B;YAC3B,MAAM,gBAAgB,GAAG,MAAM,wBAAwB,CAAC,wBAAyB,EAAE,CAAC;YAEpF,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,8BAA8B;gBAC9B,OAAO;YACT,CAAC;YAED,MAAM,YAAY,GAAuC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACtF,IAAI,YAAY,EAAE,SAAS,EAAE,CAAC;gBAC5B,uCAAuC;gBACvC,+BAA+B;gBAC/B,MAAM,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,IAAI,CACV,0GAA0G,EAC1G,CAAC,CACF,CAAC;QACJ,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,uCAAuC;IACvC,oCAAoC;IACpC,wBAAwB,CAAC,wBAAwB,EAAE,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;AACnG,CAAC;KAAM,CAAC;IACN,OAAO,CAAC,IAAI,CACV,2IAA2I,EAC3I,IAAI,mBAAmB,CAAC,0BAA0B,EAAE,0BAA0B,CAAC,CAChF,CAAC;AACJ,CAAC", "sourcesContent": ["import 'abort-controller/polyfill';\nimport { UnavailabilityError } from 'expo-modules-core';\n\nimport ServerRegistrationModule from './ServerRegistrationModule';\nimport { addPushTokenListener } from './TokenEmitter';\nimport { DevicePushToken } from './Tokens.types';\nimport getDevicePushTokenAsync from './getDevicePushTokenAsync';\nimport { updateDevicePushTokenAsync as updateDevicePushTokenAsyncWithSignal } from './utils/updateDevicePushTokenAsync';\n\nlet lastAbortController: AbortController | null = null;\nasync function updatePushTokenAsync(token: DevicePushToken) {\n  // Abort current update process\n  lastAbortController?.abort();\n  lastAbortController = new AbortController();\n  return await updateDevicePushTokenAsyncWithSignal(lastAbortController.signal, token);\n}\n\n/**\n * Encapsulates device server registration data\n */\nexport type DevicePushTokenRegistration = {\n  isEnabled: boolean;\n};\n\n/**\n * @hidden - the comment is misleading and the purpose of the function needs to be reevaluated\n *\n * Sets the registration information so that the device push token gets pushed\n * to the given registration endpoint\n * @param enabled\n */\nexport async function setAutoServerRegistrationEnabledAsync(enabled: boolean) {\n  // We are overwriting registration, so we shouldn't let\n  // any pending request complete.\n  lastAbortController?.abort();\n\n  if (!ServerRegistrationModule.setRegistrationInfoAsync) {\n    throw new UnavailabilityError('ServerRegistrationModule', 'setRegistrationInfoAsync');\n  }\n\n  await ServerRegistrationModule.setRegistrationInfoAsync(\n    enabled ? JSON.stringify({ isEnabled: enabled }) : null\n  );\n}\n\n// note(Chmiela): This function is exported only for testing purposes.\nexport async function __handlePersistedRegistrationInfoAsync(\n  registrationInfo: string | null | undefined\n) {\n  if (!registrationInfo) {\n    // No registration info, nothing to do\n    return;\n  }\n\n  let registration: DevicePushTokenRegistration | null = null;\n  try {\n    registration = JSON.parse(registrationInfo);\n  } catch (e) {\n    console.warn(\n      '[expo-notifications] Error encountered while fetching registration information for auto token updates.',\n      e\n    );\n  }\n\n  if (!registration?.isEnabled) {\n    // Registration is invalid or not enabled, nothing more to do\n    return;\n  }\n\n  try {\n    // Since the registration is enabled, fetching a \"new\" device token\n    // shouldn't be a problem.\n    const latestDevicePushToken = await getDevicePushTokenAsync();\n    await updatePushTokenAsync(latestDevicePushToken);\n  } catch (e) {\n    console.warn(\n      '[expo-notifications] Error encountered while updating server registration with latest device push token.',\n      e\n    );\n  }\n}\n\nif (ServerRegistrationModule.getRegistrationInfoAsync) {\n  // A global scope (to get all the updates) device push token\n  // subscription, never cleared.\n  addPushTokenListener(async (token) => {\n    try {\n      // Before updating the push token on server we always check if we should\n      // Since modules can't change their method availability while running, we\n      // can assert it's defined.\n      const registrationInfo = await ServerRegistrationModule.getRegistrationInfoAsync!();\n\n      if (!registrationInfo) {\n        // Registration is not enabled\n        return;\n      }\n\n      const registration: DevicePushTokenRegistration | null = JSON.parse(registrationInfo);\n      if (registration?.isEnabled) {\n        // Dispatch an abortable task to update\n        // registration with new token.\n        await updatePushTokenAsync(token);\n      }\n    } catch (e) {\n      console.warn(\n        '[expo-notifications] Error encountered while updating server registration with latest device push token.',\n        e\n      );\n    }\n  });\n\n  // Verify if persisted registration\n  // has successfully uploaded last known\n  // device push token. If not, retry.\n  ServerRegistrationModule.getRegistrationInfoAsync().then(__handlePersistedRegistrationInfoAsync);\n} else {\n  console.warn(\n    `[expo-notifications] Error encountered while fetching auto-registration state, new tokens will not be automatically registered on server.`,\n    new UnavailabilityError('ServerRegistrationModule', 'getRegistrationInfoAsync')\n  );\n}\n"]}