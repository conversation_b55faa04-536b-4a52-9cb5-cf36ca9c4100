{"version": 3, "file": "cancelScheduledNotificationAsync.js", "sourceRoot": "", "sources": ["../src/cancelScheduledNotificationAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,qBAAqB,MAAM,yBAAyB,CAAC;AAE5D;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,gCAAgC,CAAC,UAAkB;IAC/E,IAAI,CAAC,qBAAqB,CAAC,gCAAgC,EAAE,CAAC;QAC5D,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,kCAAkC,CAAC,CAAC;IACrF,CAAC;IAED,OAAO,MAAM,qBAAqB,CAAC,gCAAgC,CAAC,UAAU,CAAC,CAAC;AAClF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationScheduler from './NotificationScheduler';\n\n/**\n * Cancels a single scheduled notification. The scheduled notification of given ID will not trigger.\n * @param identifier The notification identifier with which `scheduleNotificationAsync` method resolved when the notification has been scheduled.\n * @return A Promise resolves once the scheduled notification is successfully canceled or if there is no scheduled notification for a given identifier.\n * @example Schedule and then cancel the notification:\n * ```ts\n * import * as Notifications from 'expo-notifications';\n *\n * async function scheduleAndCancel() {\n *   const identifier = await Notifications.scheduleNotificationAsync({\n *     content: {\n *       title: 'Hey!',\n *     },\n *     trigger: { seconds: 60, repeats: true },\n *   });\n *   await Notifications.cancelScheduledNotificationAsync(identifier);\n * }\n * ```\n * @header schedule\n */\nexport default async function cancelScheduledNotificationAsync(identifier: string): Promise<void> {\n  if (!NotificationScheduler.cancelScheduledNotificationAsync) {\n    throw new UnavailabilityError('Notifications', 'cancelScheduledNotificationAsync');\n  }\n\n  return await NotificationScheduler.cancelScheduledNotificationAsync(identifier);\n}\n"]}