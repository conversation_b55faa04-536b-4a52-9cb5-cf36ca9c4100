{"name": "expo-device", "version": "7.1.4", "description": "A universal module that gets physical information about the device running the application", "main": "build/Device.js", "types": "build/Device.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-device"], "jest": {"preset": "expo-module-scripts"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-device"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/device/", "dependencies": {"ua-parser-js": "^0.7.33"}, "devDependencies": {"@types/ua-parser-js": "^0.7.39", "expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*"}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}