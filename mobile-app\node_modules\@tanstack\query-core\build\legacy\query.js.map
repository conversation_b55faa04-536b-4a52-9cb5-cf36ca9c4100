{"version": 3, "sources": ["../../src/query.ts"], "sourcesContent": ["import {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  skipToken,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type { QueryCache } from './queryCache'\nimport type { QueryClient } from './queryClient'\nimport type {\n  CancelOptions,\n  DefaultError,\n  FetchStatus,\n  InitialDataFunction,\n  OmitKeyof,\n  QueryFunction,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n} from './types'\nimport type { QueryObserver } from './queryObserver'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  client: QueryClient\n  queryKey: TQuery<PERSON><PERSON>\n  queryHash: string\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = DefaultError> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: FetchMeta | null\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  client: QueryClient\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n    query: Query,\n  ) => void\n}\n\nexport type FetchDirection = 'forward' | 'backward'\n\nexport interface FetchMeta {\n  fetchMore?: { direction: FetchDirection }\n}\n\nexport interface FetchOptions<TData = unknown> {\n  cancelRefetch?: boolean\n  meta?: FetchMeta\n  initialPromise?: Promise<TData>\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: FetchMeta\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state: QueryState<TData, TError>\n\n  #initialState: QueryState<TData, TError>\n  #revertState?: QueryState<TData, TError>\n  #cache: QueryCache\n  #client: QueryClient\n  #retryer?: Retryer<TData>\n  observers: Array<QueryObserver<any, any, any, any, any>>\n  #defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  #abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.#abortSignalConsumed = false\n    this.#defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.#client = config.client\n    this.#cache = this.#client.getQueryCache()\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.#initialState = getDefaultState(this.options)\n    this.state = config.state ?? this.#initialState\n    this.scheduleGc()\n  }\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  get promise(): Promise<TData> | undefined {\n    return this.#retryer?.promise\n  }\n\n  setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.#defaultOptions, ...options }\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.#cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.#dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.#dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.#retryer?.promise\n    this.#retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.#initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false,\n    )\n  }\n\n  isDisabled(): boolean {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive()\n    }\n    // if a query has no observers, it should still be considered disabled if it never attempted a fetch\n    return (\n      this.options.queryFn === skipToken ||\n      this.state.dataUpdateCount + this.state.errorUpdateCount === 0\n    )\n  }\n\n  isStale(): boolean {\n    if (this.state.isInvalidated) {\n      return true\n    }\n\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale,\n      )\n    }\n\n    return this.state.data === undefined\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      this.state.data === undefined ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.#cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true })\n          } else {\n            this.#retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.#cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions<TQueryFnData>,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.data !== undefined && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetch\n        this.cancel({ silent: true })\n      } else if (this.#retryer) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.#retryer.continueRetry()\n        // Return current promise if we are already fetching\n        return this.#retryer.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = new AbortController()\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true\n          return abortController.signal\n        },\n      })\n    }\n\n    // Create fetch function\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions)\n\n      // Create query function context\n      const queryFnContext: OmitKeyof<\n        QueryFunctionContext<TQueryKey>,\n        'signal'\n      > = {\n        client: this.#client,\n        queryKey: this.queryKey,\n        meta: this.meta,\n      }\n\n      addSignalProperty(queryFnContext)\n\n      this.#abortSignalConsumed = false\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn as QueryFunction<any>,\n          queryFnContext as QueryFunctionContext<TQueryKey>,\n          this as unknown as Query,\n        )\n      }\n\n      return queryFn(queryFnContext as QueryFunctionContext<TQueryKey>)\n    }\n\n    // Trigger behavior hook\n    const context: OmitKeyof<\n      FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n      'signal'\n    > = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      client: this.#client,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(\n      context as FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n      this as unknown as Query,\n    )\n\n    // Store state in case the current fetch needs to be reverted\n    this.#revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.#dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.#cache.config.onError?.(\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n      }\n\n      // Schedule query gc after fetching\n      this.scheduleGc()\n    }\n\n    // Try to fetch the data\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise as\n        | Promise<TData>\n        | undefined,\n      fn: context.fetchFn as () => Promise<TData>,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === undefined) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        try {\n          this.setData(data)\n        } catch (error) {\n          onError(error as TError)\n          return\n        }\n\n        // Notify cache callback\n        this.#cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error as any,\n          this as Query<any, any, any, any>,\n        )\n\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.#dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true,\n    })\n\n    return this.#retryer.start()\n  }\n\n  #dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null,\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error\n\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate()\n      })\n\n      this.#cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nexport function fetchState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  data: TData | undefined,\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? 'fetching' : 'paused',\n    ...(data === undefined &&\n      ({\n        error: null,\n        status: 'pending',\n      } as const)),\n  } as const\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = data !== undefined\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? (initialDataUpdatedAt ?? Date.now()) : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'pending',\n    fetchStatus: 'idle',\n  }\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,qBAAqB;AAC9B,SAAS,UAAU,eAAe,wBAAwB;AAC1D,SAAS,iBAAiB;AAV1B;AA6JO,IAAM,QAAN,cAKG,UAAU;AAAA,EAelB,YAAY,QAA6D;AACvE,UAAM;AArBH;AAWL;AACA;AACA;AACA;AACA;AAEA;AACA;AAKE,uBAAK,sBAAuB;AAC5B,uBAAK,iBAAkB,OAAO;AAC9B,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,YAAY,CAAC;AAClB,uBAAK,SAAU,OAAO;AACtB,uBAAK,QAAS,mBAAK,SAAQ,cAAc;AACzC,SAAK,WAAW,OAAO;AACvB,SAAK,YAAY,OAAO;AACxB,uBAAK,eAAgB,gBAAgB,KAAK,OAAO;AACjD,SAAK,QAAQ,OAAO,SAAS,mBAAK;AAClC,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,OAA8B;AAChC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,IAAI,UAAsC;AApM5C;AAqMI,YAAO,wBAAK,cAAL,mBAAe;AAAA,EACxB;AAAA,EAEA,WACE,SACM;AACN,SAAK,UAAU,EAAE,GAAG,mBAAK,kBAAiB,GAAG,QAAQ;AAErD,SAAK,aAAa,KAAK,QAAQ,MAAM;AAAA,EACvC;AAAA,EAEU,iBAAiB;AACzB,QAAI,CAAC,KAAK,UAAU,UAAU,KAAK,MAAM,gBAAgB,QAAQ;AAC/D,yBAAK,QAAO,OAAO,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EAEA,QACE,SACA,SACO;AACP,UAAM,OAAO,YAAY,KAAK,MAAM,MAAM,SAAS,KAAK,OAAO;AAG/D,0BAAK,+BAAL,WAAe;AAAA,MACb;AAAA,MACA,MAAM;AAAA,MACN,eAAe,mCAAS;AAAA,MACxB,QAAQ,mCAAS;AAAA,IACnB;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,SACE,OACA,iBACM;AACN,0BAAK,+BAAL,WAAe,EAAE,MAAM,YAAY,OAAO,gBAAgB;AAAA,EAC5D;AAAA,EAEA,OAAO,SAAwC;AA9OjD;AA+OI,UAAM,WAAU,wBAAK,cAAL,mBAAe;AAC/B,6BAAK,cAAL,mBAAe,OAAO;AACtB,WAAO,UAAU,QAAQ,KAAK,IAAI,EAAE,MAAM,IAAI,IAAI,QAAQ,QAAQ;AAAA,EACpE;AAAA,EAEA,UAAgB;AACd,UAAM,QAAQ;AAEd,SAAK,OAAO,EAAE,QAAQ,KAAK,CAAC;AAAA,EAC9B;AAAA,EAEA,QAAc;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS,mBAAK,cAAa;AAAA,EAClC;AAAA,EAEA,WAAoB;AAClB,WAAO,KAAK,UAAU;AAAA,MACpB,CAAC,aAAa,eAAe,SAAS,QAAQ,SAAS,IAAI,MAAM;AAAA,IACnE;AAAA,EACF;AAAA,EAEA,aAAsB;AACpB,QAAI,KAAK,kBAAkB,IAAI,GAAG;AAChC,aAAO,CAAC,KAAK,SAAS;AAAA,IACxB;AAEA,WACE,KAAK,QAAQ,YAAY,aACzB,KAAK,MAAM,kBAAkB,KAAK,MAAM,qBAAqB;AAAA,EAEjE;AAAA,EAEA,UAAmB;AACjB,QAAI,KAAK,MAAM,eAAe;AAC5B,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,kBAAkB,IAAI,GAAG;AAChC,aAAO,KAAK,UAAU;AAAA,QACpB,CAAC,aAAa,SAAS,iBAAiB,EAAE;AAAA,MAC5C;AAAA,IACF;AAEA,WAAO,KAAK,MAAM,SAAS;AAAA,EAC7B;AAAA,EAEA,cAAc,YAAY,GAAY;AACpC,WACE,KAAK,MAAM,iBACX,KAAK,MAAM,SAAS,UACpB,CAAC,eAAe,KAAK,MAAM,eAAe,SAAS;AAAA,EAEvD;AAAA,EAEA,UAAgB;AAtSlB;AAuSI,UAAM,WAAW,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,yBAAyB,CAAC;AAExE,yCAAU,QAAQ,EAAE,eAAe,MAAM;AAGzC,6BAAK,cAAL,mBAAe;AAAA,EACjB;AAAA,EAEA,WAAiB;AA/SnB;AAgTI,UAAM,WAAW,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,uBAAuB,CAAC;AAEtE,yCAAU,QAAQ,EAAE,eAAe,MAAM;AAGzC,6BAAK,cAAL,mBAAe;AAAA,EACjB;AAAA,EAEA,YAAY,UAAwD;AAClE,QAAI,CAAC,KAAK,UAAU,SAAS,QAAQ,GAAG;AACtC,WAAK,UAAU,KAAK,QAAQ;AAG5B,WAAK,eAAe;AAEpB,yBAAK,QAAO,OAAO,EAAE,MAAM,iBAAiB,OAAO,MAAM,SAAS,CAAC;AAAA,IACrE;AAAA,EACF;AAAA,EAEA,eAAe,UAAwD;AACrE,QAAI,KAAK,UAAU,SAAS,QAAQ,GAAG;AACrC,WAAK,YAAY,KAAK,UAAU,OAAO,CAAC,MAAM,MAAM,QAAQ;AAE5D,UAAI,CAAC,KAAK,UAAU,QAAQ;AAG1B,YAAI,mBAAK,WAAU;AACjB,cAAI,mBAAK,uBAAsB;AAC7B,+BAAK,UAAS,OAAO,EAAE,QAAQ,KAAK,CAAC;AAAA,UACvC,OAAO;AACL,+BAAK,UAAS,YAAY;AAAA,UAC5B;AAAA,QACF;AAEA,aAAK,WAAW;AAAA,MAClB;AAEA,yBAAK,QAAO,OAAO,EAAE,MAAM,mBAAmB,OAAO,MAAM,SAAS,CAAC;AAAA,IACvE;AAAA,EACF;AAAA,EAEA,oBAA4B;AAC1B,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EAEA,aAAmB;AACjB,QAAI,CAAC,KAAK,MAAM,eAAe;AAC7B,4BAAK,+BAAL,WAAe,EAAE,MAAM,aAAa;AAAA,IACtC;AAAA,EACF;AAAA,EAEA,MACE,SACA,cACgB;AAtWpB;AAuWI,QAAI,KAAK,MAAM,gBAAgB,QAAQ;AACrC,UAAI,KAAK,MAAM,SAAS,WAAa,6CAAc,gBAAe;AAEhE,aAAK,OAAO,EAAE,QAAQ,KAAK,CAAC;AAAA,MAC9B,WAAW,mBAAK,WAAU;AAExB,2BAAK,UAAS,cAAc;AAE5B,eAAO,mBAAK,UAAS;AAAA,MACvB;AAAA,IACF;AAGA,QAAI,SAAS;AACX,WAAK,WAAW,OAAO;AAAA,IACzB;AAIA,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,YAAM,WAAW,KAAK,UAAU,KAAK,CAAC,MAAM,EAAE,QAAQ,OAAO;AAC7D,UAAI,UAAU;AACZ,aAAK,WAAW,SAAS,OAAO;AAAA,MAClC;AAAA,IACF;AAEA,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,UAAI,CAAC,MAAM,QAAQ,KAAK,QAAQ,QAAQ,GAAG;AACzC,gBAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,UAAM,kBAAkB,IAAI,gBAAgB;AAK5C,UAAM,oBAAoB,CAAC,WAAoB;AAC7C,aAAO,eAAe,QAAQ,UAAU;AAAA,QACtC,YAAY;AAAA,QACZ,KAAK,MAAM;AACT,6BAAK,sBAAuB;AAC5B,iBAAO,gBAAgB;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,UAAU,MAAM;AACpB,YAAM,UAAU,cAAc,KAAK,SAAS,YAAY;AAGxD,YAAM,iBAGF;AAAA,QACF,QAAQ,mBAAK;AAAA,QACb,UAAU,KAAK;AAAA,QACf,MAAM,KAAK;AAAA,MACb;AAEA,wBAAkB,cAAc;AAEhC,yBAAK,sBAAuB;AAC5B,UAAI,KAAK,QAAQ,WAAW;AAC1B,eAAO,KAAK,QAAQ;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,aAAO,QAAQ,cAAiD;AAAA,IAClE;AAGA,UAAM,UAGF;AAAA,MACF;AAAA,MACA,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,QAAQ,mBAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ;AAAA,IACF;AAEA,sBAAkB,OAAO;AAEzB,eAAK,QAAQ,aAAb,mBAAuB;AAAA,MACrB;AAAA,MACA;AAAA;AAIF,uBAAK,cAAe,KAAK;AAGzB,QACE,KAAK,MAAM,gBAAgB,UAC3B,KAAK,MAAM,gBAAc,aAAQ,iBAAR,mBAAsB,OAC/C;AACA,4BAAK,+BAAL,WAAe,EAAE,MAAM,SAAS,OAAM,aAAQ,iBAAR,mBAAsB,KAAK;AAAA,IACnE;AAEA,UAAM,UAAU,CAAC,UAAyC;AAnd9D,UAAAA,KAAAC,KAAAC,KAAA;AAqdM,UAAI,EAAE,iBAAiB,KAAK,KAAK,MAAM,SAAS;AAC9C,8BAAK,+BAAL,WAAe;AAAA,UACb,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAEA,UAAI,CAAC,iBAAiB,KAAK,GAAG;AAE5B,SAAAD,OAAAD,MAAA,mBAAK,QAAO,QAAO,YAAnB,gBAAAC,IAAA;AAAA,UAAAD;AAAA,UACE;AAAA,UACA;AAAA;AAEF,eAAAE,MAAA,mBAAK,QAAO,QAAO,cAAnB;AAAA,UAAAA;AAAA,UACE,KAAK,MAAM;AAAA,UACX;AAAA,UACA;AAAA;AAAA,MAEJ;AAGA,WAAK,WAAW;AAAA,IAClB;AAGA,uBAAK,UAAW,cAAc;AAAA,MAC5B,gBAAgB,6CAAc;AAAA,MAG9B,IAAI,QAAQ;AAAA,MACZ,OAAO,gBAAgB,MAAM,KAAK,eAAe;AAAA,MACjD,WAAW,CAAC,SAAS;AApf3B,YAAAF,KAAAC,KAAAC,KAAA;AAqfQ,YAAI,SAAS,QAAW;AACtB,cAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,oBAAQ;AAAA,cACN,yIAAyI,KAAK,SAAS;AAAA,YACzJ;AAAA,UACF;AACA,kBAAQ,IAAI,MAAM,GAAG,KAAK,SAAS,oBAAoB,CAAQ;AAC/D;AAAA,QACF;AAEA,YAAI;AACF,eAAK,QAAQ,IAAI;AAAA,QACnB,SAAS,OAAO;AACd,kBAAQ,KAAe;AACvB;AAAA,QACF;AAGA,SAAAD,OAAAD,MAAA,mBAAK,QAAO,QAAO,cAAnB,gBAAAC,IAAA,KAAAD,KAA+B,MAAM;AACrC,eAAAE,MAAA,mBAAK,QAAO,QAAO,cAAnB;AAAA,UAAAA;AAAA,UACE;AAAA,UACA,KAAK,MAAM;AAAA,UACX;AAAA;AAIF,aAAK,WAAW;AAAA,MAClB;AAAA,MACA;AAAA,MACA,QAAQ,CAAC,cAAc,UAAU;AAC/B,8BAAK,+BAAL,WAAe,EAAE,MAAM,UAAU,cAAc,MAAM;AAAA,MACvD;AAAA,MACA,SAAS,MAAM;AACb,8BAAK,+BAAL,WAAe,EAAE,MAAM,QAAQ;AAAA,MACjC;AAAA,MACA,YAAY,MAAM;AAChB,8BAAK,+BAAL,WAAe,EAAE,MAAM,WAAW;AAAA,MACpC;AAAA,MACA,OAAO,QAAQ,QAAQ;AAAA,MACvB,YAAY,QAAQ,QAAQ;AAAA,MAC5B,aAAa,QAAQ,QAAQ;AAAA,MAC7B,QAAQ,MAAM;AAAA,IAChB,CAAC;AAED,WAAO,mBAAK,UAAS,MAAM;AAAA,EAC7B;AAoFF;AA9cE;AACA;AACA;AACA;AACA;AAEA;AACA;AAlBK;AAuYL,cAAS,SAAC,QAAqC;AAC7C,QAAM,UAAU,CACd,UAC8B;AAC9B,YAAQ,OAAO,MAAM;AAAA,MACnB,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,mBAAmB,OAAO;AAAA,UAC1B,oBAAoB,OAAO;AAAA,QAC7B;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,aAAa;AAAA,QACf;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,aAAa;AAAA,QACf;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG,WAAW,MAAM,MAAM,KAAK,OAAO;AAAA,UACtC,WAAW,OAAO,QAAQ;AAAA,QAC5B;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,MAAM,OAAO;AAAA,UACb,iBAAiB,MAAM,kBAAkB;AAAA,UACzC,eAAe,OAAO,iBAAiB,KAAK,IAAI;AAAA,UAChD,OAAO;AAAA,UACP,eAAe;AAAA,UACf,QAAQ;AAAA,UACR,GAAI,CAAC,OAAO,UAAU;AAAA,YACpB,aAAa;AAAA,YACb,mBAAmB;AAAA,YACnB,oBAAoB;AAAA,UACtB;AAAA,QACF;AAAA,MACF,KAAK;AACH,cAAM,QAAQ,OAAO;AAErB,YAAI,iBAAiB,KAAK,KAAK,MAAM,UAAU,mBAAK,eAAc;AAChE,iBAAO,EAAE,GAAG,mBAAK,eAAc,aAAa,OAAO;AAAA,QACrD;AAEA,eAAO;AAAA,UACL,GAAG;AAAA,UACH;AAAA,UACA,kBAAkB,MAAM,mBAAmB;AAAA,UAC3C,gBAAgB,KAAK,IAAI;AAAA,UACzB,mBAAmB,MAAM,oBAAoB;AAAA,UAC7C,oBAAoB;AAAA,UACpB,aAAa;AAAA,UACb,QAAQ;AAAA,QACV;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,eAAe;AAAA,QACjB;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG,OAAO;AAAA,QACZ;AAAA,IACJ;AAAA,EACF;AAEA,OAAK,QAAQ,QAAQ,KAAK,KAAK;AAE/B,gBAAc,MAAM,MAAM;AACxB,SAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,eAAS,cAAc;AAAA,IACzB,CAAC;AAED,uBAAK,QAAO,OAAO,EAAE,OAAO,MAAM,MAAM,WAAW,OAAO,CAAC;AAAA,EAC7D,CAAC;AACH;AAGK,SAAS,WAMd,MACA,SACA;AACA,SAAO;AAAA,IACL,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,aAAa,SAAS,QAAQ,WAAW,IAAI,aAAa;AAAA,IAC1D,GAAI,SAAS,UACV;AAAA,MACC,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACJ;AACF;AAEA,SAAS,gBAMP,SAC2B;AAC3B,QAAM,OACJ,OAAO,QAAQ,gBAAgB,aAC1B,QAAQ,YAA2C,IACpD,QAAQ;AAEd,QAAM,UAAU,SAAS;AAEzB,QAAM,uBAAuB,UACzB,OAAO,QAAQ,yBAAyB,aACrC,QAAQ,qBAAkD,IAC3D,QAAQ,uBACV;AAEJ,SAAO;AAAA,IACL;AAAA,IACA,iBAAiB;AAAA,IACjB,eAAe,UAAW,wBAAwB,KAAK,IAAI,IAAK;AAAA,IAChE,OAAO;AAAA,IACP,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,QAAQ,UAAU,YAAY;AAAA,IAC9B,aAAa;AAAA,EACf;AACF;", "names": ["_a", "_b", "_c"]}