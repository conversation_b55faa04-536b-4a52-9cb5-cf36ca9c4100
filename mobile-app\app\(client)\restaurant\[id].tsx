// =====================================================
// ÉCRAN DÉTAIL RESTAURANT - US-C004
// =====================================================

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useOrderStore } from '../../../store/orderStore';
import { HomeServiceFallback as HomeService } from '../../../services/homeServiceFallback';
import Button from '../../../components/ui/Button';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  is_available: boolean;
  is_featured: boolean;
  preparation_time: number;
}

interface MerchantDetails {
  id: string;
  name: string;
  image_url: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  category: string;
  description: string;
  address: string;
  is_open: boolean;
  products: Product[];
}

export default function RestaurantScreen() {
  const { id } = useLocalSearchParams();
  const { addToCart, cart } = useOrderStore();
  const [merchant, setMerchant] = useState<MerchantDetails | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMerchantDetails();
  }, [id]);

  const loadMerchantDetails = async () => {
    try {
      setLoading(true);
      const merchantData = await HomeService.getMerchantById(id as string);
      setMerchant(merchantData);
    } catch (error) {
      console.error('Error loading merchant details:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (product: Product) => {
    if (!merchant) return;

    // Check if cart has items from different merchant
    if (cart && cart.merchantId !== merchant.id) {
      Alert.alert(
        'Nouveau restaurant',
        'Votre panier contient des articles d\'un autre restaurant. Voulez-vous vider le panier et ajouter cet article ?',
        [
          { text: 'Annuler', style: 'cancel' },
          {
            text: 'Vider et ajouter',
            onPress: () => {
              addToCart(merchant.id, {
                productId: product.id,
                name: product.name,
                price: product.price,
                quantity: 1,
                options: [],
                notes: '',
              });
            },
          },
        ]
      );
      return;
    }

    addToCart(merchant.id, {
      productId: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      options: [],
      notes: '',
    });

    Alert.alert('Ajouté au panier', `${product.name} a été ajouté à votre panier`);
  };

  const handleViewCart = () => {
    router.push('/(client)/cart');
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Chargement...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!merchant) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Restaurant introuvable</Text>
          <TouchableOpacity onPress={() => router.back()}>
            <Text style={styles.backButton}>Retour</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#1C1C1E" />
        </TouchableOpacity>
        <TouchableOpacity>
          <Ionicons name="heart-outline" size={24} color="#1C1C1E" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Restaurant Image */}
        <Image
          source={{ uri: merchant.image_url }}
          style={styles.restaurantImage}
        />

        {/* Restaurant Info */}
        <View style={styles.restaurantInfo}>
          <Text style={styles.restaurantName}>{merchant.name}</Text>
          <Text style={styles.restaurantDescription}>{merchant.description}</Text>
          
          <View style={styles.restaurantMeta}>
            <View style={styles.metaItem}>
              <Ionicons name="star" size={16} color="#FFD700" />
              <Text style={styles.metaText}>{merchant.rating}</Text>
            </View>
            <View style={styles.metaItem}>
              <Ionicons name="time" size={16} color="#8E8E93" />
              <Text style={styles.metaText}>{merchant.deliveryTime}</Text>
            </View>
            <View style={styles.metaItem}>
              <Ionicons name="bicycle" size={16} color="#8E8E93" />
              <Text style={styles.metaText}>{merchant.deliveryFee} FCFA</Text>
            </View>
          </View>

          <View style={styles.addressContainer}>
            <Ionicons name="location" size={16} color="#8E8E93" />
            <Text style={styles.addressText}>{merchant.address}</Text>
          </View>
        </View>

        {/* Products */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Menu</Text>
          {merchant.products?.map((product) => (
            <View key={product.id} style={styles.productCard}>
              <Image
                source={{ uri: product.image_url }}
                style={styles.productImage}
              />
              <View style={styles.productInfo}>
                <Text style={styles.productName}>{product.name}</Text>
                <Text style={styles.productDescription} numberOfLines={2}>
                  {product.description}
                </Text>
                <View style={styles.productFooter}>
                  <Text style={styles.productPrice}>{product.price} FCFA</Text>
                  <TouchableOpacity
                    style={[
                      styles.addButton,
                      !product.is_available && styles.addButtonDisabled
                    ]}
                    onPress={() => handleAddToCart(product)}
                    disabled={!product.is_available}
                  >
                    <Ionicons name="add" size={20} color="#FFFFFF" />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Cart Button */}
      {cart && cart.items.length > 0 && (
        <View style={styles.cartContainer}>
          <Button
            title={`Voir le panier (${cart.items.length})`}
            onPress={handleViewCart}
            style={styles.cartButton}
          />
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    zIndex: 1,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButton: {
    color: '#007AFF',
    fontSize: 16,
    marginTop: 16,
  },
  restaurantImage: {
    width: '100%',
    height: 200,
  },
  restaurantInfo: {
    backgroundColor: '#FFFFFF',
    padding: 24,
    marginBottom: 16,
  },
  restaurantName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  restaurantDescription: {
    fontSize: 16,
    color: '#8E8E93',
    lineHeight: 24,
    marginBottom: 16,
  },
  restaurantMeta: {
    flexDirection: 'row',
    gap: 24,
    marginBottom: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 14,
    color: '#8E8E93',
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  addressText: {
    fontSize: 14,
    color: '#8E8E93',
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    paddingTop: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1C1E',
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  productCard: {
    flexDirection: 'row',
    padding: 16,
    marginHorizontal: 24,
    marginBottom: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 16,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4,
  },
  productDescription: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 20,
    marginBottom: 8,
  },
  productFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  productPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  addButton: {
    backgroundColor: '#007AFF',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonDisabled: {
    backgroundColor: '#8E8E93',
  },
  cartContainer: {
    padding: 24,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
  },
  cartButton: {
    backgroundColor: '#007AFF',
  },
});
