import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import { Platform } from 'react-native';

// Storage conditionnel selon la plateforme
let storage: any = null;

if (Platform.OS !== 'web') {
  // Utiliser AsyncStorage seulement sur mobile
  const AsyncStorage = require('@react-native-async-storage/async-storage').default;
  storage = AsyncStorage;
} else {
  // Utiliser localStorage sur web
  storage = {
    getItem: (key: string) => {
      if (typeof window !== 'undefined') {
        return Promise.resolve(window.localStorage.getItem(key));
      }
      return Promise.resolve(null);
    },
    setItem: (key: string, value: string) => {
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, value);
      }
      return Promise.resolve();
    },
    removeItem: (key: string) => {
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(key);
      }
      return Promise.resolve();
    },
  };
}

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL ?? 'https://demo.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ?? 'demo-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: storage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  realtime: {
    // Désactiver les WebSockets pour éviter les erreurs
    transport: 'websocket',
    timeout: 20000,
    heartbeatIntervalMs: 30000,
    reconnectAfterMs: () => Math.random() * 10000,
    encode: (payload, callback) => {
      return callback(JSON.stringify(payload));
    },
    decode: (payload, callback) => {
      try {
        return callback(null, JSON.parse(payload));
      } catch (err) {
        return callback(err);
      }
    },
  },
});

// TODO: Add secure storage implementation for React Native
// Example using AsyncStorage:
/*
import AsyncStorage from '@react-native-async-storage/async-storage';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
*/