// Configuration Supabase simplifiée pour React Native
import 'react-native-url-polyfill/auto';

// Utiliser directement la version simplifiée pour éviter les erreurs WebSocket
export { supabase } from './supabase-simple';

// TODO: Add secure storage implementation for React Native
// Example using AsyncStorage:
/*
import AsyncStorage from '@react-native-async-storage/async-storage';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
*/