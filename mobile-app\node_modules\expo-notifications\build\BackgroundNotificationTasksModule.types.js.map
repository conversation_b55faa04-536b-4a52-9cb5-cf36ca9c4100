{"version": 3, "file": "BackgroundNotificationTasksModule.types.js", "sourceRoot": "", "sources": ["../src/BackgroundNotificationTasksModule.types.ts"], "names": [], "mappings": "AAOA,MAAM,CAAN,IAAY,4BAIX;AAJD,WAAY,4BAA4B;IACtC,mFAAU,CAAA;IACV,qFAAW,CAAA;IACX,mFAAU,CAAA;AACZ,CAAC,EAJW,4BAA4B,KAA5B,4BAA4B,QAIvC", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\nexport interface BackgroundNotificationTasksModule extends ProxyNativeModule {\n  registerTaskAsync: (taskName: string) => Promise<null>;\n  unregisterTaskAsync: (taskName: string) => Promise<null>;\n}\n\nexport enum BackgroundNotificationResult {\n  NoData = 1,\n  NewData = 2,\n  Failed = 3,\n}\n"]}