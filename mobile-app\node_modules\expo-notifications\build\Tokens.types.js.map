{"version": 3, "file": "Tokens.types.js", "sourceRoot": "", "sources": ["../src/Tokens.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { type Platform } from 'expo-modules-core';\n\n// @docsMissing\nexport interface NativeDevicePushToken {\n  type: 'ios' | 'android';\n  data: string;\n}\n\n// @docsMissing\n/**\n * @hidden\n */\nexport interface WebDevicePushToken {\n  type: 'web';\n  data: {\n    endpoint: string;\n    keys: WebDevicePushTokenKeys;\n  };\n}\n\n// @docsMissing\n/**\n * @hidden\n */\nexport type WebDevicePushTokenKeys = {\n  p256dh: string;\n  auth: string;\n};\n\n// @docsMissing\nexport type ExplicitlySupportedDevicePushToken = NativeDevicePushToken;\n\nexport type ImplicitlySupportedDevicePushToken = {\n  /**\n   * Either `android` or `ios`.\n   */\n  type: Exclude<typeof Platform.OS, ExplicitlySupportedDevicePushToken['type']>;\n  /**\n   * The push token as a string for a native platform.\n   */\n  data: any;\n};\n\n/**\n * In simple terms, an object of `type: Platform.OS` and `data: any`. The `data` type depends on the environment - on a native device it will be a string,\n * which you can then use to send notifications via Firebase Cloud Messaging (Android) or APNs (iOS).\n */\nexport type DevicePushToken =\n  | ExplicitlySupportedDevicePushToken\n  | ImplicitlySupportedDevicePushToken;\n\n/**\n * Object which contains the Expo push token in the `data` field. Use the value from `data` to send notifications via Expo Notifications service.\n */\nexport interface ExpoPushToken {\n  /**\n   * Always set to `\"expo\"`.\n   */\n  type: 'expo';\n  /**\n   * The acquired push token.\n   */\n  data: string;\n}\n\n// @needsAudit\nexport interface ExpoPushTokenOptions {\n  /**\n   * Endpoint URL override.\n   */\n  baseUrl?: string;\n  /**\n   * Request URL override.\n   */\n  url?: string;\n  /**\n   * Request body override.\n   */\n  type?: string;\n  // @docsMissing\n  deviceId?: string;\n  /**\n   * On iOS, there are two push notification services: \"sandbox\" and \"production\".\n   * This defines whether the push token is supposed to be used with the sandbox platform notification service.\n   * Defaults to [`Application.getIosPushNotificationServiceEnvironmentAsync()`](./application/#applicationgetiospushnotificationserviceenvironmentasync)\n   * exposed by `expo-application` or `false`. Most probably you won't need to customize that.\n   * You may want to customize that if you don't want to install `expo-application` and still use the sandbox APNs.\n   * @platform ios\n   */\n  development?: boolean;\n  /**\n   * The ID of the project to which the token should be attributed.\n   * Defaults to [`Constants.expoConfig.extra.eas.projectId`](./constants/#easconfig) exposed by `expo-constants`.\n   *\n   * When using EAS Build, this value is automatically set. However, it is\n   * **recommended** to set it manually. Once you have EAS Build configured, you can find\n   * the value in **app.json** under `extra.eas.projectId`. You can copy and paste it into your code.\n   * If you are not using EAS Build, it will fallback to [`Constants.expoConfig?.extra?.eas?.projectId`](./constants/#manifest).\n   */\n  projectId?: string;\n  /**\n   * The ID of the application to which the token should be attributed.\n   * Defaults to [`Application.applicationId`](./application/#applicationapplicationid) exposed by `expo-application`.\n   */\n  applicationId?: string;\n  /**\n   * The device push token with which to register at the backend.\n   * Defaults to a token fetched with [`getDevicePushTokenAsync()`](#getdevicepushtokenasync).\n   */\n  devicePushToken?: DevicePushToken;\n}\n"]}