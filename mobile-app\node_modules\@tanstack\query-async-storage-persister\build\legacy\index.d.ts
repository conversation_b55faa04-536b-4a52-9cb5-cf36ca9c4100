import { PersistedClient, Promisable, AsyncStorage, MaybePromise, Persister } from '@tanstack/query-persist-client-core';

type AsyncPersistRetryer = (props: {
    persistedClient: PersistedClient;
    error: Error;
    errorCount: number;
}) => Promisable<PersistedClient | undefined>;
interface CreateAsyncStoragePersisterOptions {
    /** The storage client used for setting and retrieving items from cache.
     * For SSR pass in `undefined`. Note that window.localStorage can be
     * `null` in Android WebViews depending on how they are configured.
     */
    storage: AsyncStorage<string> | undefined | null;
    /** The key to use when storing the cache */
    key?: string;
    /** To avoid spamming,
     * pass a time in ms to throttle saving the cache to disk */
    throttleTime?: number;
    /**
     * How to serialize the data to storage.
     * @default `JSON.stringify`
     */
    serialize?: (client: PersistedClient) => MaybePromise<string>;
    /**
     * How to deserialize the data from storage.
     * @default `JSON.parse`
     */
    deserialize?: (cachedString: string) => MaybePromise<PersistedClient>;
    retry?: AsyncPersistRetryer;
}
declare const createAsyncStoragePersister: ({ storage, key, throttleTime, serialize, deserialize, retry, }: CreateAsyncStoragePersisterOptions) => Persister;

export { type AsyncPersistRetryer, createAsyncStoragePersister };
