{"version": 3, "file": "getAllScheduledNotificationsAsync.js", "sourceRoot": "", "sources": ["../src/getAllScheduledNotificationsAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,qBAAqB,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,sBAAsB,EAAE,MAAM,iCAAiC,CAAC;AAEzE;;;;GAIG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,iCAAiC;IAC7D,IAAI,CAAC,qBAAqB,CAAC,iCAAiC,EAAE,CAAC;QAC7D,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,mCAAmC,CAAC,CAAC;IACtF,CAAC;IAED,OAAO,CAAC,MAAM,qBAAqB,CAAC,iCAAiC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CACvF,sBAAsB,CAAC,OAAO,CAAC,CAChC,CAAC;AACJ,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationScheduler from './NotificationScheduler';\nimport { NotificationRequest } from './Notifications.types';\nimport { mapNotificationRequest } from './utils/mapNotificationResponse';\n\n/**\n * Fetches information about all scheduled notifications.\n * @return Returns a Promise resolving to an array of objects conforming to the [`Notification`](#notification) interface.\n * @header schedule\n */\nexport default async function getAllScheduledNotificationsAsync(): Promise<NotificationRequest[]> {\n  if (!NotificationScheduler.getAllScheduledNotificationsAsync) {\n    throw new UnavailabilityError('Notifications', 'getAllScheduledNotificationsAsync');\n  }\n\n  return (await NotificationScheduler.getAllScheduledNotificationsAsync()).map((request) =>\n    mapNotificationRequest(request)\n  );\n}\n"]}