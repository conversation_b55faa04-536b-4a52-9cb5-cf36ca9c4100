const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Configuration pour ignorer certains modules problématiques
config.resolver.blockList = [
  /node_modules\/ws\/lib\/stream\.js$/,
  /node_modules\/ws\/lib\/buffer-util\.js$/,
  /node_modules\/ws\/lib\/validation\.js$/,
  /node_modules\/ws\/lib\/websocket-server\.js$/,
  /node_modules\/ws\/lib\/websocket\.js$/,
];

// Résolution des extensions de fichiers
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'mjs',
  'cjs'
];

// Configuration pour les transformations
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    // Configuration pour optimiser le bundle
    keep_fnames: true,
    mangle: {
      keep_fnames: true,
    },
  },
};

// Configuration pour les assets
config.resolver.assetExts = [
  ...config.resolver.assetExts,
  'bin',
  'txt',
  'jpg',
  'png',
  'json',
  'mp4',
  'mov',
  'mp3',
  'wav',
  'aac',
  'm4a',
  'pdf',
  'svg',
  'ttf',
  'otf',
  'woff',
  'woff2',
];

module.exports = config;
