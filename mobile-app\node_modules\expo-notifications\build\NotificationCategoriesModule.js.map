{"version": 3, "file": "NotificationCategoriesModule.js", "sourceRoot": "", "sources": ["../src/NotificationCategoriesModule.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,MAAM,4BAA4B,GAAiC;IACjE,KAAK,CAAC,8BAA8B;QAClC,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,KAAK,CAAC,4BAA4B;QAChC,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,8BAA8B,CAAC,CAAC;IACjF,CAAC;IACD,KAAK,CAAC,+BAA+B;QACnC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,WAAW,KAAI,CAAC;IAChB,eAAe,KAAI,CAAC;CACrB,CAAC;AAEF,eAAe,4BAA4B,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport { NotificationCategoriesModule } from './NotificationCategoriesModule.types';\n\nconst notificationCategoriesModule: NotificationCategoriesModule = {\n  async getNotificationCategoriesAsync() {\n    return [];\n  },\n  async setNotificationCategoryAsync() {\n    throw new UnavailabilityError('Notifications', 'setNotificationCategoryAsync');\n  },\n  async deleteNotificationCategoryAsync() {\n    return false;\n  },\n  addListener() {},\n  removeListeners() {},\n};\n\nexport default notificationCategoriesModule;\n"]}