{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.camera", "version": "16.1.6", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.exifinterface", "module": "exifinterface", "version": {"requires": "1.3.7"}}, {"group": "androidx.appcompat", "module": "appcompat", "version": {"requires": "1.1.0"}}], "files": [{"name": "expo.modules.camera-16.1.6.aar", "url": "expo.modules.camera-16.1.6.aar", "size": 230628, "sha512": "65089f399b432984a7f86fcd86c411f702d4ddc38c93e811fe97766abd8f3d7549b8f47f5ec3922bd87e789301206d645eb42e98b8c0dba10e65088f14da0549", "sha256": "9d84b191d2bc9f820b69d644964ccc497595649864a16ec8a8591ad3a064d43d", "sha1": "50a8239dc0ecf0a2a3e58271fd5df0968b70ac9e", "md5": "bdad20c248360b9daaac2315c6df8f32"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "androidx.camera", "module": "camera-core", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-camera2", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-lifecycle", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-video", "version": {"requires": "1.4.1"}}, {"group": "com.google.android.gms", "module": "play-services-code-scanner", "version": {"requires": "16.1.0"}}, {"group": "androidx.camera", "module": "camera-view", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-extensions", "version": {"requires": "1.4.1"}}, {"group": "com.google.mlkit", "module": "barcode-scanning", "version": {"requires": "17.2.0"}}, {"group": "androidx.camera", "module": "camera-mlkit-vision", "version": {"requires": "1.4.1"}}, {"group": "androidx.exifinterface", "module": "exifinterface", "version": {"requires": "1.3.7"}}, {"group": "androidx.appcompat", "module": "appcompat", "version": {"requires": "1.1.0"}}], "files": [{"name": "expo.modules.camera-16.1.6.aar", "url": "expo.modules.camera-16.1.6.aar", "size": 230628, "sha512": "65089f399b432984a7f86fcd86c411f702d4ddc38c93e811fe97766abd8f3d7549b8f47f5ec3922bd87e789301206d645eb42e98b8c0dba10e65088f14da0549", "sha256": "9d84b191d2bc9f820b69d644964ccc497595649864a16ec8a8591ad3a064d43d", "sha1": "50a8239dc0ecf0a2a3e58271fd5df0968b70ac9e", "md5": "bdad20c248360b9daaac2315c6df8f32"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.camera-16.1.6-sources.jar", "url": "expo.modules.camera-16.1.6-sources.jar", "size": 27381, "sha512": "1de7875b905368401f9e575a41d2c0758cc9de41d4597738b63bf5e9c2faf640917f6333c58709651eafe5112161d892316b71c327df2abfe7370cec575c661e", "sha256": "c5dc7747d946e99cb95b0d289c095670235aff71630b0b4d7afe9c716fe1cf28", "sha1": "399e71b50b8aa3780924870eac56be753d173cb6", "md5": "e1f0747edfb489d76d6c1b3edc547c24"}]}]}