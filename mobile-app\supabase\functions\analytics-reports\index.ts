import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface ReportRequest {
  report_type: 'daily' | 'weekly' | 'monthly' | 'custom'
  start_date?: string
  end_date?: string
  filters?: {
    country_id?: string
    city_id?: string
    merchant_id?: string
    delivery_type?: string
  }
  metrics?: string[]
}

interface ReportData {
  summary: {
    total_orders: number
    total_revenue: number
    total_deliveries: number
    average_order_value: number
    customer_satisfaction: number
  }
  trends: {
    orders_by_day: Array<{ date: string; count: number; revenue: number }>
    popular_times: Array<{ hour: number; order_count: number }>
    delivery_performance: Array<{ date: string; avg_time: number; success_rate: number }>
  }
  breakdowns: {
    by_delivery_type: Array<{ type: string; count: number; revenue: number }>
    by_payment_method: Array<{ method: string; count: number; revenue: number }>
    by_location: Array<{ location: string; count: number; revenue: number }>
  }
  top_performers: {
    merchants: Array<{ id: string; name: string; orders: number; revenue: number; rating: number }>
    riders: Array<{ id: string; name: string; deliveries: number; rating: number; efficiency: number }>
    products: Array<{ id: string; name: string; orders: number; revenue: number }>
  }
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Verify user has admin access
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', (await supabase.auth.getUser()).data.user?.id)
      .single()

    if (profileError || !userProfile || !['admin', 'super_admin'].includes(userProfile.role)) {
      return new Response('Unauthorized', { status: 403 })
    }

    const requestData: ReportRequest = await req.json()
    
    const {
      report_type,
      start_date,
      end_date,
      filters = {},
      metrics = ['all']
    } = requestData

    // Calculate date range
    const dateRange = calculateDateRange(report_type, start_date, end_date)
    
    console.log('Generating analytics report:', { 
      report_type, 
      date_range: dateRange, 
      filters 
    })

    // Generate comprehensive report
    const reportData: ReportData = {
      summary: await generateSummaryMetrics(supabase, dateRange, filters),
      trends: await generateTrendAnalysis(supabase, dateRange, filters),
      breakdowns: await generateBreakdownAnalysis(supabase, dateRange, filters),
      top_performers: await generateTopPerformers(supabase, dateRange, filters)
    }

    // Cache the report for future requests
    await cacheReport(supabase, report_type, dateRange, filters, reportData)

    return new Response(
      JSON.stringify({
        success: true,
        report_type,
        date_range: dateRange,
        filters,
        generated_at: new Date().toISOString(),
        data: reportData
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Analytics report error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error',
        code: 'ANALYTICS_ERROR'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

function calculateDateRange(reportType: string, startDate?: string, endDate?: string) {
  const now = new Date()
  let start: Date
  let end: Date = new Date(now)

  switch (reportType) {
    case 'daily':
      start = new Date(now)
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      break
    
    case 'weekly':
      start = new Date(now)
      start.setDate(now.getDate() - 7)
      start.setHours(0, 0, 0, 0)
      break
    
    case 'monthly':
      start = new Date(now)
      start.setMonth(now.getMonth() - 1)
      start.setHours(0, 0, 0, 0)
      break
    
    case 'custom':
      if (!startDate || !endDate) {
        throw new Error('Custom date range requires start_date and end_date')
      }
      start = new Date(startDate)
      end = new Date(endDate)
      break
    
    default:
      throw new Error('Invalid report type')
  }

  return {
    start: start.toISOString(),
    end: end.toISOString()
  }
}

async function generateSummaryMetrics(supabase: any, dateRange: any, filters: any) {
  // Build base query with filters
  let ordersQuery = supabase
    .from('orders')
    .select(`
      id,
      total_amount,
      status,
      delivery_type,
      created_at,
      delivered_at,
      merchants!inner(id, country_id, city_id)
    `)
    .gte('created_at', dateRange.start)
    .lte('created_at', dateRange.end)

  // Apply filters
  if (filters.country_id) {
    ordersQuery = ordersQuery.eq('merchants.country_id', filters.country_id)
  }
  if (filters.city_id) {
    ordersQuery = ordersQuery.eq('merchants.city_id', filters.city_id)
  }
  if (filters.merchant_id) {
    ordersQuery = ordersQuery.eq('merchant_id', filters.merchant_id)
  }
  if (filters.delivery_type) {
    ordersQuery = ordersQuery.eq('delivery_type', filters.delivery_type)
  }

  const { data: orders, error } = await ordersQuery

  if (error) {
    throw new Error('Failed to fetch orders data')
  }

  // Calculate metrics
  const totalOrders = orders.length
  const deliveredOrders = orders.filter(o => o.status === 'delivered')
  const totalDeliveries = deliveredOrders.length
  const totalRevenue = deliveredOrders.reduce((sum, o) => sum + (o.total_amount || 0), 0)
  const averageOrderValue = totalDeliveries > 0 ? totalRevenue / totalDeliveries : 0

  // Get customer satisfaction (average ratings)
  const { data: ratings } = await supabase
    .from('ratings')
    .select('rating')
    .in('order_id', deliveredOrders.map(o => o.id))
    .eq('rated_type', 'merchant')

  const customerSatisfaction = ratings && ratings.length > 0 
    ? ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length 
    : 0

  return {
    total_orders: totalOrders,
    total_revenue: Math.round(totalRevenue),
    total_deliveries: totalDeliveries,
    average_order_value: Math.round(averageOrderValue),
    customer_satisfaction: Math.round(customerSatisfaction * 100) / 100
  }
}

async function generateTrendAnalysis(supabase: any, dateRange: any, filters: any) {
  // Orders by day
  const { data: dailyOrders } = await supabase
    .rpc('get_daily_order_trends', {
      start_date: dateRange.start,
      end_date: dateRange.end,
      country_filter: filters.country_id,
      city_filter: filters.city_id,
      merchant_filter: filters.merchant_id
    })

  // Popular ordering times
  const { data: hourlyOrders } = await supabase
    .rpc('get_hourly_order_distribution', {
      start_date: dateRange.start,
      end_date: dateRange.end,
      country_filter: filters.country_id
    })

  // Delivery performance trends
  const { data: deliveryPerformance } = await supabase
    .rpc('get_delivery_performance_trends', {
      start_date: dateRange.start,
      end_date: dateRange.end,
      country_filter: filters.country_id
    })

  return {
    orders_by_day: dailyOrders || [],
    popular_times: hourlyOrders || [],
    delivery_performance: deliveryPerformance || []
  }
}

async function generateBreakdownAnalysis(supabase: any, dateRange: any, filters: any) {
  // By delivery type
  const { data: byDeliveryType } = await supabase
    .rpc('get_orders_by_delivery_type', {
      start_date: dateRange.start,
      end_date: dateRange.end,
      country_filter: filters.country_id
    })

  // By payment method
  const { data: byPaymentMethod } = await supabase
    .rpc('get_orders_by_payment_method', {
      start_date: dateRange.start,
      end_date: dateRange.end,
      country_filter: filters.country_id
    })

  // By location (cities)
  const { data: byLocation } = await supabase
    .rpc('get_orders_by_location', {
      start_date: dateRange.start,
      end_date: dateRange.end,
      country_filter: filters.country_id
    })

  return {
    by_delivery_type: byDeliveryType || [],
    by_payment_method: byPaymentMethod || [],
    by_location: byLocation || []
  }
}

async function generateTopPerformers(supabase: any, dateRange: any, filters: any) {
  // Top merchants
  const { data: topMerchants } = await supabase
    .rpc('get_top_merchants', {
      start_date: dateRange.start,
      end_date: dateRange.end,
      country_filter: filters.country_id,
      limit_count: 10
    })

  // Top riders
  const { data: topRiders } = await supabase
    .rpc('get_top_riders', {
      start_date: dateRange.start,
      end_date: dateRange.end,
      country_filter: filters.country_id,
      limit_count: 10
    })

  // Top products
  const { data: topProducts } = await supabase
    .rpc('get_top_products', {
      start_date: dateRange.start,
      end_date: dateRange.end,
      merchant_filter: filters.merchant_id,
      limit_count: 10
    })

  return {
    merchants: topMerchants || [],
    riders: topRiders || [],
    products: topProducts || []
  }
}

async function cacheReport(supabase: any, reportType: string, dateRange: any, filters: any, data: ReportData) {
  try {
    const cacheKey = generateCacheKey(reportType, dateRange, filters)
    
    await supabase
      .from('analytics_cache')
      .upsert({
        cache_key: cacheKey,
        report_type: reportType,
        date_range: dateRange,
        filters,
        data,
        expires_at: new Date(Date.now() + getCacheTTL(reportType)).toISOString(),
        created_at: new Date().toISOString()
      })
  } catch (error) {
    console.error('Failed to cache report:', error)
    // Don't throw - caching is optional
  }
}

function generateCacheKey(reportType: string, dateRange: any, filters: any): string {
  const filterString = Object.keys(filters)
    .sort()
    .map(key => `${key}:${filters[key]}`)
    .join('|')
  
  return `${reportType}:${dateRange.start}:${dateRange.end}:${filterString}`
}

function getCacheTTL(reportType: string): number {
  // Cache TTL in milliseconds
  const ttls = {
    'daily': 15 * 60 * 1000,    // 15 minutes for daily reports
    'weekly': 60 * 60 * 1000,   // 1 hour for weekly reports
    'monthly': 4 * 60 * 60 * 1000, // 4 hours for monthly reports
    'custom': 30 * 60 * 1000    // 30 minutes for custom reports
  }
  
  return ttls[reportType] || ttls['custom']
}
