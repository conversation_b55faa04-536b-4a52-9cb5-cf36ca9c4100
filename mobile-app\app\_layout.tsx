// =====================================================
// LAYOUT RACINE - NAVIGATION MULTI-RÔLES
// =====================================================

import React from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFonts } from 'expo-font';
import 'react-native-reanimated';
import { QueryProvider } from '../providers/QueryProvider';

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    return null;
  }

  return (
    <QueryProvider>
      <Stack screenOptions={{ headerShown: false }}>
        {/* Point d'entrée */}
        <Stack.Screen name="index" />

        {/* Splash Screen */}
        <Stack.Screen name="splash" />

        {/* Authentification */}
        <Stack.Screen name="(auth)/onboarding" />
        <Stack.Screen name="(auth)/login" />
        <Stack.Screen name="(auth)/register" />

        {/* Interfaces par rôle */}
        <Stack.Screen name="(client)" />
        <Stack.Screen name="(deliverer)" />
        <Stack.Screen name="(merchant)" />

        {/* Écrans partagés */}
        <Stack.Screen
          name="order-tracking/[id]"
          options={{
            presentation: 'modal',
            title: 'Suivi de commande'
          }}
        />

        {/* Test */}
        <Stack.Screen name="test" />
      </Stack>
      <StatusBar style="auto" />
    </QueryProvider>
  );
}


