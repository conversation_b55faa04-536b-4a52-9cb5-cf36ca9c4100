// =====================================================
// LAYOUT RACINE - NAVIGATION MULTI-RÔLES
// =====================================================

import React, { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useFonts } from 'expo-font';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuthStore } from '../store/authStore';
import { useNotifications } from '../hooks/utils/useNotifications';
import 'react-native-reanimated';

// Configuration React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
  },
});

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <AppInitializer />
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </QueryClientProvider>
  );
}

// Composant d'initialisation de l'app
function AppInitializer() {
  const { isAuthenticated, user, refreshToken } = useAuthStore();
  const { requestPermissions } = useNotifications();

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Vérifier la session existante
      await refreshToken();

      // Demander les permissions notifications
      await requestPermissions();

      console.log('✅ Application initialisée');
    } catch (error) {
      console.error('❌ Erreur initialisation app:', error);
    }
  };

  return <RootNavigator />;
}

// Navigateur principal avec gestion des rôles
function RootNavigator() {
  const { isAuthenticated, user } = useAuthStore();

  return (
    <>
      <Stack screenOptions={{ headerShown: false }}>
        {!isAuthenticated ? (
          // Écrans d'authentification
          <>
            <Stack.Screen name="index" />
            <Stack.Screen name="(auth)/login" />
            <Stack.Screen name="(auth)/register" />
            <Stack.Screen name="(auth)/onboarding" />
          </>
        ) : (
          // Écrans selon le rôle utilisateur
          <>
            {user?.role === 'client' && (
              <Stack.Screen name="(client)" />
            )}
            {user?.role === 'deliverer' && (
              <Stack.Screen name="(deliverer)" />
            )}
            {user?.role === 'merchant' && (
              <Stack.Screen name="(merchant)" />
            )}

            {/* Écrans partagés */}
            <Stack.Screen
              name="order-tracking/[id]"
              options={{
                presentation: 'modal',
                title: 'Suivi de commande'
              }}
            />
            <Stack.Screen
              name="notifications"
              options={{
                presentation: 'modal',
                title: 'Notifications'
              }}
            />
          </>
        )}
      </Stack>
      <StatusBar style="auto" />
    </>
  );
}
