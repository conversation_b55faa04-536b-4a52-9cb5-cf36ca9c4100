import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { AuthProvider, useAuth } from '../context/AuthContext'; // Import AuthProvider and useAuth

export default function RootLayout() {
  const colorScheme = useColorScheme();

  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <AuthProvider>
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <RootLayoutNav />
        <StatusBar style="auto" />
      </ThemeProvider>
    </AuthProvider>
  );
}

function RootLayoutNav() {
  const { isAuthenticated, userRole, loading } = useAuth();

  // Optional: Render a loading screen while auth status is being checked
  if (loading) {
    return null; // Or a <LoadingScreen /> component
  }

  return (
    <Stack>
      {isAuthenticated ? (
        // User is authenticated, show appropriate tab navigator based on role
        userRole === 'client' ? (
          <Stack.Screen name="(client)" options={{ headerShown: false }} />
        ) : userRole === 'deliverer' ? (
          <Stack.Screen name="(deliverer)" options={{ headerShown: false }} />
        ) : userRole === 'merchant' ? (
          <Stack.Screen name="(merchant)" options={{ headerShown: false }} />
        ) : (
          // Fallback if role is not recognized or still loading
          <Stack.Screen name="index" options={{ headerShown: false }} /> // Or a specific loading/error screen
        )
      ) : (
        // User is not authenticated, show the Auth screen
        <Stack.Screen name="index" options={{ headerShown: false }} /> // Assuming index.tsx is your AuthScreen or a redirect to it
      )}
    </Stack>
  );
}
