// =====================================================
// LAYOUT RACINE - NAVIGATION MULTI-RÔLES
// =====================================================

import React from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFonts } from 'expo-font';
import 'react-native-reanimated';

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    return null;
  }

  return (
    <>
      <Stack screenOptions={{ headerShown: false }}>
        {/* Écran d'accueil */}
        <Stack.Screen name="index" />

        {/* Authentification */}
        <Stack.Screen name="(auth)/onboarding" />
        <Stack.Screen name="(auth)/login" />
        <Stack.Screen name="(auth)/register" />

        {/* Interfaces par rôle */}
        <Stack.Screen name="(client)" />
        <Stack.Screen name="(deliverer)" />
        <Stack.Screen name="(merchant)" />

        {/* Écrans partagés */}
        <Stack.Screen
          name="order-tracking/[id]"
          options={{
            presentation: 'modal',
            title: 'Suivi de commande'
          }}
        />

        {/* Test */}
        <Stack.Screen name="test" />
      </Stack>
      <StatusBar style="auto" />
    </>
  );
}


