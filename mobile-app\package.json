{"name": "mobile-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.77.0", "abort-controller": "^3.0.0", "base-64": "^1.0.0", "buffer": "^6.0.3", "date-fns": "^4.1.0", "expo": "~53.0.9", "expo-blur": "~14.1.4", "expo-camera": "~16.1.6", "expo-constants": "~17.1.6", "expo-device": "~7.1.4", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-network": "~7.1.5", "expo-notifications": "~0.31.2", "expo-router": "~5.0.6", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-sqlite": "~15.2.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "lottie-react-native": "7.2.2", "process": "^0.11.10", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.4", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "zod": "^3.25.28", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}