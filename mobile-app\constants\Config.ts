// =====================================================
// CONFIGURATION DE L'APPLICATION
// =====================================================

export const Config = {
  // Environment
  environment: process.env.EXPO_PUBLIC_ENVIRONMENT || 'development',
  apiBaseUrl: process.env.EXPO_PUBLIC_API_BASE_URL || 'https://api.deliveryapp.africa',
  appVersion: process.env.EXPO_PUBLIC_APP_VERSION || '1.0.0',

  // Supabase
  supabase: {
    url: process.env.EXPO_PUBLIC_SUPABASE_URL || '',
    anonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || '',
  },

  // Google Services
  googleMapsApiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || '',

  // African Market Settings
  defaultCountry: process.env.EXPO_PUBLIC_DEFAULT_COUNTRY || 'SEN',
  defaultCurrency: process.env.EXPO_PUBLIC_DEFAULT_CURRENCY || 'XOF',
  defaultLanguage: process.env.EXPO_PUBLIC_DEFAULT_LANGUAGE || 'fr',
  supportedCountries: (process.env.EXPO_PUBLIC_SUPPORTED_COUNTRIES || 'SEN,KEN,GHA,NGA').split(','),

  // Performance Settings
  offlineSupport: process.env.EXPO_PUBLIC_OFFLINE_SUPPORT === 'true',
  lowBandwidthMode: process.env.EXPO_PUBLIC_LOW_BANDWIDTH_MODE === 'true',
  compressionEnabled: process.env.EXPO_PUBLIC_COMPRESSION_ENABLED === 'true',
  cacheSize: process.env.EXPO_PUBLIC_CACHE_SIZE || '50MB',

  // Development Settings
  enableLogging: process.env.EXPO_PUBLIC_ENABLE_LOGGING === 'true',
  debugMode: process.env.EXPO_PUBLIC_DEBUG_MODE === 'true',
  mockPayments: process.env.EXPO_PUBLIC_MOCK_PAYMENTS === 'true',
  mockLocation: process.env.EXPO_PUBLIC_MOCK_LOCATION === 'true',

  // Feature Flags
  features: {
    biometricAuth: process.env.EXPO_PUBLIC_ENABLE_BIOMETRIC_AUTH === 'true',
    darkMode: process.env.EXPO_PUBLIC_ENABLE_DARK_MODE === 'true',
    multiLanguage: process.env.EXPO_PUBLIC_ENABLE_MULTI_LANGUAGE === 'true',
    offlineMaps: process.env.EXPO_PUBLIC_ENABLE_OFFLINE_MAPS === 'true',
  },

  // African Countries Configuration
  countries: {
    SEN: {
      name: 'Sénégal',
      currency: 'XOF',
      currencySymbol: 'CFA',
      language: 'fr',
      timezone: 'Africa/Dakar',
      phonePrefix: '+221',
      paymentMethods: ['orange_money', 'wave', 'cash'],
    },
    KEN: {
      name: 'Kenya',
      currency: 'KES',
      currencySymbol: 'KSh',
      language: 'en',
      timezone: 'Africa/Nairobi',
      phonePrefix: '+254',
      paymentMethods: ['mpesa', 'airtel_money', 'cash'],
    },
    GHA: {
      name: 'Ghana',
      currency: 'GHS',
      currencySymbol: '₵',
      language: 'en',
      timezone: 'Africa/Accra',
      phonePrefix: '+233',
      paymentMethods: ['mtn_momo', 'airtel_money', 'cash'],
    },
    NGA: {
      name: 'Nigeria',
      currency: 'NGN',
      currencySymbol: '₦',
      language: 'en',
      timezone: 'Africa/Lagos',
      phonePrefix: '+234',
      paymentMethods: ['paystack', 'flutterwave', 'cash'],
    },
    CIV: {
      name: 'Côte d\'Ivoire',
      currency: 'XOF',
      currencySymbol: 'CFA',
      language: 'fr',
      timezone: 'Africa/Abidjan',
      phonePrefix: '+225',
      paymentMethods: ['orange_money', 'mtn_momo', 'cash'],
    },
    MLI: {
      name: 'Mali',
      currency: 'XOF',
      currencySymbol: 'CFA',
      language: 'fr',
      timezone: 'Africa/Bamako',
      phonePrefix: '+223',
      paymentMethods: ['orange_money', 'cash'],
    },
    BFA: {
      name: 'Burkina Faso',
      currency: 'XOF',
      currencySymbol: 'CFA',
      language: 'fr',
      timezone: 'Africa/Ouagadougou',
      phonePrefix: '+226',
      paymentMethods: ['orange_money', 'cash'],
    },
    UGA: {
      name: 'Uganda',
      currency: 'UGX',
      currencySymbol: 'USh',
      language: 'en',
      timezone: 'Africa/Kampala',
      phonePrefix: '+256',
      paymentMethods: ['mtn_momo', 'airtel_money', 'cash'],
    },
    TZA: {
      name: 'Tanzania',
      currency: 'TZS',
      currencySymbol: 'TSh',
      language: 'sw',
      timezone: 'Africa/Dar_es_Salaam',
      phonePrefix: '+255',
      paymentMethods: ['mpesa', 'airtel_money', 'cash'],
    },
    RWA: {
      name: 'Rwanda',
      currency: 'RWF',
      currencySymbol: 'RF',
      language: 'en',
      timezone: 'Africa/Kigali',
      phonePrefix: '+250',
      paymentMethods: ['mtn_momo', 'airtel_money', 'cash'],
    },
  },

  // Delivery Settings
  delivery: {
    maxRadiusKm: 50,
    defaultFee: 500,
    urgentMultiplier: 1.5,
    freeDeliveryThreshold: 5000,
    estimatedTimeMinutes: 30,
  },

  // App Store URLs
  appStoreUrls: {
    ios: 'https://apps.apple.com/app/delivery-app-africa/id123456789',
    android: 'https://play.google.com/store/apps/details?id=com.deliveryapp.africa',
  },

  // Support
  support: {
    email: '<EMAIL>',
    phone: '+221701234567',
    website: 'https://deliveryapp.africa',
    privacyPolicy: 'https://deliveryapp.africa/privacy',
    termsOfService: 'https://deliveryapp.africa/terms',
  },
};

export default Config;
