// src/index.ts
import { asyncThrottle } from "./asyncThrottle.js";
import { noop } from "./utils.js";
var createAsyncStoragePersister = ({
  storage,
  key = `REACT_QUERY_OFFLINE_CACHE`,
  throttleTime = 1e3,
  serialize = JSON.stringify,
  deserialize = JSON.parse,
  retry
}) => {
  if (storage) {
    const trySave = async (persistedClient) => {
      try {
        const serialized = await serialize(persistedClient);
        await storage.setItem(key, serialized);
        return;
      } catch (error) {
        return error;
      }
    };
    return {
      persistClient: asyncThrottle(
        async (persistedClient) => {
          let client = persistedClient;
          let error = await trySave(client);
          let errorCount = 0;
          while (error && client) {
            errorCount++;
            client = await retry?.({
              persistedClient: client,
              error,
              errorCount
            });
            if (client) {
              error = await trySave(client);
            }
          }
        },
        { interval: throttleTime }
      ),
      restoreClient: async () => {
        const cacheString = await storage.getItem(key);
        if (!cacheString) {
          return;
        }
        return await deserialize(cacheString);
      },
      removeClient: () => storage.removeItem(key)
    };
  }
  return {
    persistClient: noop,
    restoreClient: () => Promise.resolve(void 0),
    removeClient: noop
  };
};
export {
  createAsyncStoragePersister
};
//# sourceMappingURL=index.js.map