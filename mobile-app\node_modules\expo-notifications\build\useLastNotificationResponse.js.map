{"version": 3, "file": "useLastNotificationResponse.js", "sourceRoot": "", "sources": ["../src/useLastNotificationResponse.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAGlD,OAAO,EACL,uCAAuC,EACvC,sCAAsC,EACtC,gCAAgC,GACjC,MAAM,wBAAwB,CAAC;AAEhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AACH,MAAM,CAAC,OAAO,UAAU,2BAA2B;IACjD,MAAM,CAAC,wBAAwB,EAAE,2BAA2B,CAAC,GAC3D,QAAQ,CAA4B,SAAS,CAAC,CAAC;IAEjD,yEAAyE;IACzE,eAAe,CAAC,GAAG,EAAE;QACnB,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,2FAA2F;QAC3F,gCAAgC,EAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YACnD,IAAI,SAAS,EAAE,CAAC;gBACd,2BAA2B,CAAC,CAAC,YAAY,EAAE,EAAE,CAC3C,qBAAqB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAC9C,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,kFAAkF;QAClF,MAAM,YAAY,GAAG,uCAAuC,CAAC,CAAC,QAAQ,EAAE,EAAE,CACxE,2BAA2B,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,qBAAqB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAC7F,CAAC;QACF,MAAM,yBAAyB,GAAG,sCAAsC,CAAC,GAAG,EAAE;YAC5E,2BAA2B,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,EAAE;YACV,SAAS,GAAG,KAAK,CAAC;YAClB,YAAY,CAAC,MAAM,EAAE,CAAC;YACtB,yBAAyB,CAAC,MAAM,EAAE,CAAC;QACrC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,wBAAwB,CAAC;AAClC,CAAC;AAED,iEAAiE;AACjE,uFAAuF;AACvF,kDAAkD;AAClD,MAAM,CAAC,MAAM,qBAAqB,GAAG,CACnC,YAAuC,EACvC,WAAwC,EACxC,EAAE;IACF,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,OAAO,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU;QACjD,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU;QAC3C,CAAC,CAAC,WAAW;QACb,CAAC,CAAC,YAAY,CAAC;AACnB,CAAC,CAAC", "sourcesContent": ["import { useLayoutEffect, useState } from 'react';\n\nimport { MaybeNotificationResponse, NotificationResponse } from './Notifications.types';\nimport {\n  addNotificationResponseReceivedListener,\n  addNotificationResponseClearedListener,\n  getLastNotificationResponseAsync,\n} from './NotificationsEmitter';\n\n/**\n * A React hook which returns the notification response that was received most recently\n * (a notification response designates an interaction with a notification, such as tapping on it).\n *\n * To clear the last notification response, use [`clearLastNotificationResponseAsync()`](#notificationsclearlastnotificationresponseasync).\n *\n * > If you don't want to use a hook, you can use `Notifications.getLastNotificationResponseAsync()` instead.\n *\n * @return The hook may return one of these three types/values:\n * - `undefined` - until we're sure of what to return,\n * - `null` - if no notification response has been received yet,\n * - a [`NotificationResponse`](#notificationresponse) object - if a notification response was received.\n *\n * @example\n * Responding to a notification tap by opening a URL that could be put into the notification's `data`\n * (opening the URL is your responsibility and is not a part of the `expo-notifications` API):\n * ```jsx\n * import * as Notifications from 'expo-notifications';\n * import { Linking } from 'react-native';\n *\n * export default function App() {\n *   const lastNotificationResponse = Notifications.useLastNotificationResponse();\n *   React.useEffect(() => {\n *     if (\n *       lastNotificationResponse &&\n *       lastNotificationResponse.notification.request.content.data.url &&\n *       lastNotificationResponse.actionIdentifier === Notifications.DEFAULT_ACTION_IDENTIFIER\n *     ) {\n *       Linking.openURL(lastNotificationResponse.notification.request.content.data.url);\n *     }\n *   }, [lastNotificationResponse]);\n *   return (\n *     // Your app content\n *   );\n * }\n * ```\n * @header listen\n */\nexport default function useLastNotificationResponse() {\n  const [lastNotificationResponse, setLastNotificationResponse] =\n    useState<MaybeNotificationResponse>(undefined);\n\n  // useLayoutEffect ensures the listener is registered as soon as possible\n  useLayoutEffect(() => {\n    let isMounted = true;\n    // Get the last response first, in case it was set earlier (even in native code on startup)\n    getLastNotificationResponseAsync().then((response) => {\n      if (isMounted) {\n        setLastNotificationResponse((prevResponse) =>\n          determineNextResponse(prevResponse, response)\n        );\n      }\n    });\n\n    // Set up listener for responses that come in, and set the last response if needed\n    const subscription = addNotificationResponseReceivedListener((response) =>\n      setLastNotificationResponse((prevResponse) => determineNextResponse(prevResponse, response))\n    );\n    const clearResponseSubscription = addNotificationResponseClearedListener(() => {\n      setLastNotificationResponse(null);\n    });\n    return () => {\n      isMounted = false;\n      subscription.remove();\n      clearResponseSubscription.remove();\n    };\n  }, []);\n\n  return lastNotificationResponse;\n}\n\n// returns the new response if it is different from the previous,\n// also has to return undefined until we're sure of what to return (null or a response)\n// the transition from response to null is invalid\nexport const determineNextResponse = (\n  prevResponse: MaybeNotificationResponse,\n  newResponse: NotificationResponse | null\n) => {\n  if (!newResponse) {\n    return null;\n  }\n  if (!prevResponse) {\n    return newResponse;\n  }\n  return prevResponse.notification.request.identifier !==\n    newResponse.notification.request.identifier\n    ? newResponse\n    : prevResponse;\n};\n"]}