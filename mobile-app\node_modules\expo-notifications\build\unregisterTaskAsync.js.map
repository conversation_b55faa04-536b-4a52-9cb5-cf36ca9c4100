{"version": 3, "file": "unregisterTaskAsync.js", "sourceRoot": "", "sources": ["../src/unregisterTaskAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,iCAAiC,MAAM,qCAAqC,CAAC;AAEpF;;;;GAIG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,mBAAmB,CAAC,QAAgB;IAChE,IAAI,CAAC,iCAAiC,CAAC,mBAAmB,EAAE,CAAC;QAC3D,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,qBAAqB,CAAC,CAAC;IACxE,CAAC;IAED,OAAO,MAAM,iCAAiC,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AAC/E,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport BackgroundNotificationTasksModule from './BackgroundNotificationTasksModule';\n\n/**\n * Used to unregister tasks registered with `registerTaskAsync` method.\n * @param taskName The string you passed to `registerTaskAsync` as the `taskName` parameter.\n * @header inBackground\n */\nexport default async function unregisterTaskAsync(taskName: string): Promise<null> {\n  if (!BackgroundNotificationTasksModule.unregisterTaskAsync) {\n    throw new UnavailabilityError('Notifications', 'unregisterTaskAsync');\n  }\n\n  return await BackgroundNotificationTasksModule.unregisterTaskAsync(taskName);\n}\n"]}