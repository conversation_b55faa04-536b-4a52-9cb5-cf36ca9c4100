/**
 * Iconography System - Universal and Culturally Appropriate Icons
 * Optimized for African markets with clear, recognizable symbols
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Path, Circle, Rect, Line, Polygon } from 'react-native-svg';

// Icon sizes following touch target guidelines
export const IconSizes = {
  xs: 16,
  sm: 20,
  md: 24,
  lg: 32,
  xl: 48,
  xxl: 64,
} as const;

// Base icon props interface
interface IconProps {
  size?: keyof typeof IconSizes | number;
  color?: string;
  strokeWidth?: number;
  style?: any;
}

// Default icon props
const defaultIconProps: Required<Omit<IconProps, 'style'>> = {
  size: 'md',
  color: '#525252', // neutral-600
  strokeWidth: 2,
};

// Helper to get icon size
const getIconSize = (size: IconProps['size']): number => {
  if (typeof size === 'number') return size;
  return IconSizes[size || 'md'];
};

// =============================================================================
// NAVIGATION ICONS
// =============================================================================

export const HomeIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Path
        d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M9 22V12H15V22"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export const SearchIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Circle
        cx="11"
        cy="11"
        r="8"
        stroke={color}
        strokeWidth={strokeWidth}
      />
      <Path
        d="M21 21L16.65 16.65"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export const MenuIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Line x1="3" y1="6" x2="21" y2="6" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" />
      <Line x1="3" y1="12" x2="21" y2="12" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" />
      <Line x1="3" y1="18" x2="21" y2="18" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" />
    </Svg>
  );
};

export const BackIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Path
        d="M19 12H5M12 19L5 12L12 5"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

// =============================================================================
// DELIVERY & LOGISTICS ICONS
// =============================================================================

export const TruckIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Rect
        x="1"
        y="3"
        width="15"
        height="13"
        rx="2"
        stroke={color}
        strokeWidth={strokeWidth}
      />
      <Path
        d="M16 8H20L23 11V16H16V8Z"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Circle cx="5.5" cy="18.5" r="2.5" stroke={color} strokeWidth={strokeWidth} />
      <Circle cx="18.5" cy="18.5" r="2.5" stroke={color} strokeWidth={strokeWidth} />
    </Svg>
  );
};

export const MotorcycleIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Circle cx="5" cy="18" r="3" stroke={color} strokeWidth={strokeWidth} />
      <Circle cx="19" cy="18" r="3" stroke={color} strokeWidth={strokeWidth} />
      <Path
        d="M5 18H8L12 8H16L19 18"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M10 8L14 4H18"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export const BikeIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Circle cx="5.5" cy="17.5" r="3.5" stroke={color} strokeWidth={strokeWidth} />
      <Circle cx="18.5" cy="17.5" r="3.5" stroke={color} strokeWidth={strokeWidth} />
      <Path
        d="M15 6L9 17L5.5 14"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M9 17L15 6H18"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Circle cx="15" cy="6" r="1" stroke={color} strokeWidth={strokeWidth} />
    </Svg>
  );
};

export const WalkingIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="4" r="2" stroke={color} strokeWidth={strokeWidth} />
      <Path
        d="M10.5 8L12 12L14 8"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M12 12V20"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
      <Path
        d="M8 16L10 20"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
      <Path
        d="M16 16L14 20"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
    </Svg>
  );
};

// =============================================================================
// FOOD & COMMERCE ICONS
// =============================================================================

export const RestaurantIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Path
        d="M3 2V7C3 9.21 4.79 11 7 11S11 9.21 11 7V2"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M7 11V22"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
      <Path
        d="M21 2V22"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
      <Path
        d="M18 2V8C18 10.21 19.79 12 22 12"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export const ShoppingBagIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Path
        d="M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M3 6H21"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
      <Path
        d="M16 10C16 11.0609 15.5786 12.0783 14.8284 12.8284C14.0783 13.5786 13.0609 14 12 14C10.9391 14 9.92172 13.5786 9.17157 12.8284C8.42143 12.0783 8 11.0609 8 10"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export const PharmacyIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Rect
        x="3"
        y="6"
        width="18"
        height="15"
        rx="2"
        stroke={color}
        strokeWidth={strokeWidth}
      />
      <Path
        d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M12 10V18"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
      <Path
        d="M8 14H16"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
    </Svg>
  );
};

// =============================================================================
// PAYMENT & MONEY ICONS
// =============================================================================

export const MobileMoneyIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Rect
        x="5"
        y="2"
        width="14"
        height="20"
        rx="2"
        stroke={color}
        strokeWidth={strokeWidth}
      />
      <Path
        d="M12 18H12.01"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M9 8H15"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
      <Path
        d="M9 12H15"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
    </Svg>
  );
};

export const CashIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Rect
        x="2"
        y="6"
        width="20"
        height="12"
        rx="2"
        stroke={color}
        strokeWidth={strokeWidth}
      />
      <Circle cx="12" cy="12" r="2" stroke={color} strokeWidth={strokeWidth} />
      <Path
        d="M6 12H6.01"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M18 12H18.01"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

// =============================================================================
// STATUS & FEEDBACK ICONS
// =============================================================================

export const CheckIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Path
        d="M20 6L9 17L4 12"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export const XIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Line x1="18" y1="6" x2="6" y2="18" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" />
      <Line x1="6" y1="6" x2="18" y2="18" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" />
    </Svg>
  );
};

export const AlertIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Path
        d="M10.29 3.86L1.82 18C1.64 18.37 1.54 18.78 1.53 19.19C1.52 19.6 1.6 20.01 1.77 20.37C1.94 20.73 2.2 21.04 2.52 21.27C2.84 21.5 3.21 21.64 3.6 21.68H20.4C20.79 21.64 21.16 21.5 21.48 21.27C21.8 21.04 22.06 20.73 22.23 20.37C22.4 20.01 22.48 19.6 22.47 19.19C22.46 18.78 22.36 18.37 22.18 18L13.71 3.86C13.53 3.49 13.25 3.18 12.91 2.95C12.57 2.72 12.17 2.59 11.76 2.58C11.35 2.57 10.95 2.68 10.6 2.89C10.25 3.1 9.96 3.4 9.76 3.76L10.29 3.86Z"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Line x1="12" y1="9" x2="12" y2="13" stroke={color} strokeWidth={strokeWidth} strokeLinecap="round" />
      <Path
        d="M12 17H12.01"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export const InfoIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth={strokeWidth} />
      <Path
        d="M12 16V12"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
      <Path
        d="M12 8H12.01"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

// =============================================================================
// USER & PROFILE ICONS
// =============================================================================

export const UserIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Path
        d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Circle cx="12" cy="7" r="4" stroke={color} strokeWidth={strokeWidth} />
    </Svg>
  );
};

export const StarIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Polygon
        points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

// =============================================================================
// LOCATION & MAP ICONS
// =============================================================================

export const MapPinIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Path
        d="M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.364 3.63604C20.0518 5.32387 21 7.61305 21 10Z"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Circle cx="12" cy="10" r="3" stroke={color} strokeWidth={strokeWidth} />
    </Svg>
  );
};

export const ClockIcon: React.FC<IconProps> = (props) => {
  const { size, color, strokeWidth } = { ...defaultIconProps, ...props };
  const iconSize = getIconSize(size);
  
  return (
    <Svg width={iconSize} height={iconSize} viewBox="0 0 24 24" fill="none">
      <Circle cx="12" cy="12" r="10" stroke={color} strokeWidth={strokeWidth} />
      <Path
        d="M12 6V12L16 14"
        stroke={color}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

// =============================================================================
// ICON REGISTRY
// =============================================================================

export const IconRegistry = {
  // Navigation
  home: HomeIcon,
  search: SearchIcon,
  menu: MenuIcon,
  back: BackIcon,
  
  // Delivery
  truck: TruckIcon,
  motorcycle: MotorcycleIcon,
  bike: BikeIcon,
  walking: WalkingIcon,
  
  // Commerce
  restaurant: RestaurantIcon,
  shopping: ShoppingBagIcon,
  pharmacy: PharmacyIcon,
  
  // Payment
  mobileMoney: MobileMoneyIcon,
  cash: CashIcon,
  
  // Status
  check: CheckIcon,
  x: XIcon,
  alert: AlertIcon,
  info: InfoIcon,
  
  // User
  user: UserIcon,
  star: StarIcon,
  
  // Location
  mapPin: MapPinIcon,
  clock: ClockIcon,
} as const;

export type IconName = keyof typeof IconRegistry;
