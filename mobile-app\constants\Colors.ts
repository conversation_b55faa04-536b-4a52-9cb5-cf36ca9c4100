/**
 * Design System Colors - Optimized for African Markets
 * Palette inspired by African landscapes and cultures
 * High contrast ratios for outdoor visibility and accessibility
 */

// Primary Colors - Orange Soleil (African Sunset)
const primaryColors = {
  50: '#FFF7ED',   // Aube dorée
  100: '#FFEDD5',  // Sable du Sahara
  200: '#FED7AA',  // Terre de Sienna
  300: '#FDBA74',  // Coucher de soleil
  400: '#FB923C',  // Orange vif
  500: '#F97316',  // Orange principal
  600: '#EA580C',  // Orange foncé
  700: '#C2410C',  // Terre cuite
  800: '#9A3412',  // Brique rouge
  900: '#7C2D12',  // Acajou
};

// Secondary Colors - Vert Baobab
const secondaryColors = {
  50: '#F0FDF4',   // Feuille tendre
  100: '#DCFCE7',  // Vert clair
  200: '#BBF7D0',  // Vert prairie
  300: '#86EFAC',  // Vert vif
  400: '#4ADE80',  // Vert nature
  500: '#22C55E',  // Vert principal
  600: '#16A34A',  // Vert forêt
  700: '#15803D',  // Vert foncé
  800: '#166534',  // Vert profond
  900: '#14532D',  // Vert sombre
};

// Neutral Colors - High contrast for readability
const neutralColors = {
  0: '#FFFFFF',     // Blanc pur
  50: '#FAFAFA',    // Blanc cassé
  100: '#F5F5F5',   // Gris très clair
  200: '#E5E5E5',   // Gris clair
  300: '#D4D4D4',   // Gris moyen clair
  400: '#A3A3A3',   // Gris moyen
  500: '#737373',   // Gris
  600: '#525252',   // Gris foncé
  700: '#404040',   // Gris très foncé
  800: '#262626',   // Presque noir
  900: '#171717',   // Noir profond
  950: '#0A0A0A',   // Noir absolu
};

// Semantic Colors
const semanticColors = {
  success: {
    light: '#D1FAE5',
    main: '#10B981',
    dark: '#047857',
  },
  warning: {
    light: '#FEF3C7',
    main: '#F59E0B',
    dark: '#D97706',
  },
  error: {
    light: '#FEE2E2',
    main: '#EF4444',
    dark: '#DC2626',
  },
  info: {
    light: '#DBEAFE',
    main: '#3B82F6',
    dark: '#1D4ED8',
  },
};

export const Colors = {
  // Light mode - optimized for outdoor visibility
  light: {
    // Primary colors
    primary: primaryColors[500],
    primaryLight: primaryColors[100],
    primaryDark: primaryColors[700],

    // Secondary colors
    secondary: secondaryColors[500],
    secondaryLight: secondaryColors[100],
    secondaryDark: secondaryColors[700],

    // Background colors
    background: neutralColors[0],
    backgroundSecondary: neutralColors[50],
    backgroundTertiary: neutralColors[100],

    // Text colors
    text: neutralColors[900],
    textSecondary: neutralColors[600],
    textTertiary: neutralColors[500],
    textInverse: neutralColors[0],

    // Border colors
    border: neutralColors[200],
    borderSecondary: neutralColors[300],

    // Icon colors
    icon: neutralColors[600],
    iconSecondary: neutralColors[500],

    // Tab colors
    tabIconDefault: neutralColors[500],
    tabIconSelected: primaryColors[500],
    tint: primaryColors[500],

    // Semantic colors
    success: semanticColors.success.main,
    warning: semanticColors.warning.main,
    error: semanticColors.error.main,
    info: semanticColors.info.main,
  },

  // Dark mode - OLED optimized for battery saving
  dark: {
    // Primary colors (slightly muted for dark mode)
    primary: primaryColors[400],
    primaryLight: primaryColors[300],
    primaryDark: primaryColors[600],

    // Secondary colors
    secondary: secondaryColors[400],
    secondaryLight: secondaryColors[300],
    secondaryDark: secondaryColors[600],

    // Background colors (true black for OLED)
    background: '#000000',
    backgroundSecondary: neutralColors[950],
    backgroundTertiary: neutralColors[900],

    // Text colors
    text: neutralColors[0],
    textSecondary: neutralColors[300],
    textTertiary: neutralColors[400],
    textInverse: neutralColors[900],

    // Border colors
    border: neutralColors[800],
    borderSecondary: neutralColors[700],

    // Icon colors
    icon: neutralColors[300],
    iconSecondary: neutralColors[400],

    // Tab colors
    tabIconDefault: neutralColors[400],
    tabIconSelected: primaryColors[400],
    tint: primaryColors[400],

    // Semantic colors (adjusted for dark mode)
    success: semanticColors.success.light,
    warning: semanticColors.warning.light,
    error: semanticColors.error.light,
    info: semanticColors.info.light,
  },

  // Raw color palettes for component usage
  primary: primaryColors,
  secondary: secondaryColors,
  neutral: neutralColors,
  semantic: semanticColors,
};
