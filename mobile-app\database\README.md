# SCHÉMA BASE DE DONNÉES - APPLICATION LIVRAISON AFRIQUE

## 🎯 OVERVIEW

Ce schéma de base de données PostgreSQL est conçu spécifiquement pour une application de livraison adaptée aux marchés africains. Il prend en compte les spécificités locales : paiements mobile money, support multi-langues, gestion des zones hors connexion, et optimisations pour les infrastructures limitées.

## 📋 STRUCTURE DU SCHÉMA

### 📁 Fichiers SQL

1. **[01-schema-core.sql](./01-schema-core.sql)** - Tables principales et types de base
2. **[02-schema-orders-delivery.sql](./02-schema-orders-delivery.sql)** - Système de commandes et livraison
3. **[03-schema-payments.sql](./03-schema-payments.sql)** - Paiements et mobile money
4. **[04-schema-notifications-audit.sql](./04-schema-notifications-audit.sql)** - Notifications et audit
5. **[05-triggers-functions.sql](./05-triggers-functions.sql)** - Triggers et fonctions
6. **[06-seed-data.sql](./06-seed-data.sql)** - Données d'initialisation
7. **[07-indexes-optimization.sql](./07-indexes-optimization.sql)** - Index et optimisations

## 🏗️ ARCHITECTURE

### Rôles Utilisateurs
- **Customer** : Clients qui passent commandes
- **Delivery Rider** : Livreurs qui acceptent et livrent
- **Merchant** : Commerçants/restaurants qui gèrent catalogues
- **Admin** : Administrateurs avec supervision globale

### Types de Livraison
- **Food** : Livraison de repas
- **Package** : Livraison de colis
- **Grocery** : Livraison d'épicerie
- **Pharmacy** : Livraison pharmacie
- **Wholesale** : Livraison en gros
- **Documents** : Livraison de documents

## 🌍 SPÉCIFICITÉS AFRICAINES

### Support Multi-Pays
```sql
-- 17 pays africains pré-configurés
countries: SEN, GHA, NGA, KEN, TZA, RWA, CIV, MLI, BFA, UGA, ETH, MAR, TUN, EGY, CMR, GAB, ZAF, ZWE
```

### Paiements Mobile Money
```sql
-- Providers supportés
mobile_money_provider: mpesa, orange_money, mtn_momo, airtel_money, wave, moov_money, tigo_pesa, ecocash
```

### Support Multi-Langues
```sql
-- Langues supportées
supported_language: fr, en, wo, sw, ha, yo, ig, am, ar, pt
```

### Fuseaux Horaires Africains
- **Afrique/Dakar** : Sénégal, Mali, Burkina Faso
- **Afrique/Nairobi** : Kenya, Tanzanie, Ouganda
- **Afrique/Lagos** : Nigeria, Cameroun
- **Afrique/Accra** : Ghana, Côte d'Ivoire
- **Afrique/Kigali** : Rwanda
- **Afrique/Casablanca** : Maroc
- **Afrique/Le_Caire** : Égypte

## 📊 TABLES PRINCIPALES

### 🏢 Tables Core
- **countries** : Pays et configurations locales
- **cities** : Villes avec géolocalisation
- **delivery_zones** : Zones de livraison avec polygones
- **profiles** : Profils utilisateurs étendus
- **addresses** : Adresses avec géolocalisation

### 🛒 Tables Commerce
- **merchants** : Commerçants et restaurants
- **product_categories** : Catégories de produits
- **products** : Catalogue produits avec variantes
- **orders** : Commandes avec tracking
- **order_items** : Articles de commande

### 🚚 Tables Livraison
- **delivery_riders** : Livreurs avec véhicules
- **delivery_tracking** : Tracking temps réel
- **ratings** : Évaluations multi-directionnelles

### 💳 Tables Paiement
- **payments** : Transactions principales
- **mobile_money_payments** : Spécifique mobile money
- **card_payments** : Paiements par carte
- **refunds** : Remboursements
- **user_wallets** : Portefeuilles utilisateurs
- **wallet_transactions** : Historique portefeuille

### 🔔 Tables Notifications
- **notification_templates** : Templates multi-langues
- **notifications** : Notifications utilisateurs
- **notification_preferences** : Préférences utilisateur

### 📋 Tables Audit
- **audit_logs** : Logs d'audit complets
- **user_sessions** : Sessions utilisateur
- **system_errors** : Erreurs système

## 🚀 INSTALLATION

### Prérequis
```bash
# PostgreSQL 14+
sudo apt install postgresql-14 postgresql-14-postgis

# Extensions requises
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";
```

### Déploiement
```bash
# 1. Créer la base de données
createdb delivery_app_africa

# 2. Exécuter les scripts dans l'ordre
psql -d delivery_app_africa -f 01-schema-core.sql
psql -d delivery_app_africa -f 02-schema-orders-delivery.sql
psql -d delivery_app_africa -f 03-schema-payments.sql
psql -d delivery_app_africa -f 04-schema-notifications-audit.sql
psql -d delivery_app_africa -f 05-triggers-functions.sql
psql -d delivery_app_africa -f 06-seed-data.sql
psql -d delivery_app_africa -f 07-indexes-optimization.sql
```

## 🔧 FONCTIONNALITÉS AVANCÉES

### Géolocalisation
- **PostGIS** : Support géospatial complet
- **Zones de livraison** : Polygones avec calculs de distance
- **Tracking temps réel** : Positions GPS des livreurs
- **Optimisation routes** : Index spatiaux optimisés

### Offline Support
- **Sync status** : Gestion des données hors ligne
- **Conflict resolution** : Résolution automatique des conflits
- **Queue management** : File d'attente des actions offline

### Performance
- **Index composites** : Optimisés pour requêtes fréquentes
- **Vues matérialisées** : Statistiques pré-calculées
- **Partitioning** : Tables partitionnées par date
- **Cleanup automatique** : Purge des anciennes données

### Sécurité
- **Audit complet** : Traçabilité de toutes les actions
- **Chiffrement** : Données sensibles chiffrées
- **Rate limiting** : Protection contre les abus
- **Session management** : Gestion sécurisée des sessions

## 📈 OPTIMISATIONS

### Index Spécialisés
```sql
-- Recherche géospatiale
idx_merchants_location_active USING GIST(location)
idx_delivery_zones_boundary USING GIST(boundary)

-- Recherche textuelle
idx_products_search_vector USING GIN(to_tsvector())
idx_merchants_search_vector USING GIN(to_tsvector())

-- Requêtes fréquentes
idx_orders_customer_status_created (customer_id, status, created_at)
idx_payments_order_status_created (order_id, status, created_at)
```

### Vues Matérialisées
- **merchant_stats** : Statistiques commerçants
- **rider_stats** : Performance livreurs
- **popular_delivery_zones** : Zones populaires

### Maintenance Automatique
```sql
-- Rafraîchissement stats (toutes les heures)
SELECT refresh_materialized_views();

-- Nettoyage données anciennes (hebdomadaire)
SELECT cleanup_old_data();

-- Optimisation base (quotidien)
SELECT optimize_database();
```

## 🔍 MONITORING

### Métriques Clés
- **Performance requêtes** : Temps d'exécution moyen
- **Utilisation index** : Index inutilisés
- **Taille tables** : Croissance des données
- **Erreurs système** : Monitoring des erreurs

### Alertes Recommandées
- Requêtes lentes (>100ms)
- Espace disque faible (<20%)
- Connexions élevées (>80% max)
- Erreurs critiques système

## 🌐 INTÉGRATIONS

### APIs Externes
- **Mobile Money** : M-Pesa, Orange Money, Wave
- **Cartes** : Paystack, Flutterwave, Stripe
- **SMS** : Twilio, Africa's Talking
- **Push** : Firebase Cloud Messaging
- **Maps** : Google Maps, OpenStreetMap

### Webhooks
- **Paiements** : Callbacks providers
- **Livraison** : Updates tracking
- **Notifications** : Delivery status

## 📱 SUPPORT MOBILE

### Synchronisation
- **Incremental sync** : Sync différentielle
- **Conflict resolution** : Résolution automatique
- **Offline queue** : Actions en attente
- **Background sync** : Sync en arrière-plan

### Optimisations Mobile
- **Data compression** : Réduction taille payloads
- **Pagination** : Chargement par pages
- **Caching** : Cache intelligent côté client
- **Delta updates** : Mises à jour partielles

## 🔒 SÉCURITÉ

### Authentification
- **Supabase Auth** : Intégration native
- **Multi-factor** : 2FA via SMS
- **Session management** : Tokens sécurisés
- **Device tracking** : Suivi des appareils

### Autorisation
- **Row Level Security** : RLS Supabase
- **Role-based access** : Contrôle par rôle
- **Data isolation** : Isolation par tenant
- **Audit trail** : Traçabilité complète

## 📚 DOCUMENTATION

### Guides Développeur
- **API Documentation** : Endpoints et schemas
- **Integration Guide** : Guide d'intégration
- **Best Practices** : Bonnes pratiques
- **Troubleshooting** : Résolution problèmes

### Guides Administrateur
- **Deployment Guide** : Guide de déploiement
- **Monitoring Setup** : Configuration monitoring
- **Backup Strategy** : Stratégie de sauvegarde
- **Scaling Guide** : Guide de montée en charge

## 🚀 ÉVOLUTIVITÉ

### Scaling Horizontal
- **Read replicas** : Répliques lecture
- **Sharding** : Partitionnement horizontal
- **Connection pooling** : Pool de connexions
- **Load balancing** : Répartition de charge

### Scaling Vertical
- **Resource optimization** : Optimisation ressources
- **Query optimization** : Optimisation requêtes
- **Index tuning** : Optimisation index
- **Cache strategies** : Stratégies de cache

Ce schéma est conçu pour supporter une croissance de 0 à 1M+ utilisateurs tout en maintenant des performances optimales et une expérience utilisateur fluide sur le continent africain.
