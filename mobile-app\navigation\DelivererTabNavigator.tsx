import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import DelivererDashboardScreen from '../screens/DelivererDashboardScreen';
import DelivererDeliveriesScreen from '../screens/DelivererDeliveriesScreen';
import DelivererEarningsScreen from '../screens/DelivererEarningsScreen';
import DelivererProfileScreen from '../screens/DelivererProfileScreen';

const Tab = createBottomTabNavigator();

const DelivererTabNavigator = () => {
  return (
    <Tab.Navigator>
      <Tab.Screen name="Dashboard" component={DelivererDashboardScreen} />
      <Tab.Screen name="Livraisons" component={DelivererDeliveriesScreen} />
      <Tab.Screen name="Gains" component={DelivererEarningsScreen} />
      <Tab.Screen name="Profil" component={DelivererProfileScreen} />
    </Tab.Navigator>
  );
};

export default DelivererTabNavigator;