// =====================================================
// TYPES COMMANDES ET LIVRAISONS
// =====================================================

export type OrderStatus = 
  | 'pending'           // En attente
  | 'confirmed'         // Confirmée par le marchand
  | 'preparing'         // En préparation
  | 'ready'            // Prête pour livraison
  | 'assigned'         // Assignée à un livreur
  | 'picked_up'        // Récupérée par le livreur
  | 'in_transit'       // En cours de livraison
  | 'delivered'        // Livrée
  | 'cancelled'        // Annulée
  | 'refunded';        // Remboursée

export type DeliveryType = 'standard' | 'express' | 'scheduled';

export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  description?: string;
  image?: string;
  price: number;
  quantity: number;
  options?: OrderItemOption[];
  notes?: string;
}

export interface OrderItemOption {
  id: string;
  name: string;
  value: string;
  price: number;
}

export interface Order {
  id: string;
  clientId: string;
  merchantId: string;
  delivererId?: string;
  status: OrderStatus;
  type: DeliveryType;
  
  // Items et prix
  items: OrderItem[];
  subtotal: number;
  deliveryFee: number;
  serviceFee: number;
  tax: number;
  discount: number;
  total: number;
  
  // Adresses
  pickupAddress: {
    address: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
    instructions?: string;
  };
  deliveryAddress: {
    address: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
    instructions?: string;
  };
  
  // Timing
  createdAt: string;
  updatedAt: string;
  scheduledAt?: string;
  confirmedAt?: string;
  readyAt?: string;
  pickedUpAt?: string;
  deliveredAt?: string;
  estimatedDeliveryTime: number;
  
  // Paiement
  paymentMethod: {
    type: 'mobile_money' | 'card' | 'cash';
    provider: string;
    status: 'pending' | 'paid' | 'failed' | 'refunded';
  };
  
  // Communication
  notes?: string;
  clientNotes?: string;
  delivererNotes?: string;
  
  // Tracking
  trackingCode: string;
  deliveryProof?: {
    photo?: string;
    signature?: string;
    timestamp: string;
  };
  
  // Évaluations
  clientRating?: {
    merchant: number;
    deliverer: number;
    comment?: string;
  };
  merchantRating?: {
    client: number;
    deliverer: number;
    comment?: string;
  };
  delivererRating?: {
    client: number;
    merchant: number;
    comment?: string;
  };
}

export interface CartItem {
  productId: string;
  name: string;
  price: number;
  quantity: number;
  options?: OrderItemOption[];
  notes?: string;
  image?: string;
}

export interface Cart {
  merchantId: string;
  items: CartItem[];
  subtotal: number;
  deliveryFee: number;
  serviceFee: number;
  tax: number;
  discount: number;
  total: number;
  estimatedDeliveryTime: number;
}

export interface DeliveryTracking {
  orderId: string;
  status: OrderStatus;
  currentLocation?: {
    latitude: number;
    longitude: number;
    heading?: number;
    speed?: number;
  };
  estimatedArrival?: string;
  route?: {
    latitude: number;
    longitude: number;
  }[];
  updates: DeliveryUpdate[];
}

export interface DeliveryUpdate {
  id: string;
  status: OrderStatus;
  message: string;
  timestamp: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  photo?: string;
}

export interface OrderFilters {
  status?: OrderStatus[];
  dateFrom?: string;
  dateTo?: string;
  merchantId?: string;
  delivererId?: string;
  minAmount?: number;
  maxAmount?: number;
  type?: DeliveryType[];
}

export interface OrderStats {
  total: number;
  pending: number;
  confirmed: number;
  inTransit: number;
  delivered: number;
  cancelled: number;
  totalRevenue: number;
  averageOrderValue: number;
  averageDeliveryTime: number;
}

export interface DeliveryEarnings {
  orderId: string;
  baseAmount: number;
  distanceBonus: number;
  timeBonus: number;
  tip: number;
  total: number;
  date: string;
}

export interface OrderState {
  orders: Order[];
  currentOrder: Order | null;
  cart: Cart | null;
  tracking: DeliveryTracking | null;
  isLoading: boolean;
  error: string | null;
}

export interface OrderActions {
  // Client actions
  addToCart: (merchantId: string, item: CartItem) => void;
  removeFromCart: (productId: string) => void;
  updateCartItem: (productId: string, updates: Partial<CartItem>) => void;
  clearCart: () => void;
  placeOrder: (deliveryAddress: any, paymentMethod: any) => Promise<Order>;
  
  // Merchant actions
  confirmOrder: (orderId: string) => Promise<void>;
  updateOrderStatus: (orderId: string, status: OrderStatus) => Promise<void>;
  
  // Deliverer actions
  acceptOrder: (orderId: string) => Promise<void>;
  pickupOrder: (orderId: string) => Promise<void>;
  deliverOrder: (orderId: string, proof: any) => Promise<void>;
  
  // Common actions
  getOrders: (filters?: OrderFilters) => Promise<Order[]>;
  getOrder: (orderId: string) => Promise<Order>;
  trackOrder: (orderId: string) => Promise<DeliveryTracking>;
  rateOrder: (orderId: string, rating: any) => Promise<void>;
  cancelOrder: (orderId: string, reason: string) => Promise<void>;
  
  clearError: () => void;
}

export type OrderStore = OrderState & OrderActions;
