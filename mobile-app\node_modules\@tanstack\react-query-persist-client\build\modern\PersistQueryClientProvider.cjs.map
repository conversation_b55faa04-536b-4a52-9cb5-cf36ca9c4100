{"version": 3, "sources": ["../../src/PersistQueryClientProvider.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport {\n  persistQueryClientRestore,\n  persistQueryClientSubscribe,\n} from '@tanstack/query-persist-client-core'\nimport { IsRestoringProvider, QueryClientProvider } from '@tanstack/react-query'\nimport type { PersistQueryClientOptions } from '@tanstack/query-persist-client-core'\nimport type { OmitKeyof, QueryClientProviderProps } from '@tanstack/react-query'\n\nexport type PersistQueryClientProviderProps = QueryClientProviderProps & {\n  persistOptions: OmitKeyof<PersistQueryClientOptions, 'queryClient'>\n  onSuccess?: () => Promise<unknown> | unknown\n  onError?: () => Promise<unknown> | unknown\n}\n\nexport const PersistQueryClientProvider = ({\n  children,\n  persistOptions,\n  onSuccess,\n  onError,\n  ...props\n}: PersistQueryClientProviderProps): React.JSX.Element => {\n  const [isRestoring, setIsRestoring] = React.useState(true)\n  const refs = React.useRef({ persistOptions, onSuccess, onError })\n  const didRestore = React.useRef(false)\n\n  React.useEffect(() => {\n    refs.current = { persistOptions, onSuccess, onError }\n  })\n\n  React.useEffect(() => {\n    const options = {\n      ...refs.current.persistOptions,\n      queryClient: props.client,\n    }\n    if (!didRestore.current) {\n      didRestore.current = true\n      persistQueryClientRestore(options)\n        .then(() => refs.current.onSuccess?.())\n        .catch(() => refs.current.onError?.())\n        .finally(() => {\n          setIsRestoring(false)\n        })\n    }\n    return isRestoring ? undefined : persistQueryClientSubscribe(options)\n  }, [props.client, isRestoring])\n\n  return (\n    <QueryClientProvider {...props}>\n      <IsRestoringProvider value={isRestoring}>{children}</IsRestoringProvider>\n    </QueryClientProvider>\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,YAAuB;AAEvB,uCAGO;AACP,yBAAyD;AA4CnD;AAlCC,IAAM,6BAA6B,CAAC;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,MAA0D;AACxD,QAAM,CAAC,aAAa,cAAc,IAAU,eAAS,IAAI;AACzD,QAAM,OAAa,aAAO,EAAE,gBAAgB,WAAW,QAAQ,CAAC;AAChE,QAAM,aAAmB,aAAO,KAAK;AAErC,EAAM,gBAAU,MAAM;AACpB,SAAK,UAAU,EAAE,gBAAgB,WAAW,QAAQ;AAAA,EACtD,CAAC;AAED,EAAM,gBAAU,MAAM;AACpB,UAAM,UAAU;AAAA,MACd,GAAG,KAAK,QAAQ;AAAA,MAChB,aAAa,MAAM;AAAA,IACrB;AACA,QAAI,CAAC,WAAW,SAAS;AACvB,iBAAW,UAAU;AACrB,sEAA0B,OAAO,EAC9B,KAAK,MAAM,KAAK,QAAQ,YAAY,CAAC,EACrC,MAAM,MAAM,KAAK,QAAQ,UAAU,CAAC,EACpC,QAAQ,MAAM;AACb,uBAAe,KAAK;AAAA,MACtB,CAAC;AAAA,IACL;AACA,WAAO,cAAc,aAAY,8DAA4B,OAAO;AAAA,EACtE,GAAG,CAAC,MAAM,QAAQ,WAAW,CAAC;AAE9B,SACE,4CAAC,0CAAqB,GAAG,OACvB,sDAAC,0CAAoB,OAAO,aAAc,UAAS,GACrD;AAEJ;", "names": []}