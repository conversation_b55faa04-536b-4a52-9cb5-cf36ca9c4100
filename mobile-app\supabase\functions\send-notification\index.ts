import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface NotificationRequest {
  user_id: string
  type: string
  title: string
  body: string
  data?: Record<string, any>
  channels?: ('push' | 'sms' | 'email' | 'in_app')[]
  scheduled_for?: string
  priority?: number
}

interface PushNotificationPayload {
  to: string
  title: string
  body: string
  data?: Record<string, any>
  sound?: string
  badge?: number
  priority?: 'default' | 'high'
  ttl?: number
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const requestData: NotificationRequest = await req.json()
    
    const {
      user_id,
      type,
      title,
      body,
      data = {},
      channels = ['push'],
      scheduled_for,
      priority = 5
    } = requestData

    // Validate input
    if (!user_id || !type || !title || !body) {
      throw new Error('Missing required fields')
    }

    // Get user profile and preferences
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select(`
        *,
        countries (
          timezone,
          default_language
        )
      `)
      .eq('id', user_id)
      .single()

    if (profileError || !userProfile) {
      throw new Error('User not found')
    }

    // Get user notification preferences
    const { data: preferences } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', user_id)
      .eq('notification_type', type)
      .single()

    // Check if notifications are enabled for this type
    if (preferences && !preferences.is_enabled) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Notification disabled by user preferences',
          sent: false
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Filter channels based on user preferences
    const enabledChannels = preferences?.enabled_channels || ['push']
    const finalChannels = channels.filter(channel => enabledChannels.includes(channel))

    if (finalChannels.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No enabled channels for this notification type',
          sent: false
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Check quiet hours
    const now = new Date()
    const userTimezone = userProfile.countries?.timezone || 'UTC'
    const userTime = new Date(now.toLocaleString("en-US", { timeZone: userTimezone }))
    
    if (preferences?.quiet_hours_start && preferences?.quiet_hours_end) {
      const quietStart = parseTime(preferences.quiet_hours_start)
      const quietEnd = parseTime(preferences.quiet_hours_end)
      const currentTime = userTime.getHours() * 60 + userTime.getMinutes()
      
      const isQuietTime = quietStart <= quietEnd 
        ? (currentTime >= quietStart && currentTime <= quietEnd)
        : (currentTime >= quietStart || currentTime <= quietEnd)
      
      if (isQuietTime && priority < 8) { // Only high priority notifications during quiet hours
        // Schedule for after quiet hours
        const nextMorning = new Date(userTime)
        nextMorning.setHours(Math.floor(quietEnd / 60), quietEnd % 60, 0, 0)
        if (nextMorning <= userTime) {
          nextMorning.setDate(nextMorning.getDate() + 1)
        }
        
        scheduled_for = nextMorning.toISOString()
      }
    }

    // Get or create notification template
    const { data: template } = await supabase
      .from('notification_templates')
      .select('*')
      .eq('notification_type', type)
      .eq('target_role', userProfile.role)
      .eq('is_active', true)
      .single()

    // Localize content if template exists
    let localizedTitle = title
    let localizedBody = body
    
    if (template) {
      const userLang = userProfile.preferred_language || 'en'
      localizedTitle = template.title_translations?.[userLang] || title
      localizedBody = template.body_translations?.[userLang] || body
      
      // Replace placeholders in localized content
      localizedTitle = replacePlaceholders(localizedTitle, data)
      localizedBody = replacePlaceholders(localizedBody, data)
    }

    // Create notification record
    const { data: notification, error: notificationError } = await supabase
      .from('notifications')
      .insert({
        user_id,
        notification_type: type,
        template_id: template?.id,
        title: localizedTitle,
        body: localizedBody,
        data,
        channels: finalChannels,
        scheduled_for: scheduled_for || new Date().toISOString(),
        status: scheduled_for ? 'pending' : 'pending'
      })
      .select()
      .single()

    if (notificationError) {
      throw new Error('Failed to create notification record')
    }

    // If scheduled for later, don't send now
    if (scheduled_for && new Date(scheduled_for) > new Date()) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Notification scheduled',
          notification_id: notification.id,
          scheduled_for
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Send notifications through enabled channels
    const deliveryResults = await Promise.allSettled(
      finalChannels.map(channel => sendNotificationChannel(
        supabase,
        channel,
        userProfile,
        localizedTitle,
        localizedBody,
        data,
        notification.id
      ))
    )

    // Update notification status based on delivery results
    const hasSuccess = deliveryResults.some(result => result.status === 'fulfilled')
    const allFailed = deliveryResults.every(result => result.status === 'rejected')

    await supabase
      .from('notifications')
      .update({
        status: hasSuccess ? 'sent' : 'failed',
        sent_at: hasSuccess ? new Date().toISOString() : null,
        send_attempts: 1,
        last_error: allFailed ? 'All channels failed' : null,
        updated_at: new Date().toISOString()
      })
      .eq('id', notification.id)

    // Log delivery results
    for (let i = 0; i < finalChannels.length; i++) {
      const channel = finalChannels[i]
      const result = deliveryResults[i]
      
      await supabase
        .from('notification_delivery_logs')
        .insert({
          notification_id: notification.id,
          channel,
          status: result.status === 'fulfilled' ? 'delivered' : 'failed',
          error_message: result.status === 'rejected' ? result.reason?.toString() : null,
          attempted_at: new Date().toISOString(),
          delivered_at: result.status === 'fulfilled' ? new Date().toISOString() : null
        })
    }

    return new Response(
      JSON.stringify({
        success: hasSuccess,
        message: hasSuccess ? 'Notification sent successfully' : 'Failed to send notification',
        notification_id: notification.id,
        channels_attempted: finalChannels,
        delivery_results: deliveryResults.map((result, index) => ({
          channel: finalChannels[index],
          success: result.status === 'fulfilled',
          error: result.status === 'rejected' ? result.reason : null
        }))
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Notification error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error',
        code: 'NOTIFICATION_ERROR'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function sendNotificationChannel(
  supabase: any,
  channel: string,
  userProfile: any,
  title: string,
  body: string,
  data: Record<string, any>,
  notificationId: string
): Promise<void> {
  
  switch (channel) {
    case 'push':
      return await sendPushNotification(userProfile, title, body, data)
    
    case 'sms':
      return await sendSMSNotification(userProfile, title, body)
    
    case 'email':
      return await sendEmailNotification(userProfile, title, body, data)
    
    case 'in_app':
      // In-app notifications are handled by the database record itself
      return Promise.resolve()
    
    default:
      throw new Error(`Unsupported notification channel: ${channel}`)
  }
}

async function sendPushNotification(
  userProfile: any,
  title: string,
  body: string,
  data: Record<string, any>
): Promise<void> {
  
  // Get user's FCM tokens from user_sessions
  const { data: sessions } = await supabase
    .from('user_sessions')
    .select('device_id, metadata')
    .eq('user_id', userProfile.id)
    .eq('is_active', true)

  if (!sessions || sessions.length === 0) {
    throw new Error('No active devices found for push notification')
  }

  const fcmTokens = sessions
    .map(session => session.metadata?.fcm_token)
    .filter(token => token)

  if (fcmTokens.length === 0) {
    throw new Error('No FCM tokens found')
  }

  // Send to Firebase Cloud Messaging
  const fcmServerKey = Deno.env.get('FCM_SERVER_KEY')
  if (!fcmServerKey) {
    throw new Error('FCM server key not configured')
  }

  const payload: PushNotificationPayload = {
    to: fcmTokens[0], // Send to first token, implement multicast for multiple tokens
    title,
    body,
    data: {
      ...data,
      click_action: 'FLUTTER_NOTIFICATION_CLICK'
    },
    sound: 'default',
    priority: 'high',
    ttl: 3600 // 1 hour TTL
  }

  const response = await fetch('https://fcm.googleapis.com/fcm/send', {
    method: 'POST',
    headers: {
      'Authorization': `key=${fcmServerKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`FCM error: ${error}`)
  }

  const result = await response.json()
  if (result.failure > 0) {
    throw new Error(`FCM delivery failed: ${JSON.stringify(result.results)}`)
  }
}

async function sendSMSNotification(
  userProfile: any,
  title: string,
  body: string
): Promise<void> {
  
  if (!userProfile.phone_number) {
    throw new Error('No phone number available for SMS')
  }

  // Use Africa's Talking for African numbers
  const africasTalkingApiKey = Deno.env.get('AFRICAS_TALKING_API_KEY')
  const africasTalkingUsername = Deno.env.get('AFRICAS_TALKING_USERNAME')
  
  if (!africasTalkingApiKey || !africasTalkingUsername) {
    throw new Error('Africa\'s Talking credentials not configured')
  }

  const message = `${title}\n\n${body}`
  
  const response = await fetch('https://api.africastalking.com/version1/messaging', {
    method: 'POST',
    headers: {
      'apiKey': africasTalkingApiKey,
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json'
    },
    body: new URLSearchParams({
      username: africasTalkingUsername,
      to: userProfile.phone_number,
      message: message,
      from: 'DeliveryApp' // Your sender ID
    })
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`SMS error: ${error}`)
  }

  const result = await response.json()
  if (result.SMSMessageData.Recipients[0].status !== 'Success') {
    throw new Error(`SMS delivery failed: ${result.SMSMessageData.Recipients[0].status}`)
  }
}

async function sendEmailNotification(
  userProfile: any,
  title: string,
  body: string,
  data: Record<string, any>
): Promise<void> {
  
  // Get user email from auth.users
  const { data: authUser } = await supabase.auth.admin.getUserById(userProfile.id)
  
  if (!authUser.user?.email) {
    throw new Error('No email address available')
  }

  // Use SendGrid for email delivery
  const sendGridApiKey = Deno.env.get('SENDGRID_API_KEY')
  if (!sendGridApiKey) {
    throw new Error('SendGrid API key not configured')
  }

  const emailPayload = {
    personalizations: [{
      to: [{ email: authUser.user.email }],
      subject: title
    }],
    from: {
      email: '<EMAIL>',
      name: 'Delivery App Africa'
    },
    content: [{
      type: 'text/html',
      value: `
        <h2>${title}</h2>
        <p>${body}</p>
        ${data.order_id ? `<p><strong>Commande:</strong> ${data.order_id}</p>` : ''}
        <hr>
        <p><small>Delivery App Africa - Votre partenaire de livraison</small></p>
      `
    }]
  }

  const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${sendGridApiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(emailPayload)
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`Email error: ${error}`)
  }
}

function replacePlaceholders(text: string, data: Record<string, any>): string {
  return text.replace(/\{(\w+)\}/g, (match, key) => {
    return data[key]?.toString() || match
  })
}

function parseTime(timeString: string): number {
  const [hours, minutes] = timeString.split(':').map(Number)
  return hours * 60 + minutes
}
