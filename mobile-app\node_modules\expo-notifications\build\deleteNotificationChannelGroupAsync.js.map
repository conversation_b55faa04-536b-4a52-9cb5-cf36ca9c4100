{"version": 3, "file": "deleteNotificationChannelGroupAsync.js", "sourceRoot": "", "sources": ["../src/deleteNotificationChannelGroupAsync.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,mCAAmC,CAAC,OAAe;IAC/E,OAAO,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;AAC/E,CAAC", "sourcesContent": ["/**\n * Removes the notification channel group and all notification channels that belong to it.\n * @param groupId The channel group identifier.\n * @return A Promise which resolves once the channel group is removed (or if there was no channel group for given identifier).\n * @platform android\n * @header channels\n */\nexport default async function deleteNotificationChannelGroupAsync(groupId: string): Promise<void> {\n  console.debug('Notification channels feature is only supported on Android.');\n}\n"]}