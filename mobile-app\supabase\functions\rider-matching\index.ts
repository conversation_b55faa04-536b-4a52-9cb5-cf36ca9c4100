import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface MatchingRequest {
  order_id: string
  pickup_location: {
    latitude: number
    longitude: number
  }
  delivery_location: {
    latitude: number
    longitude: number
  }
  delivery_type: string
  estimated_value: number
  urgency_level: 'low' | 'medium' | 'high'
  special_requirements?: string[]
}

interface RiderScore {
  rider_id: string
  score: number
  distance_km: number
  estimated_arrival_minutes: number
  factors: {
    proximity: number
    availability: number
    rating: number
    experience: number
    vehicle_match: number
    zone_familiarity: number
  }
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const requestData: MatchingRequest = await req.json()
    
    const {
      order_id,
      pickup_location,
      delivery_location,
      delivery_type,
      estimated_value,
      urgency_level,
      special_requirements = []
    } = requestData

    // Validate input
    if (!order_id || !pickup_location || !delivery_location) {
      throw new Error('Missing required fields')
    }

    // Get order details to ensure it exists and is in correct status
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('*')
      .eq('id', order_id)
      .eq('status', 'confirmed')
      .single()

    if (orderError || !order) {
      throw new Error('Order not found or not ready for assignment')
    }

    // Find available riders in the area
    const searchRadius = getSearchRadius(urgency_level)
    
    const { data: availableRiders, error: ridersError } = await supabase
      .rpc('find_available_riders', {
        pickup_lat: pickup_location.latitude,
        pickup_lng: pickup_location.longitude,
        search_radius_km: searchRadius,
        delivery_type_filter: delivery_type
      })

    if (ridersError) {
      throw new Error('Failed to find available riders')
    }

    if (!availableRiders || availableRiders.length === 0) {
      // No riders available, add to queue and notify admins
      await addToRiderQueue(supabase, order_id, urgency_level)
      
      return new Response(
        JSON.stringify({
          success: false,
          message: 'No riders available at the moment',
          queued: true,
          estimated_wait_minutes: getEstimatedWaitTime(urgency_level)
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Score and rank riders
    const scoredRiders: RiderScore[] = []
    
    for (const rider of availableRiders) {
      const score = await calculateRiderScore(
        supabase,
        rider,
        pickup_location,
        delivery_location,
        delivery_type,
        estimated_value,
        urgency_level,
        special_requirements
      )
      
      if (score.score > 0) {
        scoredRiders.push(score)
      }
    }

    // Sort by score (highest first)
    scoredRiders.sort((a, b) => b.score - a.score)

    if (scoredRiders.length === 0) {
      await addToRiderQueue(supabase, order_id, urgency_level)
      
      return new Response(
        JSON.stringify({
          success: false,
          message: 'No suitable riders found',
          queued: true
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Try to assign to the best rider
    const bestRider = scoredRiders[0]
    
    const { error: assignError } = await supabase
      .from('orders')
      .update({
        delivery_rider_id: bestRider.rider_id,
        status: 'assigned',
        estimated_pickup_time: new Date(Date.now() + bestRider.estimated_arrival_minutes * 60000).toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', order_id)
      .eq('status', 'confirmed') // Ensure status hasn't changed

    if (assignError) {
      throw new Error('Failed to assign rider to order')
    }

    // Update rider status
    await supabase
      .from('delivery_riders')
      .update({
        current_status: 'busy',
        updated_at: new Date().toISOString()
      })
      .eq('id', bestRider.rider_id)

    // Create delivery tracking entry
    await supabase
      .from('delivery_tracking')
      .insert({
        order_id,
        rider_id: bestRider.rider_id,
        status: 'assigned',
        event_type: 'rider_assigned',
        notes: `Rider assigned via automatic matching. Score: ${bestRider.score}`,
        estimated_time_remaining: bestRider.estimated_arrival_minutes
      })

    // Send notifications
    await Promise.all([
      // Notify customer
      supabase.functions.invoke('send-notification', {
        body: {
          user_id: order.customer_id,
          type: 'rider_assigned',
          title: 'Livreur assigné',
          body: `Votre commande sera récupérée dans ${bestRider.estimated_arrival_minutes} minutes`,
          data: {
            order_id,
            rider_id: bestRider.rider_id,
            estimated_arrival: bestRider.estimated_arrival_minutes
          }
        }
      }),
      
      // Notify rider
      supabase.functions.invoke('send-notification', {
        body: {
          user_id: bestRider.rider_id,
          type: 'order_assigned',
          title: 'Nouvelle livraison',
          body: `Nouvelle commande assignée - ${delivery_type}`,
          data: {
            order_id,
            pickup_location,
            delivery_location,
            estimated_value
          }
        }
      })
    ])

    // Log the matching for analytics
    await supabase
      .from('rider_matching_logs')
      .insert({
        order_id,
        assigned_rider_id: bestRider.rider_id,
        total_candidates: availableRiders.length,
        matching_score: bestRider.score,
        matching_factors: bestRider.factors,
        urgency_level,
        search_radius_km: searchRadius,
        assignment_time_ms: Date.now() - new Date(order.created_at).getTime()
      })

    return new Response(
      JSON.stringify({
        success: true,
        assigned_rider: {
          rider_id: bestRider.rider_id,
          estimated_arrival_minutes: bestRider.estimated_arrival_minutes,
          distance_km: bestRider.distance_km,
          score: bestRider.score
        },
        alternatives: scoredRiders.slice(1, 3), // Top 2 alternatives
        total_candidates: availableRiders.length
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Rider matching error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error',
        code: 'MATCHING_ERROR'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function calculateRiderScore(
  supabase: any,
  rider: any,
  pickupLocation: { latitude: number, longitude: number },
  deliveryLocation: { latitude: number, longitude: number },
  deliveryType: string,
  estimatedValue: number,
  urgencyLevel: string,
  specialRequirements: string[]
): Promise<RiderScore> {
  
  // Calculate distance to pickup
  const distanceToPickup = calculateDistance(
    rider.current_latitude,
    rider.current_longitude,
    pickupLocation.latitude,
    pickupLocation.longitude
  )

  // Base factors
  const factors = {
    proximity: calculateProximityScore(distanceToPickup),
    availability: rider.current_status === 'available' ? 1.0 : 0.5,
    rating: (rider.average_rating || 3.0) / 5.0,
    experience: calculateExperienceScore(rider.total_deliveries),
    vehicle_match: calculateVehicleMatchScore(rider.vehicle_type, deliveryType, estimatedValue),
    zone_familiarity: await calculateZoneFamiliarityScore(supabase, rider.id, deliveryLocation)
  }

  // Apply urgency multipliers
  const urgencyMultipliers = {
    'high': { proximity: 2.0, availability: 1.5 },
    'medium': { proximity: 1.5, availability: 1.2 },
    'low': { proximity: 1.0, availability: 1.0 }
  }

  const multiplier = urgencyMultipliers[urgencyLevel] || urgencyMultipliers['medium']
  factors.proximity *= multiplier.proximity
  factors.availability *= multiplier.availability

  // Check special requirements
  if (specialRequirements.length > 0) {
    // Reduce score if rider doesn't meet special requirements
    // This would need to be implemented based on your specific requirements
    factors.vehicle_match *= 0.8
  }

  // Calculate weighted score
  const weights = {
    proximity: 0.3,
    availability: 0.25,
    rating: 0.2,
    experience: 0.1,
    vehicle_match: 0.1,
    zone_familiarity: 0.05
  }

  const score = Object.keys(factors).reduce((total, key) => {
    return total + (factors[key] * weights[key])
  }, 0)

  // Estimate arrival time
  const averageSpeed = getAverageSpeed(rider.vehicle_type)
  const estimatedArrivalMinutes = Math.max(5, (distanceToPickup / averageSpeed) * 60)

  return {
    rider_id: rider.id,
    score: Math.round(score * 100) / 100,
    distance_km: Math.round(distanceToPickup * 100) / 100,
    estimated_arrival_minutes: Math.round(estimatedArrivalMinutes),
    factors
  }
}

function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371
  const dLat = toRadians(lat2 - lat1)
  const dLon = toRadians(lon2 - lon1)
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2)
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}

function calculateProximityScore(distance: number): number {
  // Closer is better, max score at 0km, min score at 10km+
  return Math.max(0, 1 - (distance / 10))
}

function calculateExperienceScore(totalDeliveries: number): number {
  // Experience curve: 0-10 deliveries = 0-0.5, 10-100 = 0.5-0.8, 100+ = 0.8-1.0
  if (totalDeliveries < 10) return totalDeliveries / 20
  if (totalDeliveries < 100) return 0.5 + ((totalDeliveries - 10) / 300)
  return Math.min(1.0, 0.8 + ((totalDeliveries - 100) / 1000))
}

function calculateVehicleMatchScore(vehicleType: string, deliveryType: string, estimatedValue: number): number {
  const vehicleCapabilities = {
    'motorcycle': { speed: 1.0, capacity: 0.6, security: 0.7 },
    'bicycle': { speed: 0.6, capacity: 0.4, security: 0.8 },
    'car': { speed: 0.8, capacity: 1.0, security: 1.0 },
    'van': { speed: 0.7, capacity: 1.0, security: 1.0 },
    'walking': { speed: 0.3, capacity: 0.2, security: 0.9 }
  }

  const deliveryRequirements = {
    'food': { speed: 1.0, capacity: 0.5, security: 0.6 },
    'package': { speed: 0.7, capacity: 0.8, security: 0.9 },
    'grocery': { speed: 0.6, capacity: 1.0, security: 0.7 },
    'pharmacy': { speed: 0.8, capacity: 0.4, security: 1.0 },
    'wholesale': { speed: 0.5, capacity: 1.0, security: 0.8 },
    'documents': { speed: 0.9, capacity: 0.2, security: 1.0 }
  }

  const vehicle = vehicleCapabilities[vehicleType] || vehicleCapabilities['motorcycle']
  const requirements = deliveryRequirements[deliveryType] || deliveryRequirements['package']

  // Calculate match score
  const speedMatch = Math.min(1, vehicle.speed / requirements.speed)
  const capacityMatch = Math.min(1, vehicle.capacity / requirements.capacity)
  const securityMatch = Math.min(1, vehicle.security / requirements.security)

  // High-value orders need better security
  const securityWeight = estimatedValue > 50000 ? 0.5 : 0.2
  const speedWeight = 0.5 - securityWeight
  const capacityWeight = 0.3

  return (speedMatch * speedWeight) + (capacityMatch * capacityWeight) + (securityMatch * securityWeight)
}

async function calculateZoneFamiliarityScore(supabase: any, riderId: string, deliveryLocation: any): Promise<number> {
  // Get rider's delivery history in this zone
  const { data: deliveryHistory } = await supabase
    .rpc('get_rider_zone_experience', {
      rider_id: riderId,
      delivery_lat: deliveryLocation.latitude,
      delivery_lng: deliveryLocation.longitude,
      radius_km: 2
    })

  if (!deliveryHistory || deliveryHistory.length === 0) return 0.5

  const totalDeliveries = deliveryHistory[0].delivery_count || 0
  const successRate = deliveryHistory[0].success_rate || 0.5

  // Combine delivery count and success rate
  const experienceScore = Math.min(1.0, totalDeliveries / 20) // Max score at 20 deliveries
  const performanceScore = successRate

  return (experienceScore * 0.6) + (performanceScore * 0.4)
}

function getAverageSpeed(vehicleType: string): number {
  const speeds = {
    'motorcycle': 25, // km/h in city traffic
    'bicycle': 15,
    'car': 20,
    'van': 18,
    'walking': 5
  }
  return speeds[vehicleType] || 20
}

function getSearchRadius(urgencyLevel: string): number {
  const radii = {
    'high': 3,   // 3km for urgent orders
    'medium': 5, // 5km for normal orders
    'low': 8     // 8km for low priority
  }
  return radii[urgencyLevel] || 5
}

function getEstimatedWaitTime(urgencyLevel: string): number {
  const waitTimes = {
    'high': 10,   // 10 minutes for urgent
    'medium': 20, // 20 minutes for normal
    'low': 30     // 30 minutes for low priority
  }
  return waitTimes[urgencyLevel] || 20
}

async function addToRiderQueue(supabase: any, orderId: string, urgencyLevel: string) {
  await supabase
    .from('rider_assignment_queue')
    .insert({
      order_id: orderId,
      urgency_level,
      created_at: new Date().toISOString(),
      retry_count: 0,
      next_retry_at: new Date(Date.now() + 5 * 60000).toISOString() // Retry in 5 minutes
    })
}
