// =====================================================
// COMPOSANT INPUT RÉUTILISABLE
// =====================================================

import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  ViewStyle,
  TextInputProps,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  variant?: 'default' | 'filled' | 'outline';
  size?: 'small' | 'medium' | 'large';
  required?: boolean;
  containerStyle?: ViewStyle;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'outline',
  size = 'medium',
  required = false,
  containerStyle,
  secureTextEntry,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const isPassword = secureTextEntry;
  const actualSecureTextEntry = isPassword && !isPasswordVisible;

  const containerStyles = [
    styles.container,
    containerStyle,
  ];

  const inputContainerStyles = [
    styles.inputContainer,
    styles[variant],
    styles[size],
    isFocused && styles.focused,
    error && styles.error,
  ];

  const inputStyles = [
    styles.input,
    styles[`${size}Input`],
    leftIcon && styles.inputWithLeftIcon,
    (rightIcon || isPassword) && styles.inputWithRightIcon,
  ];

  const handleRightIconPress = () => {
    if (isPassword) {
      setIsPasswordVisible(!isPasswordVisible);
    } else if (onRightIconPress) {
      onRightIconPress();
    }
  };

  const getRightIcon = () => {
    if (isPassword) {
      return isPasswordVisible ? 'eye-off' : 'eye';
    }
    return rightIcon;
  };

  return (
    <View style={containerStyles}>
      {label && (
        <Text style={styles.label}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <View style={inputContainerStyles}>
        {leftIcon && (
          <Ionicons
            name={leftIcon}
            size={20}
            color={isFocused ? '#007AFF' : '#8E8E93'}
            style={styles.leftIcon}
          />
        )}
        
        <TextInput
          style={inputStyles}
          secureTextEntry={actualSecureTextEntry}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholderTextColor="#8E8E93"
          {...textInputProps}
        />
        
        {(rightIcon || isPassword) && (
          <TouchableOpacity
            onPress={handleRightIconPress}
            style={styles.rightIconContainer}
            disabled={!isPassword && !onRightIconPress}
          >
            <Ionicons
              name={getRightIcon()!}
              size={20}
              color={isFocused ? '#007AFF' : '#8E8E93'}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}
      
      {hint && !error && (
        <Text style={styles.hintText}>{hint}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  
  required: {
    color: '#FF3B30',
  },
  
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
  },
  
  // Variants
  default: {
    backgroundColor: '#F2F2F7',
    borderColor: 'transparent',
  },
  
  filled: {
    backgroundColor: '#F2F2F7',
    borderColor: 'transparent',
  },
  
  outline: {
    backgroundColor: '#FFFFFF',
    borderColor: '#D1D1D6',
  },
  
  // Sizes
  small: {
    minHeight: 36,
    paddingHorizontal: 12,
  },
  
  medium: {
    minHeight: 44,
    paddingHorizontal: 16,
  },
  
  large: {
    minHeight: 52,
    paddingHorizontal: 20,
  },
  
  // States
  focused: {
    borderColor: '#007AFF',
    borderWidth: 2,
  },
  
  error: {
    borderColor: '#FF3B30',
    borderWidth: 2,
  },
  
  // Input
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1C1C1E',
  },
  
  smallInput: {
    fontSize: 14,
  },
  
  mediumInput: {
    fontSize: 16,
  },
  
  largeInput: {
    fontSize: 18,
  },
  
  inputWithLeftIcon: {
    marginLeft: 8,
  },
  
  inputWithRightIcon: {
    marginRight: 8,
  },
  
  // Icons
  leftIcon: {
    marginRight: 8,
  },
  
  rightIconContainer: {
    padding: 4,
  },
  
  // Helper texts
  errorText: {
    fontSize: 14,
    color: '#FF3B30',
    marginTop: 4,
  },
  
  hintText: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 4,
  },
});

export default Input;
