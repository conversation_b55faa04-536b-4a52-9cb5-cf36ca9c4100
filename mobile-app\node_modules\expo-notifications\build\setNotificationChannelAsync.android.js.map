{"version": 3, "file": "setNotificationChannelAsync.android.js", "sourceRoot": "", "sources": ["../src/setNotificationChannelAsync.android.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,0BAA0B,MAAM,8BAA8B,CAAC;AAGtE,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,2BAA2B,CACvD,SAAiB,EACjB,OAAiC;IAEjC,IAAI,CAAC,0BAA0B,CAAC,2BAA2B,EAAE,CAAC;QAC5D,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,6BAA6B,CAAC,CAAC;IAChF,CAAC;IAED,OAAO,MAAM,0BAA0B,CAAC,2BAA2B,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC1F,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationChannelManager from './NotificationChannelManager';\nimport { NotificationChannelInput, NotificationChannel } from './NotificationChannelManager.types';\n\nexport default async function setNotificationChannelAsync(\n  channelId: string,\n  channel: NotificationChannelInput\n): Promise<NotificationChannel | null> {\n  if (!NotificationChannelManager.setNotificationChannelAsync) {\n    throw new UnavailabilityError('Notifications', 'setNotificationChannelAsync');\n  }\n\n  return await NotificationChannelManager.setNotificationChannelAsync(channelId, channel);\n}\n"]}