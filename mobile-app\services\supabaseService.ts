// =====================================================
// SUPABASE DATABASE SERVICE - AFRICAN DELIVERY APP
// =====================================================

import { supabase } from '../lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Types for database entities
export interface User {
  id: string;
  email?: string;
  phone: string;
  phone_verified: boolean;
  role: 'client' | 'merchant' | 'deliverer' | 'admin';
  status: string;
  preferred_language: string;
  country_id?: string;
  city_id?: string;
  created_at: string;
  updated_at: string;
  contact_info?: any;
}

export interface Category {
  id: string;
  name: any; // JSONB field for multilingual names
  description?: any; // JSONB field for multilingual descriptions
  icon: string;
  image_url?: string;
  is_active: boolean;
  sort_order: number;
  parent_id?: string;
  created_at: string;
  updated_at: string;
}

export interface MerchantProfile {
  id?: string;
  user_id: string;
  business_name: string;
  business_type: string;
  description?: any; // JSONB field
  address: string;
  coordinates?: any; // PostGIS point
  phone: string;
  email?: string;
  logo_url?: string;
  cover_image_url?: string;
  is_open: boolean;
  is_verified: boolean;
  rating: number;
  total_orders: number;
  delivery_fee: number;
  minimum_order_amount: number;
  average_preparation_time: number;
  opening_hours?: any; // JSONB field
  delivery_zone_id?: string;
  commission_rate: number;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  merchant_id: string;
  category_id: string;
  name: any; // JSONB field
  description?: any; // JSONB field
  price: number;
  image_url?: string;
  is_available: boolean;
  is_featured: boolean;
  preparation_time: number;
  ingredients?: any; // JSONB field
  nutritional_info?: any; // JSONB field
  allergens?: string[];
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  client_id: string;
  merchant_id: string;
  delivery_person_id?: string;
  status: string;
  type: string;
  subtotal: number;
  delivery_fee: number;
  service_fee: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  pickup_address: any; // JSONB field
  delivery_address: any; // JSONB field
  scheduled_at?: string;
  estimated_delivery_time: number;
  payment_method: string;
  payment_status: string;
  notes?: string;
  tracking_code: string;
  created_at: string;
  updated_at: string;
}

class SupabaseService {
  private static instance: SupabaseService;
  private currentUser: User | null = null;

  static getInstance(): SupabaseService {
    if (!SupabaseService.instance) {
      SupabaseService.instance = new SupabaseService();
    }
    return SupabaseService.instance;
  }

  // =====================================================
  // AUTHENTICATION METHODS
  // =====================================================

  async signUp(phone: string, password: string, userData: Partial<User> = {}) {
    try {
      // For African markets, we primarily use phone-based authentication
      const { data, error } = await supabase.auth.signUp({
        phone: phone,
        password: password,
      });

      if (error) throw error;

      if (data.user) {
        // Create user profile in our custom users table
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            phone: phone,
            role: userData.role || 'client',
            preferred_language: userData.preferred_language || 'fr',
            contact_info: userData.contact_info || {},
          });

        if (profileError) throw profileError;
      }

      return { user: data.user, session: data.session };
    } catch (error) {
      console.error('❌ Signup error:', error);
      throw error;
    }
  }

  async signIn(phone: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        phone: phone,
        password: password,
      });

      if (error) throw error;

      if (data.user) {
        // Fetch user profile
        await this.fetchCurrentUser();
      }

      return { user: data.user, session: data.session };
    } catch (error) {
      console.error('❌ Signin error:', error);
      throw error;
    }
  }

  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      this.currentUser = null;
      await AsyncStorage.removeItem('user_session');
    } catch (error) {
      console.error('❌ Signout error:', error);
      throw error;
    }
  }

  async fetchCurrentUser(): Promise<User | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        this.currentUser = null;
        return null;
      }

      const { data: profile, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) throw error;

      this.currentUser = profile;
      return profile;
    } catch (error) {
      console.error('❌ Fetch user error:', error);
      return null;
    }
  }

  getCurrentUser(): User | null {
    return this.currentUser;
  }

  // =====================================================
  // CATEGORIES METHODS
  // =====================================================

  async getCategories(): Promise<Category[]> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('❌ Get categories error:', error);
      return [];
    }
  }

  // =====================================================
  // MERCHANTS METHODS
  // =====================================================

  async getNearbyMerchants(latitude: number, longitude: number, radius: number = 10): Promise<MerchantProfile[]> {
    try {
      // Using PostGIS for geospatial queries
      const { data, error } = await supabase
        .rpc('get_nearby_merchants', {
          lat: latitude,
          lng: longitude,
          radius_km: radius
        });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('❌ Get nearby merchants error:', error);
      // Fallback to all merchants if geospatial query fails
      return this.getAllMerchants();
    }
  }

  async getAllMerchants(): Promise<MerchantProfile[]> {
    try {
      const { data, error } = await supabase
        .from('merchant_profiles')
        .select('*')
        .eq('is_verified', true)
        .order('rating', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('❌ Get all merchants error:', error);
      return [];
    }
  }

  async getMerchantById(merchantId: string): Promise<MerchantProfile | null> {
    try {
      const { data, error } = await supabase
        .from('merchant_profiles')
        .select('*')
        .eq('user_id', merchantId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('❌ Get merchant by ID error:', error);
      return null;
    }
  }

  async getMerchantsByCategory(categoryId: string): Promise<MerchantProfile[]> {
    try {
      // Get merchants that have products in this category
      const { data, error } = await supabase
        .from('merchant_profiles')
        .select(`
          *,
          products!inner(category_id)
        `)
        .eq('products.category_id', categoryId)
        .eq('is_verified', true);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('❌ Get merchants by category error:', error);
      return [];
    }
  }

  // =====================================================
  // PRODUCTS METHODS
  // =====================================================

  async getProductsByMerchant(merchantId: string): Promise<Product[]> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('merchant_id', merchantId)
        .eq('is_available', true)
        .order('is_featured', { ascending: false })
        .order('name', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('❌ Get products by merchant error:', error);
      return [];
    }
  }

  async getFeaturedProducts(): Promise<Product[]> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          merchant_profiles!inner(is_verified, is_open)
        `)
        .eq('is_featured', true)
        .eq('is_available', true)
        .eq('merchant_profiles.is_verified', true)
        .eq('merchant_profiles.is_open', true)
        .limit(10);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('❌ Get featured products error:', error);
      return [];
    }
  }

  // =====================================================
  // ORDERS METHODS
  // =====================================================

  async createOrder(orderData: Partial<Order>): Promise<Order | null> {
    try {
      const currentUser = this.getCurrentUser();
      if (!currentUser) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('orders')
        .insert({
          ...orderData,
          client_id: currentUser.id,
          tracking_code: `TRK${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`,
          status: 'pending',
          payment_status: 'pending',
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('❌ Create order error:', error);
      return null;
    }
  }

  async getOrdersByUser(userId?: string): Promise<Order[]> {
    try {
      const targetUserId = userId || this.getCurrentUser()?.id;
      if (!targetUserId) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('client_id', targetUserId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('❌ Get orders by user error:', error);
      return [];
    }
  }

  async getOrderById(orderId: string): Promise<Order | null> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('❌ Get order by ID error:', error);
      return null;
    }
  }

  async updateOrderStatus(orderId: string, status: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('orders')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('❌ Update order status error:', error);
      return false;
    }
  }

  async cancelOrder(orderId: string, reason: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('orders')
        .update({
          status: 'cancelled',
          notes: reason,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('❌ Cancel order error:', error);
      return false;
    }
  }

  // =====================================================
  // PROMOTIONS AND COUPONS
  // =====================================================

  async getActivePromotions(): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('coupons')
        .select('*')
        .eq('is_active', true)
        .gte('valid_until', new Date().toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('❌ Get active promotions error:', error);
      return [];
    }
  }

  // =====================================================
  // OFFLINE SUPPORT METHODS
  // =====================================================

  async cacheData(key: string, data: any): Promise<void> {
    try {
      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify({
        data,
        timestamp: Date.now(),
      }));
    } catch (error) {
      console.error('❌ Cache data error:', error);
    }
  }

  async getCachedData(key: string, maxAge: number = 300000): Promise<any> {
    try {
      const cached = await AsyncStorage.getItem(`cache_${key}`);
      if (!cached) return null;

      const { data, timestamp } = JSON.parse(cached);
      if (Date.now() - timestamp > maxAge) {
        await AsyncStorage.removeItem(`cache_${key}`);
        return null;
      }

      return data;
    } catch (error) {
      console.error('❌ Get cached data error:', error);
      return null;
    }
  }

  async clearCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('❌ Clear cache error:', error);
    }
  }
}

export const supabaseService = SupabaseService.getInstance();
export default supabaseService;
