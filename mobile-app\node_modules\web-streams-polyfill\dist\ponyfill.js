/**
 * @license
 * web-streams-polyfill v4.1.0
 * Copyright 2024 <PERSON>, <PERSON><PERSON><PERSON> and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).WebStreamsPolyfill={})}(this,(function(e){"use strict";function t(){}function r(e){return"object"==typeof e&&null!==e||"function"==typeof e}const o=t;function n(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}const a=Promise,i=Promise.resolve.bind(a),l=Promise.prototype.then,s=Promise.reject.bind(a),u=i;function c(e){return new a(e)}function d(e){return c((t=>t(e)))}function f(e){return s(e)}function b(e,t,r){return l.call(e,t,r)}function h(e,t,r){b(b(e,t,r),void 0,o)}function m(e,t){h(e,t)}function _(e,t){h(e,void 0,t)}function p(e,t,r){return b(e,t,r)}function y(e){b(e,void 0,o)}let S=e=>{if("function"==typeof queueMicrotask)S=queueMicrotask;else{const e=d(void 0);S=t=>b(e,t)}return S(e)};function g(e,t,r){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function v(e,t,r){try{return d(g(e,t,r))}catch(e){return f(e)}}class w{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let o=r+1;const n=e._elements,a=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,a}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(r=r._next,o=r._elements,t=0,0===o.length));)e(o[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}const R=Symbol("[[AbortSteps]]"),T=Symbol("[[ErrorSteps]]"),C=Symbol("[[CancelSteps]]"),P=Symbol("[[PullSteps]]"),q=Symbol("[[ReleaseSteps]]");function E(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?j(e):"closed"===t._state?function(e){j(e),z(e)}(e):k(e,t._storedError)}function W(e,t){return Or(e._ownerReadableStream,t)}function B(e){const t=e._ownerReadableStream;"readable"===t._state?A(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,t){k(e,t)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),t._readableStreamController[q](),t._reader=void 0,e._ownerReadableStream=void 0}function O(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function j(e){e._closedPromise=c(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function k(e,t){j(e),A(e,t)}function A(e,t){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function z(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const D=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},L=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function F(e,t){if(void 0!==e&&("object"!=typeof(r=e)&&"function"!=typeof r))throw new TypeError(`${t} is not an object.`);var r}function I(e,t){if("function"!=typeof e)throw new TypeError(`${t} is not a function.`)}function $(e,t){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function M(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function Y(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function x(e){return Number(e)}function Q(e){return 0===e?0:e}function N(e,t){const r=Number.MAX_SAFE_INTEGER;let o=Number(e);if(o=Q(o),!D(o))throw new TypeError(`${t} is not a finite number`);if(o=function(e){return Q(L(e))}(o),o<0||o>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return D(o)&&0!==o?o:0}function H(e,t){if(!Wr(e))throw new TypeError(`${t} is not a ReadableStream.`)}function V(e){return new ReadableStreamDefaultReader(e)}function U(e,t){e._reader._readRequests.push(t)}function G(e,t,r){const o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function X(e){return e._reader._readRequests.length}function J(e){const t=e._reader;return void 0!==t&&!!K(t)}class ReadableStreamDefaultReader{constructor(e){if(M(e,1,"ReadableStreamDefaultReader"),H(e,"First parameter"),Br(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");E(this,e),this._readRequests=new w}get closed(){return K(this)?this._closedPromise:f(te("closed"))}cancel(e=void 0){return K(this)?void 0===this._ownerReadableStream?f(O("cancel")):W(this,e):f(te("cancel"))}read(){if(!K(this))return f(te("read"));if(void 0===this._ownerReadableStream)return f(O("read from"));let e,t;const r=c(((r,o)=>{e=r,t=o}));return Z(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!K(this))throw te("releaseLock");void 0!==this._ownerReadableStream&&function(e){B(e);const t=new TypeError("Reader was released");ee(e,t)}(this)}}function K(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof ReadableStreamDefaultReader)}function Z(e,t){const r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[P](t)}function ee(e,t){const r=e._readRequests;e._readRequests=new w,r.forEach((e=>{e._errorSteps(t)}))}function te(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}var re,oe,ne;function ae(e){return e.slice()}function ie(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}Object.defineProperties(ReadableStreamDefaultReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(ReadableStreamDefaultReader.prototype.cancel,"cancel"),n(ReadableStreamDefaultReader.prototype.read,"read"),n(ReadableStreamDefaultReader.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamDefaultReader.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let le=e=>(le="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e,le(e)),se=e=>(se="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength,se(e));function ue(e,t,r){if(e.slice)return e.slice(t,r);const o=r-t,n=new ArrayBuffer(o);return ie(n,0,e,t,o),n}function ce(e,t){const r=e[t];if(null!=r){if("function"!=typeof r)throw new TypeError(`${String(t)} is not a function`);return r}}function de(e){try{const t=e.done,r=e.value;return b(u(r),(e=>({done:t,value:e})))}catch(e){return f(e)}}const fe=null!==(ne=null!==(re=Symbol.asyncIterator)&&void 0!==re?re:null===(oe=Symbol.for)||void 0===oe?void 0:oe.call(Symbol,"Symbol.asyncIterator"))&&void 0!==ne?ne:"@@asyncIterator";function be(e,t="sync",o){if(void 0===o)if("async"===t){if(void 0===(o=ce(e,fe))){return function(e){const t={next(){let t;try{t=he(e)}catch(e){return f(e)}return de(t)},return(t){let o;try{const r=ce(e.iterator,"return");if(void 0===r)return d({done:!0,value:t});o=g(r,e.iterator,[t])}catch(e){return f(e)}return r(o)?de(o):f(new TypeError("The iterator.return() method must return an object"))}};return{iterator:t,nextMethod:t.next,done:!1}}(be(e,"sync",ce(e,Symbol.iterator)))}}else o=ce(e,Symbol.iterator);if(void 0===o)throw new TypeError("The object is not iterable");const n=g(o,e,[]);if(!r(n))throw new TypeError("The iterator method must return an object");return{iterator:n,nextMethod:n.next,done:!1}}function he(e){const t=g(e.nextMethod,e.iterator,[]);if(!r(t))throw new TypeError("The iterator.next() method must return an object");return t}class me{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?p(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise=this._ongoingPromise?p(this._ongoingPromise,t,t):t(),this._ongoingPromise}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;let t,r;const o=c(((e,o)=>{t=e,r=o}));return Z(e,{_chunkSteps:e=>{this._ongoingPromise=void 0,S((()=>t({value:e,done:!1})))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,B(e),t({value:void 0,done:!0})},_errorSteps:t=>{this._ongoingPromise=void 0,this._isFinished=!0,B(e),r(t)}}),o}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(!this._preventCancel){const r=W(t,e);return B(t),p(r,(()=>({value:e,done:!0})))}return B(t),d({value:e,done:!0})}}const _e={next(){return pe(this)?this._asyncIteratorImpl.next():f(ye("next"))},return(e){return pe(this)?this._asyncIteratorImpl.return(e):f(ye("return"))},[fe](){return this}};function pe(e){if(!r(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof me}catch(e){return!1}}function ye(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.defineProperty(_e,fe,{enumerable:!1});const Se=Number.isNaN||function(e){return e!=e};function ge(e){const t=ue(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function ve(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function we(e,t,r){if("number"!=typeof(o=r)||Se(o)||o<0||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var o;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function Re(e){e._queue=new w,e._queueTotalSize=0}function Te(e){return e===DataView}class ReadableStreamBYOBRequest{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!Pe(this))throw Ze("view");return this._view}respond(e){if(!Pe(this))throw Ze("respond");if(M(e,1,"respond"),e=N(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(se(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");Xe(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!Pe(this))throw Ze("respondWithNewView");if(M(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(se(e.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");Je(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ReadableStreamBYOBRequest.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),n(ReadableStreamBYOBRequest.prototype.respond,"respond"),n(ReadableStreamBYOBRequest.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamBYOBRequest.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class ReadableByteStreamController{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!Ce(this))throw et("byobRequest");return Ue(this)}get desiredSize(){if(!Ce(this))throw et("desiredSize");return Ge(this)}close(){if(!Ce(this))throw et("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);Qe(this)}enqueue(e){if(!Ce(this))throw et("enqueue");if(M(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const t=this._controlledReadableByteStream._state;if("readable"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);Ne(this,e)}error(e=void 0){if(!Ce(this))throw et("error");He(this,e)}[C](e){Ee(this),Re(this);const t=this._cancelAlgorithm(e);return xe(this),t}[P](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void Ve(this,e);const r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}U(t,e),qe(this)}[q](){if(this._pendingPullIntos.length>0){const e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new w,this._pendingPullIntos.push(e)}}}function Ce(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof ReadableByteStreamController)}function Pe(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof ReadableStreamBYOBRequest)}function qe(e){const t=function(e){const t=e._controlledReadableByteStream;if("readable"!==t._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(J(t)&&X(t)>0)return!0;if(at(t)&&nt(t)>0)return!0;const r=Ge(e);if(r>0)return!0;return!1}(e);if(!t)return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;h(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,qe(e)),null)),(t=>(He(e,t),null)))}function Ee(e){Fe(e),e._pendingPullIntos=new w}function We(e,t){let r=!1;"closed"===e._state&&(r=!0);const o=Oe(t);"default"===t.readerType?G(e,o,r):function(e,t,r){const o=e._reader,n=o._readIntoRequests.shift();r?n._closeSteps(t):n._chunkSteps(t)}(e,o,r)}function Be(e,t){for(let r=0;r<t.length;++r)We(e,t[r])}function Oe(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function je(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function ke(e,t,r,o){let n;try{n=ue(t,r,r+o)}catch(t){throw He(e,t),t}je(e,n,0,o)}function Ae(e,t){t.bytesFilled>0&&ke(e,t.buffer,t.byteOffset,t.bytesFilled),Ye(e)}function ze(e,t){const r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),o=t.bytesFilled+r;let n=r,a=!1;const i=o-o%t.elementSize;i>=t.minimumFill&&(n=i-t.bytesFilled,a=!0);const l=e._queue;for(;n>0;){const r=l.peek(),o=Math.min(n,r.byteLength),a=t.byteOffset+t.bytesFilled;ie(t.buffer,a,r.buffer,r.byteOffset,o),r.byteLength===o?l.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,De(e,o,t),n-=o}return a}function De(e,t,r){r.bytesFilled+=t}function Le(e){0===e._queueTotalSize&&e._closeRequested?(xe(e),jr(e._controlledReadableByteStream)):qe(e)}function Fe(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Ie(e){const t=[];for(;e._pendingPullIntos.length>0&&0!==e._queueTotalSize;){const r=e._pendingPullIntos.peek();ze(e,r)&&(Ye(e),t.push(r))}return t}function $e(e,t,r,o){const n=e._controlledReadableByteStream,a=t.constructor,i=function(e){return Te(e)?1:e.BYTES_PER_ELEMENT}(a),{byteOffset:l,byteLength:s}=t,u=r*i;let c;try{c=le(t.buffer)}catch(e){return void o._errorSteps(e)}const d={buffer:c,bufferByteLength:c.byteLength,byteOffset:l,byteLength:s,bytesFilled:0,minimumFill:u,elementSize:i,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(d),void ot(n,o);if("closed"!==n._state){if(e._queueTotalSize>0){if(ze(e,d)){const t=Oe(d);return Le(e),void o._chunkSteps(t)}if(e._closeRequested){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");return He(e,t),void o._errorSteps(t)}}e._pendingPullIntos.push(d),ot(n,o),qe(e)}else{const e=new a(d.buffer,d.byteOffset,0);o._closeSteps(e)}}function Me(e,t){const r=e._pendingPullIntos.peek();Fe(e);"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&Ye(e);const r=e._controlledReadableByteStream;if(at(r)){const t=[];for(let o=0;o<nt(r);++o)t.push(Ye(e));Be(r,t)}}(e,r):function(e,t,r){if(De(0,t,r),"none"===r.readerType){Ae(e,r);const t=Ie(e);return void Be(e._controlledReadableByteStream,t)}if(r.bytesFilled<r.minimumFill)return;Ye(e);const o=r.bytesFilled%r.elementSize;if(o>0){const t=r.byteOffset+r.bytesFilled;ke(e,r.buffer,t-o,o)}r.bytesFilled-=o;const n=Ie(e);We(e._controlledReadableByteStream,r),Be(e._controlledReadableByteStream,n)}(e,t,r),qe(e)}function Ye(e){return e._pendingPullIntos.shift()}function xe(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Qe(e){const t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw He(e,t),t}}xe(e),jr(t)}}function Ne(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;const{buffer:o,byteOffset:n,byteLength:a}=t;if(se(o))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");const i=le(o);if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();if(se(t.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");Fe(e),t.buffer=le(t.buffer),"none"===t.readerType&&Ae(e,t)}if(J(r))if(function(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;Ve(e,t._readRequests.shift())}}(e),0===X(r))je(e,i,n,a);else{e._pendingPullIntos.length>0&&Ye(e);G(r,new Uint8Array(i,n,a),!1)}else if(at(r)){je(e,i,n,a);const t=Ie(e);Be(e._controlledReadableByteStream,t)}else je(e,i,n,a);qe(e)}function He(e,t){const r=e._controlledReadableByteStream;"readable"===r._state&&(Ee(e),Re(e),xe(e),kr(r,t))}function Ve(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,Le(e);const o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function Ue(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(ReadableStreamBYOBRequest.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(o,e,r),e._byobRequest=o}return e._byobRequest}function Ge(e){const t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function Xe(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=le(r.buffer),Me(e,t)}function Je(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const o=t.byteLength;r.buffer=le(t.buffer),Me(e,o)}function Ke(e,t,r,o,n,a,i){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,Re(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=i,t._pendingPullIntos=new w,e._readableStreamController=t;h(d(r()),(()=>(t._started=!0,qe(t),null)),(e=>(He(t,e),null)))}function Ze(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function et(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function tt(e,t){if("byob"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function rt(e){return new ReadableStreamBYOBReader(e)}function ot(e,t){e._reader._readIntoRequests.push(t)}function nt(e){return e._reader._readIntoRequests.length}function at(e){const t=e._reader;return void 0!==t&&!!it(t)}Object.defineProperties(ReadableByteStreamController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ReadableByteStreamController.prototype.close,"close"),n(ReadableByteStreamController.prototype.enqueue,"enqueue"),n(ReadableByteStreamController.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableByteStreamController.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class ReadableStreamBYOBReader{constructor(e){if(M(e,1,"ReadableStreamBYOBReader"),H(e,"First parameter"),Br(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Ce(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");E(this,e),this._readIntoRequests=new w}get closed(){return it(this)?this._closedPromise:f(ut("closed"))}cancel(e=void 0){return it(this)?void 0===this._ownerReadableStream?f(O("cancel")):W(this,e):f(ut("cancel"))}read(e,t={}){if(!it(this))return f(ut("read"));if(!ArrayBuffer.isView(e))return f(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return f(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return f(new TypeError("view's buffer must have non-zero byteLength"));if(se(e.buffer))return f(new TypeError("view's buffer has been detached"));let r;try{r=function(e,t){var r;return F(e,t),{min:N(null!==(r=null==e?void 0:e.min)&&void 0!==r?r:1,`${t} has member 'min' that`)}}(t,"options")}catch(e){return f(e)}const o=r.min;if(0===o)return f(new TypeError("options.min must be greater than 0"));if(function(e){return Te(e.constructor)}(e)){if(o>e.byteLength)return f(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(o>e.length)return f(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return f(O("read from"));let n,a;const i=c(((e,t)=>{n=e,a=t}));return lt(this,e,o,{_chunkSteps:e=>n({value:e,done:!1}),_closeSteps:e=>n({value:e,done:!0}),_errorSteps:e=>a(e)}),i}releaseLock(){if(!it(this))throw ut("releaseLock");void 0!==this._ownerReadableStream&&function(e){B(e);const t=new TypeError("Reader was released");st(e,t)}(this)}}function it(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof ReadableStreamBYOBReader)}function lt(e,t,r,o){const n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?o._errorSteps(n._storedError):$e(n._readableStreamController,t,r,o)}function st(e,t){const r=e._readIntoRequests;e._readIntoRequests=new w,r.forEach((e=>{e._errorSteps(t)}))}function ut(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function ct(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(Se(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}function dt(e){const{size:t}=e;return t||(()=>1)}function ft(e,t){F(e,t);const r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:x(r),size:void 0===o?void 0:bt(o,`${t} has member 'size' that`)}}function bt(e,t){return I(e,t),t=>x(e(t))}function ht(e,t,r){return I(e,r),r=>v(e,t,[r])}function mt(e,t,r){return I(e,r),()=>v(e,t,[])}function _t(e,t,r){return I(e,r),r=>g(e,t,[r])}function pt(e,t,r){return I(e,r),(r,o)=>v(e,t,[r,o])}function yt(e,t){if(!vt(e))throw new TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(ReadableStreamBYOBReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(ReadableStreamBYOBReader.prototype.cancel,"cancel"),n(ReadableStreamBYOBReader.prototype.read,"read"),n(ReadableStreamBYOBReader.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamBYOBReader.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});class WritableStream{constructor(e={},t={}){void 0===e?e=null:$(e,"First parameter");const r=ft(t,"Second parameter"),o=function(e,t){F(e,t);const r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===r?void 0:ht(r,e,`${t} has member 'abort' that`),close:void 0===o?void 0:mt(o,e,`${t} has member 'close' that`),start:void 0===n?void 0:_t(n,e,`${t} has member 'start' that`),write:void 0===i?void 0:pt(i,e,`${t} has member 'write' that`),type:a}}(e,"First parameter");gt(this);if(void 0!==o.type)throw new RangeError("Invalid type is specified");const n=dt(r);!function(e,t,r,o){const n=Object.create(WritableStreamDefaultController.prototype);let a,i,l,s;a=void 0!==t.start?()=>t.start(n):()=>{};i=void 0!==t.write?e=>t.write(e,n):()=>d(void 0);l=void 0!==t.close?()=>t.close():()=>d(void 0);s=void 0!==t.abort?e=>t.abort(e):()=>d(void 0);It(e,n,a,i,l,s,r,o)}(this,o,ct(r,1),n)}get locked(){if(!vt(this))throw Ht("locked");return wt(this)}abort(e=void 0){return vt(this)?wt(this)?f(new TypeError("Cannot abort a stream that already has a writer")):Rt(this,e):f(Ht("abort"))}close(){return vt(this)?wt(this)?f(new TypeError("Cannot close a stream that already has a writer")):Et(this)?f(new TypeError("Cannot close an already-closing stream")):Tt(this):f(Ht("close"))}getWriter(){if(!vt(this))throw Ht("getWriter");return St(this)}}function St(e){return new WritableStreamDefaultWriter(e)}function gt(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new w,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function vt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof WritableStream)}function wt(e){return void 0!==e._writer}function Rt(e,t){var r;if("closed"===e._state||"errored"===e._state)return d(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);const o=e._state;if("closed"===o||"errored"===o)return d(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;"erroring"===o&&(n=!0,t=void 0);const a=c(((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=a,n||Pt(e,t),a}function Tt(e){const t=e._state;if("closed"===t||"errored"===t)return f(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=c(((t,r)=>{const o={_resolve:t,_reject:r};e._closeRequest=o})),o=e._writer;var n;return void 0!==o&&e._backpressure&&"writable"===t&&nr(o),we(n=e._writableStreamController,Lt,0),Yt(n),r}function Ct(e,t){"writable"!==e._state?qt(e):Pt(e,t)}function Pt(e,t){const r=e._writableStreamController;e._state="erroring",e._storedError=t;const o=e._writer;void 0!==o&&At(o,t),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&r._started&&qt(e)}function qt(e){e._state="errored",e._writableStreamController[T]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new w,void 0===e._pendingAbortRequest)return void Wt(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void Wt(e);h(e._writableStreamController[R](r._reason),(()=>(r._resolve(),Wt(e),null)),(t=>(r._reject(t),Wt(e),null)))}function Et(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function Wt(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&Kt(t,e._storedError)}function Bt(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){er(e)}(r):nr(r)),e._backpressure=t}Object.defineProperties(WritableStream.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),n(WritableStream.prototype.abort,"abort"),n(WritableStream.prototype.close,"close"),n(WritableStream.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStream.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class WritableStreamDefaultWriter{constructor(e){if(M(e,1,"WritableStreamDefaultWriter"),yt(e,"First parameter"),wt(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;const t=e._state;if("writable"===t)!Et(e)&&e._backpressure?er(this):rr(this),Xt(this);else if("erroring"===t)tr(this,e._storedError),Xt(this);else if("closed"===t)rr(this),Xt(r=this),Zt(r);else{const t=e._storedError;tr(this,t),Jt(this,t)}var r}get closed(){return Ot(this)?this._closedPromise:f(Ut("closed"))}get desiredSize(){if(!Ot(this))throw Ut("desiredSize");if(void 0===this._ownerWritableStream)throw Gt("desiredSize");return function(e){const t=e._ownerWritableStream,r=t._state;if("errored"===r||"erroring"===r)return null;if("closed"===r)return 0;return Mt(t._writableStreamController)}(this)}get ready(){return Ot(this)?this._readyPromise:f(Ut("ready"))}abort(e=void 0){return Ot(this)?void 0===this._ownerWritableStream?f(Gt("abort")):function(e,t){return Rt(e._ownerWritableStream,t)}(this,e):f(Ut("abort"))}close(){if(!Ot(this))return f(Ut("close"));const e=this._ownerWritableStream;return void 0===e?f(Gt("close")):Et(e)?f(new TypeError("Cannot close an already-closing stream")):jt(this)}releaseLock(){if(!Ot(this))throw Ut("releaseLock");void 0!==this._ownerWritableStream&&zt(this)}write(e=void 0){return Ot(this)?void 0===this._ownerWritableStream?f(Gt("write to")):Dt(this,e):f(Ut("write"))}}function Ot(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof WritableStreamDefaultWriter)}function jt(e){return Tt(e._ownerWritableStream)}function kt(e,t){"pending"===e._closedPromiseState?Kt(e,t):function(e,t){Jt(e,t)}(e,t)}function At(e,t){"pending"===e._readyPromiseState?or(e,t):function(e,t){tr(e,t)}(e,t)}function zt(e){const t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");At(e,r),kt(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function Dt(e,t){const r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){if(void 0===e._strategySizeAlgorithm)return 1;try{return e._strategySizeAlgorithm(t)}catch(t){return xt(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return f(Gt("write to"));const a=r._state;if("errored"===a)return f(r._storedError);if(Et(r)||"closed"===a)return f(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return f(r._storedError);const i=function(e){return c(((t,r)=>{const o={_resolve:t,_reject:r};e._writeRequests.push(o)}))}(r);return function(e,t,r){try{we(e,t,r)}catch(t){return void xt(e,t)}const o=e._controlledWritableStream;if(!Et(o)&&"writable"===o._state){Bt(o,Qt(e))}Yt(e)}(o,t,n),i}Object.defineProperties(WritableStreamDefaultWriter.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),n(WritableStreamDefaultWriter.prototype.abort,"abort"),n(WritableStreamDefaultWriter.prototype.close,"close"),n(WritableStreamDefaultWriter.prototype.releaseLock,"releaseLock"),n(WritableStreamDefaultWriter.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStreamDefaultWriter.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});const Lt={};class WritableStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Ft(this))throw Vt("abortReason");return this._abortReason}get signal(){if(!Ft(this))throw Vt("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e=void 0){if(!Ft(this))throw Vt("error");"writable"===this._controlledWritableStream._state&&Nt(this,e)}[R](e){const t=this._abortAlgorithm(e);return $t(this),t}[T](){Re(this)}}function Ft(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof WritableStreamDefaultController)}function It(e,t,r,o,n,a,i,l){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,Re(t),t._abortReason=void 0,t._abortController=function(){if("function"==typeof AbortController)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=l,t._strategyHWM=i,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=a;const s=Qt(t);Bt(e,s);h(d(r()),(()=>(t._started=!0,Yt(t),null)),(r=>(t._started=!0,Ct(e,r),null)))}function $t(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Mt(e){return e._strategyHWM-e._queueTotalSize}function Yt(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void qt(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===Lt?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),ve(e);const r=e._closeAlgorithm();$t(e),h(r,(()=>(function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";const t=e._writer;void 0!==t&&Zt(t)}(t),null)),(e=>(function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Ct(e,t)}(t,e),null)))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r);const o=e._writeAlgorithm(t);h(o,(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(ve(e),!Et(r)&&"writable"===t){const t=Qt(e);Bt(r,t)}return Yt(e),null}),(t=>("writable"===r._state&&$t(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Ct(e,t)}(r,t),null)))}(e,r)}function xt(e,t){"writable"===e._controlledWritableStream._state&&Nt(e,t)}function Qt(e){return Mt(e)<=0}function Nt(e,t){const r=e._controlledWritableStream;$t(e),Pt(r,t)}function Ht(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function Vt(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function Ut(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function Gt(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Xt(e){e._closedPromise=c(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function Jt(e,t){Xt(e),Kt(e,t)}function Kt(e,t){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Zt(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function er(e){e._readyPromise=c(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function tr(e,t){er(e),or(e,t)}function rr(e){er(e),nr(e)}function or(e,t){void 0!==e._readyPromise_reject&&(y(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function nr(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(WritableStreamDefaultController.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(WritableStreamDefaultController.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});const ar="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0;const ir=function(){const e=null==ar?void 0:ar.DOMException;return function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;if("DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?e:void 0}()||function(){const e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return n(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function lr(e,r,o,n,a,i){const l=V(e),s=St(r);e._disturbed=!0;let u=!1,p=d(void 0);return c(((S,g)=>{let v;if(void 0!==i){if(v=()=>{const t=void 0!==i.reason?i.reason:new ir("Aborted","AbortError"),o=[];n||o.push((()=>"writable"===r._state?Rt(r,t):d(void 0))),a||o.push((()=>"readable"===e._state?Or(e,t):d(void 0))),q((()=>Promise.all(o.map((e=>e())))),!0,t)},i.aborted)return void v();i.addEventListener("abort",v)}var w,R,T;if(P(e,l._closedPromise,(e=>(n?E(!0,e):q((()=>Rt(r,e)),!0,e),null))),P(r,s._closedPromise,(t=>(a?E(!0,t):q((()=>Or(e,t)),!0,t),null))),w=e,R=l._closedPromise,T=()=>(o?E():q((()=>function(e){const t=e._ownerWritableStream,r=t._state;return Et(t)||"closed"===r?d(void 0):"errored"===r?f(t._storedError):jt(e)}(s))),null),"closed"===w._state?T():m(R,T),Et(r)||"closed"===r._state){const t=new TypeError("the destination writable stream closed before all data could be piped to it");a?E(!0,t):q((()=>Or(e,t)),!0,t)}function C(){const e=p;return b(p,(()=>e!==p?C():void 0))}function P(e,t,r){"errored"===e._state?r(e._storedError):_(t,r)}function q(e,t,o){function n(){return h(e(),(()=>W(t,o)),(e=>W(!0,e))),null}u||(u=!0,"writable"!==r._state||Et(r)?n():m(C(),n))}function E(e,t){u||(u=!0,"writable"!==r._state||Et(r)?W(e,t):m(C(),(()=>W(e,t))))}function W(e,t){return zt(s),B(l),void 0!==i&&i.removeEventListener("abort",v),e?g(t):S(void 0),null}y(c(((e,r)=>{!function o(n){n?e():b(u?d(!0):b(s._readyPromise,(()=>c(((e,r)=>{Z(l,{_chunkSteps:r=>{p=b(Dt(s,r),void 0,t),e(!1)},_closeSteps:()=>e(!0),_errorSteps:r})})))),o,r)}(!1)})))}))}class ReadableStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!sr(this))throw yr("desiredSize");return mr(this)}close(){if(!sr(this))throw yr("close");if(!_r(this))throw new TypeError("The stream is not in a state that permits close");fr(this)}enqueue(e=void 0){if(!sr(this))throw yr("enqueue");if(!_r(this))throw new TypeError("The stream is not in a state that permits enqueue");return br(this,e)}error(e=void 0){if(!sr(this))throw yr("error");hr(this,e)}[C](e){Re(this);const t=this._cancelAlgorithm(e);return dr(this),t}[P](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=ve(this);this._closeRequested&&0===this._queue.length?(dr(this),jr(t)):ur(this),e._chunkSteps(r)}else U(t,e),ur(this)}[q](){}}function sr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof ReadableStreamDefaultController)}function ur(e){if(!cr(e))return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;h(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ur(e)),null)),(t=>(hr(e,t),null)))}function cr(e){const t=e._controlledReadableStream;if(!_r(e))return!1;if(!e._started)return!1;if(Br(t)&&X(t)>0)return!0;return mr(e)>0}function dr(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function fr(e){if(!_r(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(dr(e),jr(t))}function br(e,t){if(!_r(e))return;const r=e._controlledReadableStream;if(Br(r)&&X(r)>0)G(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw hr(e,t),t}try{we(e,t,r)}catch(t){throw hr(e,t),t}}ur(e)}function hr(e,t){const r=e._controlledReadableStream;"readable"===r._state&&(Re(e),dr(e),kr(r,t))}function mr(e){const t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function _r(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function pr(e,t,r,o,n,a,i){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,Re(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=i,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t;h(d(r()),(()=>(t._started=!0,ur(t),null)),(e=>(hr(t,e),null)))}function yr(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function Sr(e,t){return Ce(e._readableStreamController)?function(e){let t,r,o,n,a,i=V(e),l=!1,s=!1,u=!1,f=!1,b=!1;const h=c((e=>{a=e}));function m(e){_(e._closedPromise,(t=>(e!==i||(He(o._readableStreamController,t),He(n._readableStreamController,t),f&&b||a(void 0)),null)))}function p(){it(i)&&(B(i),i=V(e),m(i));Z(i,{_chunkSteps:t=>{S((()=>{s=!1,u=!1;const r=t;let i=t;if(!f&&!b)try{i=ge(t)}catch(t){return He(o._readableStreamController,t),He(n._readableStreamController,t),void a(Or(e,t))}f||Ne(o._readableStreamController,r),b||Ne(n._readableStreamController,i),l=!1,s?g():u&&v()}))},_closeSteps:()=>{l=!1,f||Qe(o._readableStreamController),b||Qe(n._readableStreamController),o._readableStreamController._pendingPullIntos.length>0&&Xe(o._readableStreamController,0),n._readableStreamController._pendingPullIntos.length>0&&Xe(n._readableStreamController,0),f&&b||a(void 0)},_errorSteps:()=>{l=!1}})}function y(t,r){K(i)&&(B(i),i=rt(e),m(i));const c=r?n:o,d=r?o:n;lt(i,t,1,{_chunkSteps:t=>{S((()=>{s=!1,u=!1;const o=r?b:f;if(r?f:b)o||Je(c._readableStreamController,t);else{let r;try{r=ge(t)}catch(t){return He(c._readableStreamController,t),He(d._readableStreamController,t),void a(Or(e,t))}o||Je(c._readableStreamController,t),Ne(d._readableStreamController,r)}l=!1,s?g():u&&v()}))},_closeSteps:e=>{l=!1;const t=r?b:f,o=r?f:b;t||Qe(c._readableStreamController),o||Qe(d._readableStreamController),void 0!==e&&(t||Je(c._readableStreamController,e),!o&&d._readableStreamController._pendingPullIntos.length>0&&Xe(d._readableStreamController,0)),t&&o||a(void 0)},_errorSteps:()=>{l=!1}})}function g(){if(l)return s=!0,d(void 0);l=!0;const e=Ue(o._readableStreamController);return null===e?p():y(e._view,!1),d(void 0)}function v(){if(l)return u=!0,d(void 0);l=!0;const e=Ue(n._readableStreamController);return null===e?p():y(e._view,!0),d(void 0)}function w(o){if(f=!0,t=o,b){const o=ae([t,r]),n=Or(e,o);a(n)}return h}function R(o){if(b=!0,r=o,f){const o=ae([t,r]),n=Or(e,o);a(n)}return h}function T(){}return o=qr(T,g,w),n=qr(T,v,R),m(i),[o,n]}(e):function(e,t){const r=V(e);let o,n,a,i,l,s=!1,u=!1,f=!1,b=!1;const h=c((e=>{l=e}));function m(){if(s)return u=!0,d(void 0);s=!0;return Z(r,{_chunkSteps:e=>{S((()=>{u=!1;const t=e,r=e;f||br(a._readableStreamController,t),b||br(i._readableStreamController,r),s=!1,u&&m()}))},_closeSteps:()=>{s=!1,f||fr(a._readableStreamController),b||fr(i._readableStreamController),f&&b||l(void 0)},_errorSteps:()=>{s=!1}}),d(void 0)}function p(t){if(f=!0,o=t,b){const t=ae([o,n]),r=Or(e,t);l(r)}return h}function y(t){if(b=!0,n=t,f){const t=ae([o,n]),r=Or(e,t);l(r)}return h}function g(){}return a=Pr(g,m,p),i=Pr(g,m,y),_(r._closedPromise,(e=>(hr(a._readableStreamController,e),hr(i._readableStreamController,e),f&&b||l(void 0),null))),[a,i]}(e)}function gr(e){return r(o=e)&&void 0!==o.getReader?function(e){let o;function n(){let t;try{t=e.read()}catch(e){return f(e)}return p(t,(e=>{if(!r(e))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)fr(o._readableStreamController);else{const t=e.value;br(o._readableStreamController,t)}}))}function a(t){try{return d(e.cancel(t))}catch(e){return f(e)}}return o=Pr(t,n,a,0),o}(e.getReader()):function(e){let o;const n=be(e,"async");function a(){let e;try{e=he(n)}catch(e){return f(e)}return p(d(e),(e=>{if(!r(e))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)fr(o._readableStreamController);else{const t=e.value;br(o._readableStreamController,t)}}))}function i(e){const t=n.iterator;let o;try{o=ce(t,"return")}catch(e){return f(e)}if(void 0===o)return d(void 0);return p(v(o,t,[e]),(e=>{if(!r(e))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}return o=Pr(t,a,i,0),o}(e);var o}function vr(e,t,r){return I(e,r),r=>v(e,t,[r])}function wr(e,t,r){return I(e,r),r=>v(e,t,[r])}function Rr(e,t,r){return I(e,r),r=>g(e,t,[r])}function Tr(e,t){if("bytes"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function Cr(e,t){F(e,t);const r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(a,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(o),preventClose:Boolean(n),signal:a}}Object.defineProperties(ReadableStreamDefaultController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ReadableStreamDefaultController.prototype.close,"close"),n(ReadableStreamDefaultController.prototype.enqueue,"enqueue"),n(ReadableStreamDefaultController.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStreamDefaultController.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class ReadableStream{constructor(e={},t={}){void 0===e?e=null:$(e,"First parameter");const r=ft(t,"Second parameter"),o=function(e,t){F(e,t);const r=e,o=null==r?void 0:r.autoAllocateChunkSize,n=null==r?void 0:r.cancel,a=null==r?void 0:r.pull,i=null==r?void 0:r.start,l=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===o?void 0:N(o,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:vr(n,r,`${t} has member 'cancel' that`),pull:void 0===a?void 0:wr(a,r,`${t} has member 'pull' that`),start:void 0===i?void 0:Rr(i,r,`${t} has member 'start' that`),type:void 0===l?void 0:Tr(l,`${t} has member 'type' that`)}}(e,"First parameter");if(Er(this),"bytes"===o.type){if(void 0!==r.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){const o=Object.create(ReadableByteStreamController.prototype);let n,a,i;n=void 0!==t.start?()=>t.start(o):()=>{},a=void 0!==t.pull?()=>t.pull(o):()=>d(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0);const l=t.autoAllocateChunkSize;if(0===l)throw new TypeError("autoAllocateChunkSize must be greater than 0");Ke(e,o,n,a,i,r,l)}(this,o,ct(r,0))}else{const e=dt(r);!function(e,t,r,o){const n=Object.create(ReadableStreamDefaultController.prototype);let a,i,l;a=void 0!==t.start?()=>t.start(n):()=>{},i=void 0!==t.pull?()=>t.pull(n):()=>d(void 0),l=void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0),pr(e,n,a,i,l,r,o)}(this,o,ct(r,1),e)}}get locked(){if(!Wr(this))throw Ar("locked");return Br(this)}cancel(e=void 0){return Wr(this)?Br(this)?f(new TypeError("Cannot cancel a stream that already has a reader")):Or(this,e):f(Ar("cancel"))}getReader(e=void 0){if(!Wr(this))throw Ar("getReader");return void 0===function(e,t){F(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:tt(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?V(this):rt(this)}pipeThrough(e,t={}){if(!Wr(this))throw Ar("pipeThrough");M(e,1,"pipeThrough");const r=function(e,t){F(e,t);const r=null==e?void 0:e.readable;Y(r,"readable","ReadableWritablePair"),H(r,`${t} has member 'readable' that`);const o=null==e?void 0:e.writable;return Y(o,"writable","ReadableWritablePair"),yt(o,`${t} has member 'writable' that`),{readable:r,writable:o}}(e,"First parameter"),o=Cr(t,"Second parameter");if(Br(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(wt(r.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return y(lr(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){if(!Wr(this))return f(Ar("pipeTo"));if(void 0===e)return f("Parameter 1 is required in 'pipeTo'.");if(!vt(e))return f(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let r;try{r=Cr(t,"Second parameter")}catch(e){return f(e)}return Br(this)?f(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):wt(e)?f(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):lr(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!Wr(this))throw Ar("tee");return ae(Sr(this))}values(e=void 0){if(!Wr(this))throw Ar("values");return function(e,t){const r=V(e),o=new me(r,t),n=Object.create(_e);return n._asyncIteratorImpl=o,n}(this,function(e,t){F(e,t);const r=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(r)}}(e,"First parameter").preventCancel)}[fe](e){return this.values(e)}static from(e){return gr(e)}}function Pr(e,t,r,o=1,n=(()=>1)){const a=Object.create(ReadableStream.prototype);Er(a);return pr(a,Object.create(ReadableStreamDefaultController.prototype),e,t,r,o,n),a}function qr(e,t,r){const o=Object.create(ReadableStream.prototype);Er(o);return Ke(o,Object.create(ReadableByteStreamController.prototype),e,t,r,0,void 0),o}function Er(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Wr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof ReadableStream)}function Br(e){return void 0!==e._reader}function Or(e,r){if(e._disturbed=!0,"closed"===e._state)return d(void 0);if("errored"===e._state)return f(e._storedError);jr(e);const o=e._reader;if(void 0!==o&&it(o)){const e=o._readIntoRequests;o._readIntoRequests=new w,e.forEach((e=>{e._closeSteps(void 0)}))}return p(e._readableStreamController[C](r),t)}function jr(e){e._state="closed";const t=e._reader;if(void 0!==t&&(z(t),K(t))){const e=t._readRequests;t._readRequests=new w,e.forEach((e=>{e._closeSteps()}))}}function kr(e,t){e._state="errored",e._storedError=t;const r=e._reader;void 0!==r&&(A(r,t),K(r)?ee(r,t):st(r,t))}function Ar(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function zr(e,t){F(e,t);const r=null==e?void 0:e.highWaterMark;return Y(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:x(r)}}Object.defineProperties(ReadableStream,{from:{enumerable:!0}}),Object.defineProperties(ReadableStream.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),n(ReadableStream.from,"from"),n(ReadableStream.prototype.cancel,"cancel"),n(ReadableStream.prototype.getReader,"getReader"),n(ReadableStream.prototype.pipeThrough,"pipeThrough"),n(ReadableStream.prototype.pipeTo,"pipeTo"),n(ReadableStream.prototype.tee,"tee"),n(ReadableStream.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ReadableStream.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(ReadableStream.prototype,fe,{value:ReadableStream.prototype.values,writable:!0,configurable:!0});const Dr=e=>e.byteLength;n(Dr,"size");class ByteLengthQueuingStrategy{constructor(e){M(e,1,"ByteLengthQueuingStrategy"),e=zr(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Fr(this))throw Lr("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!Fr(this))throw Lr("size");return Dr}}function Lr(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function Fr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof ByteLengthQueuingStrategy)}Object.defineProperties(ByteLengthQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ByteLengthQueuingStrategy.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});const Ir=()=>1;n(Ir,"size");class CountQueuingStrategy{constructor(e){M(e,1,"CountQueuingStrategy"),e=zr(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Mr(this))throw $r("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!Mr(this))throw $r("size");return Ir}}function $r(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function Mr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof CountQueuingStrategy)}function Yr(e,t,r){return I(e,r),r=>v(e,t,[r])}function xr(e,t,r){return I(e,r),r=>g(e,t,[r])}function Qr(e,t,r){return I(e,r),(r,o)=>v(e,t,[r,o])}function Nr(e,t,r){return I(e,r),r=>v(e,t,[r])}Object.defineProperties(CountQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(CountQueuingStrategy.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class TransformStream{constructor(e={},t={},r={}){void 0===e&&(e=null);const o=ft(t,"Second parameter"),n=ft(r,"Third parameter"),a=function(e,t){F(e,t);const r=null==e?void 0:e.cancel,o=null==e?void 0:e.flush,n=null==e?void 0:e.readableType,a=null==e?void 0:e.start,i=null==e?void 0:e.transform,l=null==e?void 0:e.writableType;return{cancel:void 0===r?void 0:Nr(r,e,`${t} has member 'cancel' that`),flush:void 0===o?void 0:Yr(o,e,`${t} has member 'flush' that`),readableType:n,start:void 0===a?void 0:xr(a,e,`${t} has member 'start' that`),transform:void 0===i?void 0:Qr(i,e,`${t} has member 'transform' that`),writableType:l}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");const i=ct(n,0),l=dt(n),s=ct(o,1),u=dt(o);let b;!function(e,t,r,o,n,a){function i(){return t}function l(t){return function(e,t){const r=e._transformStreamController;if(e._backpressure){return p(e._backpressureChangePromise,(()=>{const o=e._writable;if("erroring"===o._state)throw o._storedError;return eo(r,t)}))}return eo(r,t)}(e,t)}function s(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const o=e._readable;r._finishPromise=c(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const n=r._cancelAlgorithm(t);return Kr(r),h(n,(()=>("errored"===o._state?oo(r,o._storedError):(hr(o._readableStreamController,t),ro(r)),null)),(e=>(hr(o._readableStreamController,e),oo(r,e),null))),r._finishPromise}(e,t)}function u(){return function(e){const t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;const r=e._readable;t._finishPromise=c(((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r}));const o=t._flushAlgorithm();return Kr(t),h(o,(()=>("errored"===r._state?oo(t,r._storedError):(fr(r._readableStreamController),ro(t)),null)),(e=>(hr(r._readableStreamController,e),oo(t,e),null))),t._finishPromise}(e)}function d(){return function(e){return Xr(e,!1),e._backpressureChangePromise}(e)}function f(t){return function(e,t){const r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;const o=e._writable;r._finishPromise=c(((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t}));const n=r._cancelAlgorithm(t);return Kr(r),h(n,(()=>("errored"===o._state?oo(r,o._storedError):(xt(o._writableStreamController,t),Gr(e),ro(r)),null)),(t=>(xt(o._writableStreamController,t),Gr(e),oo(r,t),null))),r._finishPromise}(e,t)}e._writable=function(e,t,r,o,n=1,a=(()=>1)){const i=Object.create(WritableStream.prototype);return gt(i),It(i,Object.create(WritableStreamDefaultController.prototype),e,t,r,o,n,a),i}(i,l,u,s,r,o),e._readable=Pr(i,d,f,n,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Xr(e,!0),e._transformStreamController=void 0}(this,c((e=>{b=e})),s,u,i,l),function(e,t){const r=Object.create(TransformStreamDefaultController.prototype);let o,n,a;o=void 0!==t.transform?e=>t.transform(e,r):e=>{try{return Zr(r,e),d(void 0)}catch(e){return f(e)}};n=void 0!==t.flush?()=>t.flush(r):()=>d(void 0);a=void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0);!function(e,t,r,o,n){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=o,t._cancelAlgorithm=n,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0}(e,r,o,n,a)}(this,a),void 0!==a.start?b(a.start(this._transformStreamController)):b(void 0)}get readable(){if(!Hr(this))throw no("readable");return this._readable}get writable(){if(!Hr(this))throw no("writable");return this._writable}}function Hr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof TransformStream)}function Vr(e,t){hr(e._readable._readableStreamController,t),Ur(e,t)}function Ur(e,t){Kr(e._transformStreamController),xt(e._writable._writableStreamController,t),Gr(e)}function Gr(e){e._backpressure&&Xr(e,!1)}function Xr(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=c((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(TransformStream.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(TransformStream.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class TransformStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Jr(this))throw to("desiredSize");return mr(this._controlledTransformStream._readable._readableStreamController)}enqueue(e=void 0){if(!Jr(this))throw to("enqueue");Zr(this,e)}error(e=void 0){if(!Jr(this))throw to("error");var t;t=e,Vr(this._controlledTransformStream,t)}terminate(){if(!Jr(this))throw to("terminate");!function(e){const t=e._controlledTransformStream;fr(t._readable._readableStreamController);const r=new TypeError("TransformStream terminated");Ur(t,r)}(this)}}function Jr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof TransformStreamDefaultController)}function Kr(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function Zr(e,t){const r=e._controlledTransformStream,o=r._readable._readableStreamController;if(!_r(o))throw new TypeError("Readable side is not in a state that permits enqueue");try{br(o,t)}catch(e){throw Ur(r,e),r._readable._storedError}const n=function(e){return!cr(e)}(o);n!==r._backpressure&&Xr(r,!0)}function eo(e,t){return p(e._transformAlgorithm(t),void 0,(t=>{throw Vr(e._controlledTransformStream,t),t}))}function to(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function ro(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function oo(e,t){void 0!==e._finishPromise_reject&&(y(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function no(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(TransformStreamDefaultController.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),n(TransformStreamDefaultController.prototype.enqueue,"enqueue"),n(TransformStreamDefaultController.prototype.error,"error"),n(TransformStreamDefaultController.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(TransformStreamDefaultController.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0}),e.ByteLengthQueuingStrategy=ByteLengthQueuingStrategy,e.CountQueuingStrategy=CountQueuingStrategy,e.ReadableByteStreamController=ReadableByteStreamController,e.ReadableStream=ReadableStream,e.ReadableStreamBYOBReader=ReadableStreamBYOBReader,e.ReadableStreamBYOBRequest=ReadableStreamBYOBRequest,e.ReadableStreamDefaultController=ReadableStreamDefaultController,e.ReadableStreamDefaultReader=ReadableStreamDefaultReader,e.TransformStream=TransformStream,e.TransformStreamDefaultController=TransformStreamDefaultController,e.WritableStream=WritableStream,e.WritableStreamDefaultController=WritableStreamDefaultController,e.WritableStreamDefaultWriter=WritableStreamDefaultWriter}));
