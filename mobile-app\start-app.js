#!/usr/bin/env node

// Script de démarrage simple pour l'application
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Démarrage de l\'application de livraison africaine...');
console.log('📱 Application: Delivery App Africa');
console.log('🌍 Marchés: Afrique de l\'Ouest et de l\'Est');
console.log('');

// Vérifier que nous sommes dans le bon répertoire
const packageJsonPath = path.join(__dirname, 'package.json');
try {
  require(packageJsonPath);
  console.log('✅ Répertoire correct détecté');
} catch (error) {
  console.error('❌ Erreur: package.json non trouvé');
  console.error('Assurez-vous d\'être dans le répertoire mobile-app');
  process.exit(1);
}

// Démarrer Expo
console.log('🔄 Démarrage du serveur Expo...');
const expo = spawn('npx', ['expo', 'start', '--clear'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname
});

expo.on('error', (error) => {
  console.error('❌ Erreur lors du démarrage:', error.message);
});

expo.on('close', (code) => {
  console.log(`\n📱 Serveur Expo fermé avec le code ${code}`);
});

// Gestion des signaux pour arrêter proprement
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt de l\'application...');
  expo.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt de l\'application...');
  expo.kill('SIGTERM');
  process.exit(0);
});
