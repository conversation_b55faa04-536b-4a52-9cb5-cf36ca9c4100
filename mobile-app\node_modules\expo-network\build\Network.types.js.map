{"version": 3, "file": "Network.types.js", "sourceRoot": "", "sources": ["../src/Network.types.ts"], "names": [], "mappings": "AAqBA,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,gBAgDX;AAhDD,WAAY,gBAAgB;IAC1B;;OAEG;IACH,iCAAa,CAAA;IACb;;OAEG;IACH,uCAAmB,CAAA;IACnB;;;;;OAKG;IACH,yCAAqB,CAAA;IACrB;;;;OAIG;IACH,iCAAa,CAAA;IACb;;;OAGG;IACH,2CAAuB,CAAA;IACvB;;;;OAIG;IACH,yCAAqB,CAAA;IACrB;;;OAGG;IACH,mCAAe,CAAA;IACf;;;OAGG;IACH,+BAAW,CAAA;IACX;;;OAGG;IACH,mCAAe,CAAA;AACjB,CAAC,EAhDW,gBAAgB,KAAhB,gBAAgB,QAgD3B", "sourcesContent": ["// @needsAudit\nexport type NetworkState = {\n  /**\n   * A [`NetworkStateType`](#networkstatetype) enum value that represents the current network\n   * connection type.\n   */\n  type?: NetworkStateType;\n  /**\n   * If there is an active network connection. Note that this does not mean that internet is reachable.\n   * This field is `false` if the type is either `Network.NetworkStateType.NONE` or `Network.NetworkStateType.UNKNOWN`,\n   * `true` otherwise.\n   */\n  isConnected?: boolean;\n  /**\n   * If the internet is reachable with the currently active network connection. On Android, this\n   * depends on `NetInfo.isConnected()` (API level < 29) or `ConnectivityManager.getActiveNetwork()`\n   * (API level >= 29). On iOS, this value will always be the same as `isConnected`.\n   */\n  isInternetReachable?: boolean;\n};\n\n// @needsAudit\n/**\n * An enum of the different types of devices supported by Expo.\n */\nexport enum NetworkStateType {\n  /**\n   * No active network connection detected.\n   */\n  NONE = 'NONE',\n  /**\n   * The connection type could not be determined.\n   */\n  UNKNOWN = 'UNKNOWN',\n  /**\n   * Active network connection over mobile data or [`DUN-specific`](https://developer.android.com/reference/android/net/ConnectivityManager#TYPE_MOBILE_DUN)\n   * mobile connection when setting an upstream connection for tethering.\n   * @platform android\n   * @platform ios\n   */\n  CELLULAR = 'CELLULAR',\n  /**\n   * Active network connection over Wi-Fi.\n   * @platform android\n   * @platform ios\n   */\n  WIFI = 'WIFI',\n  /**\n   * Active network connection over Bluetooth.\n   * @platform android\n   */\n  BLUETOOTH = 'BLUETOOTH',\n  /**\n   * Active network connection over Ethernet.\n   * @platform android\n   * @platform ios\n   */\n  ETHERNET = 'ETHERNET',\n  /**\n   * Active network connection over WiMAX.\n   * @platform android\n   */\n  WIMAX = 'WIMAX',\n  /**\n   * Active network connection over VPN.\n   * @platform android\n   */\n  VPN = 'VPN',\n  /**\n   * Active network connection over other network connection types.\n   * @platform android\n   */\n  OTHER = 'OTHER',\n}\n\n// @needsAudit\n/**\n * Represents an event that provides the updated network state when there is a change in the network status.\n * This is passed as the argument to listeners registered with [`addNetworkStateListener()`](#networkaddnetworkstatelistenerlistener).\n */\nexport type NetworkStateEvent = NetworkState;\n\n/**\n * @hidden\n */\nexport type NetworkEvents = {\n  onNetworkStateChanged(event: NetworkStateEvent): void;\n};\n"]}