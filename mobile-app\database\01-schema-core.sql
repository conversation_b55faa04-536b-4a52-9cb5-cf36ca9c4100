-- =====================================================
-- SCHÉMA BASE DE DONNÉES - APPLICATION LIVRAISON AFRIQUE
-- Version: 1.0
-- PostgreSQL 14+
-- =====================================================

-- Extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- =====================================================
-- TYPES ENUM
-- =====================================================

-- R<PERSON>les utilisateurs
CREATE TYPE user_role AS ENUM (
    'customer',      -- Client
    'delivery_rider', -- Livreur
    'merchant',      -- Commerçant/Restaurant
    'admin',         -- Administrateur
    'super_admin'    -- Super administrateur
);

-- Statuts des commandes
CREATE TYPE order_status AS ENUM (
    'draft',         -- Brouillon (panier)
    'pending',       -- En attente de confirmation
    'confirmed',     -- Confirmée par le commerçant
    'preparing',     -- En préparation
    'ready',         -- Prête pour livraison
    'assigned',      -- Assignée à un livreur
    'picked_up',     -- Récupérée par le livreur
    'in_transit',    -- En cours de livraison
    'delivered',     -- Livrée
    'cancelled',     -- Annulée
    'failed',        -- Échec de livraison
    'refunded'       -- Remboursée
);

-- Types de livraison
CREATE TYPE delivery_type AS ENUM (
    'food',          -- Livraison de repas
    'package',       -- Livraison de colis
    'grocery',       -- Livraison d'épicerie
    'pharmacy',      -- Livraison pharmacie
    'wholesale',     -- Livraison en gros
    'documents'      -- Livraison de documents
);

-- Modes de paiement
CREATE TYPE payment_method AS ENUM (
    'mobile_money',  -- Mobile Money (M-Pesa, Orange Money, etc.)
    'card',          -- Carte bancaire
    'cash',          -- Espèces à la livraison
    'bank_transfer', -- Virement bancaire
    'wallet'         -- Portefeuille électronique
);

-- Statuts des paiements
CREATE TYPE payment_status AS ENUM (
    'pending',       -- En attente
    'processing',    -- En cours de traitement
    'completed',     -- Complété
    'failed',        -- Échec
    'cancelled',     -- Annulé
    'refunded',      -- Remboursé
    'expired'        -- Expiré
);

-- Providers Mobile Money
CREATE TYPE mobile_money_provider AS ENUM (
    'mpesa',         -- M-Pesa (Kenya, Tanzanie)
    'orange_money',  -- Orange Money (multi-pays)
    'mtn_momo',      -- MTN Mobile Money
    'airtel_money',  -- Airtel Money
    'wave',          -- Wave (Sénégal, Côte d'Ivoire)
    'moov_money',    -- Moov Money
    'tigo_pesa',     -- Tigo Pesa
    'ecocash'        -- EcoCash (Zimbabwe)
);

-- Types de véhicules de livraison
CREATE TYPE vehicle_type AS ENUM (
    'motorcycle',    -- Moto
    'bicycle',       -- Vélo
    'car',          -- Voiture
    'van',          -- Camionnette
    'truck',        -- Camion
    'walking'       -- À pied
);

-- Statuts des livreurs
CREATE TYPE rider_status AS ENUM (
    'offline',       -- Hors ligne
    'available',     -- Disponible
    'busy',         -- Occupé
    'on_break',     -- En pause
    'inactive'      -- Inactif
);

-- Langues supportées
CREATE TYPE supported_language AS ENUM (
    'fr',           -- Français
    'en',           -- Anglais
    'wo',           -- Wolof (Sénégal)
    'sw',           -- Swahili (Kenya, Tanzanie)
    'ha',           -- Hausa (Nigeria, Niger)
    'yo',           -- Yoruba (Nigeria)
    'ig',           -- Igbo (Nigeria)
    'am',           -- Amharique (Éthiopie)
    'ar',           -- Arabe
    'pt'            -- Portugais (Angola, Mozambique)
);

-- =====================================================
-- TABLES PRINCIPALES
-- =====================================================

-- Table des pays et régions
CREATE TABLE countries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(3) UNIQUE NOT NULL, -- Code ISO 3166-1 alpha-3
    name_en VARCHAR(100) NOT NULL,
    name_fr VARCHAR(100) NOT NULL,
    name_local VARCHAR(100),
    currency_code VARCHAR(3) NOT NULL, -- XOF, KES, NGN, etc.
    currency_symbol VARCHAR(10) NOT NULL,
    phone_prefix VARCHAR(10) NOT NULL,
    timezone VARCHAR(50) NOT NULL, -- Africa/Dakar, Africa/Nairobi, etc.
    default_language supported_language NOT NULL DEFAULT 'en',
    supported_languages supported_language[] NOT NULL DEFAULT ARRAY['en', 'fr'],
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des villes
CREATE TABLE cities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    country_id UUID NOT NULL REFERENCES countries(id),
    name VARCHAR(100) NOT NULL,
    name_local VARCHAR(100),
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    timezone VARCHAR(50) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    service_radius_km INTEGER NOT NULL DEFAULT 50, -- Rayon de service en km
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des zones de livraison
CREATE TABLE delivery_zones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    city_id UUID NOT NULL REFERENCES cities(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    -- Géométrie de la zone (polygone)
    boundary GEOMETRY(POLYGON, 4326) NOT NULL,
    -- Centre de la zone
    center_point GEOMETRY(POINT, 4326) NOT NULL,
    -- Frais de livraison de base pour cette zone
    base_delivery_fee DECIMAL(10, 2) NOT NULL DEFAULT 0,
    -- Temps de livraison estimé en minutes
    estimated_delivery_time INTEGER NOT NULL DEFAULT 30,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des utilisateurs (étend auth.users de Supabase)
CREATE TABLE profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    role user_role NOT NULL DEFAULT 'customer',
    phone_number VARCHAR(20) UNIQUE NOT NULL,
    country_id UUID NOT NULL REFERENCES countries(id),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    avatar_url TEXT,
    preferred_language supported_language NOT NULL DEFAULT 'en',
    -- Localisation par défaut
    default_city_id UUID REFERENCES cities(id),
    default_address TEXT,
    default_location GEOMETRY(POINT, 4326),
    -- Préférences
    notifications_enabled BOOLEAN NOT NULL DEFAULT true,
    sms_notifications BOOLEAN NOT NULL DEFAULT true,
    email_notifications BOOLEAN NOT NULL DEFAULT false,
    marketing_notifications BOOLEAN NOT NULL DEFAULT false,
    -- Métadonnées
    last_seen_at TIMESTAMP WITH TIME ZONE,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des adresses
CREATE TABLE addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    city_id UUID NOT NULL REFERENCES cities(id),
    delivery_zone_id UUID REFERENCES delivery_zones(id),
    label VARCHAR(50), -- 'Domicile', 'Bureau', 'Autre'
    street_address TEXT NOT NULL,
    landmark TEXT, -- Point de repère
    additional_info TEXT, -- Instructions supplémentaires
    location GEOMETRY(POINT, 4326) NOT NULL,
    is_default BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des commerçants/restaurants
CREATE TABLE merchants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    owner_id UUID NOT NULL REFERENCES profiles(id),
    business_name VARCHAR(200) NOT NULL,
    business_type VARCHAR(100) NOT NULL, -- 'restaurant', 'grocery', 'pharmacy', etc.
    description TEXT,
    logo_url TEXT,
    cover_image_url TEXT,
    -- Contact
    phone_number VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    website_url TEXT,
    -- Adresse business
    city_id UUID NOT NULL REFERENCES cities(id),
    delivery_zone_id UUID REFERENCES delivery_zones(id),
    address TEXT NOT NULL,
    location GEOMETRY(POINT, 4326) NOT NULL,
    -- Horaires (JSON avec structure pour chaque jour)
    business_hours JSONB NOT NULL DEFAULT '{}',
    -- Configuration livraison
    delivery_radius_km INTEGER NOT NULL DEFAULT 10,
    min_order_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
    delivery_fee DECIMAL(10, 2) NOT NULL DEFAULT 0,
    estimated_prep_time INTEGER NOT NULL DEFAULT 30, -- minutes
    -- Statut
    is_verified BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_accepting_orders BOOLEAN NOT NULL DEFAULT true,
    -- Ratings
    average_rating DECIMAL(3, 2) DEFAULT 0,
    total_ratings INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des catégories de produits
CREATE TABLE product_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    merchant_id UUID NOT NULL REFERENCES merchants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    name_translations JSONB DEFAULT '{}', -- Traductions
    description TEXT,
    image_url TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des produits
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    merchant_id UUID NOT NULL REFERENCES merchants(id) ON DELETE CASCADE,
    category_id UUID REFERENCES product_categories(id),
    name VARCHAR(200) NOT NULL,
    name_translations JSONB DEFAULT '{}', -- Traductions
    description TEXT,
    description_translations JSONB DEFAULT '{}',
    image_url TEXT,
    images JSONB DEFAULT '[]', -- Array d'URLs d'images
    -- Prix
    price DECIMAL(10, 2) NOT NULL,
    compare_at_price DECIMAL(10, 2), -- Prix barré
    cost_price DECIMAL(10, 2), -- Prix de revient
    -- Gestion stock
    track_inventory BOOLEAN NOT NULL DEFAULT false,
    inventory_quantity INTEGER DEFAULT 0,
    allow_backorder BOOLEAN NOT NULL DEFAULT false,
    -- Métadonnées
    sku VARCHAR(100),
    barcode VARCHAR(100),
    weight_grams INTEGER,
    -- Options et variantes (JSON)
    options JSONB DEFAULT '[]',
    variants JSONB DEFAULT '[]',
    -- Statut
    is_available BOOLEAN NOT NULL DEFAULT true,
    is_featured BOOLEAN NOT NULL DEFAULT false,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour optimiser les performances
CREATE INDEX idx_countries_code ON countries(code);
CREATE INDEX idx_countries_active ON countries(is_active);

CREATE INDEX idx_cities_country ON cities(country_id);
CREATE INDEX idx_cities_active ON cities(is_active);
CREATE INDEX idx_cities_location ON cities USING GIST(ST_Point(longitude, latitude));

CREATE INDEX idx_delivery_zones_city ON delivery_zones(city_id);
CREATE INDEX idx_delivery_zones_boundary ON delivery_zones USING GIST(boundary);
CREATE INDEX idx_delivery_zones_center ON delivery_zones USING GIST(center_point);
CREATE INDEX idx_delivery_zones_active ON delivery_zones(is_active);

CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_phone ON profiles(phone_number);
CREATE INDEX idx_profiles_country ON profiles(country_id);
CREATE INDEX idx_profiles_city ON profiles(default_city_id);
CREATE INDEX idx_profiles_active ON profiles(is_active);
CREATE INDEX idx_profiles_location ON profiles USING GIST(default_location);

CREATE INDEX idx_addresses_user ON addresses(user_id);
CREATE INDEX idx_addresses_city ON addresses(city_id);
CREATE INDEX idx_addresses_zone ON addresses(delivery_zone_id);
CREATE INDEX idx_addresses_location ON addresses USING GIST(location);
CREATE INDEX idx_addresses_default ON addresses(user_id, is_default) WHERE is_default = true;

CREATE INDEX idx_merchants_owner ON merchants(owner_id);
CREATE INDEX idx_merchants_city ON merchants(city_id);
CREATE INDEX idx_merchants_zone ON merchants(delivery_zone_id);
CREATE INDEX idx_merchants_location ON merchants USING GIST(location);
CREATE INDEX idx_merchants_active ON merchants(is_active);
CREATE INDEX idx_merchants_accepting ON merchants(is_accepting_orders);
CREATE INDEX idx_merchants_rating ON merchants(average_rating);

CREATE INDEX idx_product_categories_merchant ON product_categories(merchant_id);
CREATE INDEX idx_product_categories_active ON product_categories(is_active);
CREATE INDEX idx_product_categories_sort ON product_categories(sort_order);

CREATE INDEX idx_products_merchant ON products(merchant_id);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_available ON products(is_available);
CREATE INDEX idx_products_featured ON products(is_featured);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_name_trgm ON products USING GIN(name gin_trgm_ops);
CREATE INDEX idx_products_search ON products USING GIN(to_tsvector('french', name || ' ' || COALESCE(description, '')));

-- Contraintes de validation
ALTER TABLE profiles ADD CONSTRAINT chk_phone_format 
    CHECK (phone_number ~ '^\+[1-9]\d{1,14}$');

ALTER TABLE addresses ADD CONSTRAINT chk_one_default_per_user 
    EXCLUDE (user_id WITH =) WHERE (is_default = true);

ALTER TABLE merchants ADD CONSTRAINT chk_delivery_radius 
    CHECK (delivery_radius_km > 0 AND delivery_radius_km <= 100);

ALTER TABLE merchants ADD CONSTRAINT chk_min_order_amount 
    CHECK (min_order_amount >= 0);

ALTER TABLE products ADD CONSTRAINT chk_price_positive 
    CHECK (price > 0);

ALTER TABLE products ADD CONSTRAINT chk_inventory_non_negative 
    CHECK (inventory_quantity >= 0);
