// =====================================================
// TYPES AUTHENTIFICATION
// =====================================================

export type UserRole = 'client' | 'deliverer' | 'merchant' | 'admin';

export interface User {
  id: string;
  email: string;
  role: UserRole;
  profile: UserProfile;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  isVerified: boolean;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  phone: string;
  avatar?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  language: 'fr' | 'en' | 'wo' | 'ha';
  country: string;
  city: string;
  address?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface ClientProfile extends UserProfile {
  preferences: {
    notifications: {
      orderUpdates: boolean;
      promotions: boolean;
      newRestaurants: boolean;
    };
    dietary: string[];
    favoriteCategories: string[];
  };
  deliveryAddresses: DeliveryAddress[];
  paymentMethods: PaymentMethod[];
}

export interface DelivererProfile extends UserProfile {
  vehicleType: 'bicycle' | 'motorcycle' | 'car' | 'walking';
  vehicleDetails?: {
    brand?: string;
    model?: string;
    licensePlate?: string;
    color?: string;
  };
  documents: {
    idCard: string;
    drivingLicense?: string;
    vehicleRegistration?: string;
  };
  bankAccount: {
    accountNumber: string;
    bankName: string;
    accountHolder: string;
  };
  workingHours: {
    start: string;
    end: string;
    daysOfWeek: number[];
  };
  isOnline: boolean;
  currentLocation?: {
    latitude: number;
    longitude: number;
    heading?: number;
    speed?: number;
  };
  rating: number;
  totalDeliveries: number;
  earnings: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    total: number;
  };
}

export interface MerchantProfile extends UserProfile {
  businessName: string;
  businessType: 'restaurant' | 'grocery' | 'pharmacy' | 'electronics' | 'fashion' | 'other';
  businessLicense: string;
  taxNumber?: string;
  description: string;
  logo?: string;
  coverImage?: string;
  openingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  deliveryRadius: number;
  minimumOrder: number;
  deliveryFee: number;
  estimatedDeliveryTime: number;
  rating: number;
  totalOrders: number;
  isOpen: boolean;
  categories: string[];
  paymentMethods: string[];
}

export interface DeliveryAddress {
  id: string;
  label: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  instructions?: string;
  isDefault: boolean;
}

export interface PaymentMethod {
  id: string;
  type: 'mobile_money' | 'card' | 'cash';
  provider: string;
  details: {
    phoneNumber?: string;
    cardLast4?: string;
    expiryDate?: string;
  };
  isDefault: boolean;
}

export interface AuthSession {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  confirmPassword: string;
  role: UserRole;
  profile: Partial<UserProfile>;
}

export interface AuthState {
  user: User | null;
  session: AuthSession | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateProfile: (profile: Partial<UserProfile>) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  clearError: () => void;
}

export type AuthStore = AuthState & AuthActions;
