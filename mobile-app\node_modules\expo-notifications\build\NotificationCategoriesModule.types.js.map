{"version": 3, "file": "NotificationCategoriesModule.types.js", "sourceRoot": "", "sources": ["../src/NotificationCategoriesModule.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\nimport { NotificationCategory, NotificationAction } from './Notifications.types';\n\nexport interface NotificationCategoriesModule extends ProxyNativeModule {\n  getNotificationCategoriesAsync: () => Promise<NotificationCategory[]>;\n  setNotificationCategoryAsync: (\n    identifier: string,\n    actions: NotificationAction[],\n    options?: {\n      previewPlaceholder?: string;\n      intentIdentifiers?: string[];\n      categorySummaryFormat?: string;\n      customDismissAction?: boolean;\n      allowInCarPlay?: boolean;\n      showTitle?: boolean;\n      showSubtitle?: boolean;\n      allowAnnouncement?: boolean;\n    }\n  ) => Promise<NotificationCategory>;\n  deleteNotificationCategoryAsync: (identifier: string) => Promise<boolean>;\n}\n"]}