// =====================================================
// POLYFILLS POUR REACT NATIVE
// =====================================================

// Polyfill pour URL (requis par Supabase)
import 'react-native-url-polyfill/auto';

// Polyfill pour TextEncoder/TextDecoder
import 'text-encoding-polyfill';

// Configuration globale pour React Native
if (typeof global !== 'undefined') {
  // Polyfill pour Buffer
  if (!global.Buffer) {
    global.Buffer = require('buffer').Buffer;
  }

  // Polyfill pour process
  if (!global.process) {
    global.process = require('process');
  }

  // Polyfill pour btoa/atob
  if (!global.btoa) {
    global.btoa = require('base-64').encode;
  }

  if (!global.atob) {
    global.atob = require('base-64').decode;
  }

  // Polyfill pour TextEncoder/TextDecoder
  if (!global.TextEncoder) {
    const { TextEncoder, TextDecoder } = require('text-encoding-polyfill');
    global.TextEncoder = TextEncoder;
    global.TextDecoder = TextDecoder;
  }
}

// Configuration spécifique pour les environnements de développement
if (__DEV__) {
  // Désactiver les warnings pour les polyfills
  const originalWarn = console.warn;
  console.warn = (...args) => {
    if (
      args[0] &&
      typeof args[0] === 'string' &&
      (args[0].includes('polyfill') ||
       args[0].includes('TextEncoder') ||
       args[0].includes('WebSocket'))
    ) {
      return;
    }
    originalWarn.apply(console, args);
  };
}

// Export pour utilisation dans d'autres fichiers
export default {
  initialized: true,
  timestamp: new Date().toISOString(),
};
