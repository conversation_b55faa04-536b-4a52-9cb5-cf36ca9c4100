-- =====================================================
-- SCHÉMA COMMANDES ET LIVRAISON
-- =====================================================

-- Table des commandes
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number VARCHAR(20) UNIQUE NOT NULL, -- Format: ORD-YYYYMMDD-XXXX
    customer_id UUID NOT NULL REFERENCES profiles(id),
    merchant_id UUID NOT NULL REFERENCES merchants(id),
    delivery_rider_id UUID REFERENCES profiles(id),
    
    -- Type et statut
    delivery_type delivery_type NOT NULL DEFAULT 'food',
    status order_status NOT NULL DEFAULT 'draft',
    
    -- Adresses
    pickup_address TEXT NOT NULL,
    pickup_location GEOMETRY(POINT, 4326) NOT NULL,
    delivery_address_id UUID REFERENCES addresses(id),
    delivery_address TEXT NOT NULL,
    delivery_location GEOMETRY(POINT, 4326) NOT NULL,
    delivery_zone_id UUID REFERENCES delivery_zones(id),
    
    -- Instructions
    pickup_instructions TEXT,
    delivery_instructions TEXT,
    special_instructions TEXT,
    
    -- Montants (en centimes pour éviter les erreurs de précision)
    subtotal_amount INTEGER NOT NULL DEFAULT 0, -- Sous-total produits
    delivery_fee INTEGER NOT NULL DEFAULT 0,    -- Frais de livraison
    service_fee INTEGER NOT NULL DEFAULT 0,     -- Frais de service
    tax_amount INTEGER NOT NULL DEFAULT 0,      -- Taxes
    discount_amount INTEGER NOT NULL DEFAULT 0, -- Remises
    tip_amount INTEGER NOT NULL DEFAULT 0,      -- Pourboire
    total_amount INTEGER NOT NULL DEFAULT 0,    -- Total final
    currency_code VARCHAR(3) NOT NULL DEFAULT 'XOF',
    
    -- Timing
    estimated_pickup_time TIMESTAMP WITH TIME ZONE,
    estimated_delivery_time TIMESTAMP WITH TIME ZONE,
    actual_pickup_time TIMESTAMP WITH TIME ZONE,
    actual_delivery_time TIMESTAMP WITH TIME ZONE,
    
    -- Métadonnées
    customer_notes TEXT,
    merchant_notes TEXT,
    rider_notes TEXT,
    admin_notes TEXT,
    
    -- Tracking
    tracking_code VARCHAR(50) UNIQUE,
    
    -- Offline support
    created_offline BOOLEAN NOT NULL DEFAULT false,
    sync_status VARCHAR(20) NOT NULL DEFAULT 'synced', -- 'synced', 'pending', 'failed'
    
    -- Timestamps
    confirmed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des articles de commande
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    
    -- Snapshot des données produit au moment de la commande
    product_name VARCHAR(200) NOT NULL,
    product_description TEXT,
    product_image_url TEXT,
    
    -- Prix et quantité
    unit_price INTEGER NOT NULL, -- Prix unitaire en centimes
    quantity INTEGER NOT NULL DEFAULT 1,
    total_price INTEGER NOT NULL, -- Prix total ligne
    
    -- Variantes et options sélectionnées
    selected_options JSONB DEFAULT '{}',
    
    -- Instructions spéciales pour cet article
    special_instructions TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des livreurs
CREATE TABLE delivery_riders (
    id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Informations personnelles
    license_number VARCHAR(50),
    license_expiry_date DATE,
    
    -- Véhicule
    vehicle_type vehicle_type NOT NULL DEFAULT 'motorcycle',
    vehicle_make VARCHAR(50),
    vehicle_model VARCHAR(50),
    vehicle_year INTEGER,
    vehicle_plate_number VARCHAR(20),
    vehicle_color VARCHAR(30),
    
    -- Zone de service
    service_city_id UUID NOT NULL REFERENCES cities(id),
    service_zones UUID[] DEFAULT '{}', -- Array des zones de livraison
    
    -- Statut et disponibilité
    current_status rider_status NOT NULL DEFAULT 'offline',
    current_location GEOMETRY(POINT, 4326),
    last_location_update TIMESTAMP WITH TIME ZONE,
    
    -- Performance
    total_deliveries INTEGER NOT NULL DEFAULT 0,
    successful_deliveries INTEGER NOT NULL DEFAULT 0,
    average_rating DECIMAL(3, 2) DEFAULT 0,
    total_ratings INTEGER DEFAULT 0,
    
    -- Finances
    earnings_balance INTEGER NOT NULL DEFAULT 0, -- En centimes
    pending_earnings INTEGER NOT NULL DEFAULT 0,
    
    -- Vérifications
    background_check_status VARCHAR(20) DEFAULT 'pending',
    background_check_date DATE,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Préférences
    max_delivery_distance_km INTEGER DEFAULT 15,
    preferred_delivery_types delivery_type[] DEFAULT ARRAY['food'],
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table de tracking des livraisons
CREATE TABLE delivery_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    rider_id UUID REFERENCES delivery_riders(id),
    
    -- Position et timing
    location GEOMETRY(POINT, 4326) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Statut et événement
    status order_status NOT NULL,
    event_type VARCHAR(50) NOT NULL, -- 'status_change', 'location_update', 'note_added'
    
    -- Métadonnées
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Estimation temps restant en minutes
    estimated_time_remaining INTEGER,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des évaluations
CREATE TABLE ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id),
    
    -- Qui évalue qui
    rater_id UUID NOT NULL REFERENCES profiles(id), -- Celui qui note
    rated_id UUID NOT NULL REFERENCES profiles(id), -- Celui qui est noté
    rated_type VARCHAR(20) NOT NULL, -- 'merchant', 'rider', 'customer'
    
    -- Évaluation
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    
    -- Critères détaillés (optionnel)
    criteria_scores JSONB DEFAULT '{}', -- Ex: {"food_quality": 5, "delivery_time": 4}
    
    -- Métadonnées
    is_anonymous BOOLEAN NOT NULL DEFAULT false,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Un utilisateur ne peut noter qu'une fois par commande
    UNIQUE(order_id, rater_id, rated_id)
);

-- Table des promotions et codes promo
CREATE TABLE promotions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    merchant_id UUID REFERENCES merchants(id), -- NULL = promotion globale
    
    -- Informations de base
    name VARCHAR(200) NOT NULL,
    description TEXT,
    code VARCHAR(50) UNIQUE, -- NULL = promotion automatique
    
    -- Type de promotion
    discount_type VARCHAR(20) NOT NULL, -- 'percentage', 'fixed_amount', 'free_delivery'
    discount_value INTEGER NOT NULL, -- Pourcentage ou montant en centimes
    
    -- Conditions
    min_order_amount INTEGER DEFAULT 0,
    max_discount_amount INTEGER, -- Plafond de remise
    max_uses_total INTEGER, -- Nombre total d'utilisations
    max_uses_per_user INTEGER DEFAULT 1,
    
    -- Validité
    valid_from TIMESTAMP WITH TIME ZONE NOT NULL,
    valid_until TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Restrictions
    applicable_delivery_types delivery_type[] DEFAULT ARRAY['food'],
    applicable_cities UUID[], -- Array des villes concernées
    first_order_only BOOLEAN NOT NULL DEFAULT false,
    
    -- Statut
    is_active BOOLEAN NOT NULL DEFAULT true,
    current_uses INTEGER NOT NULL DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table d'utilisation des promotions
CREATE TABLE promotion_uses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    promotion_id UUID NOT NULL REFERENCES promotions(id),
    order_id UUID NOT NULL REFERENCES orders(id),
    user_id UUID NOT NULL REFERENCES profiles(id),
    
    discount_amount INTEGER NOT NULL, -- Montant de la remise appliquée
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Un code promo ne peut être utilisé qu'une fois par commande
    UNIQUE(promotion_id, order_id)
);

-- Index pour optimiser les performances
CREATE INDEX idx_orders_customer ON orders(customer_id);
CREATE INDEX idx_orders_merchant ON orders(merchant_id);
CREATE INDEX idx_orders_rider ON orders(delivery_rider_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_delivery_type ON orders(delivery_type);
CREATE INDEX idx_orders_zone ON orders(delivery_zone_id);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_number ON orders(order_number);
CREATE INDEX idx_orders_tracking ON orders(tracking_code);
CREATE INDEX idx_orders_pickup_location ON orders USING GIST(pickup_location);
CREATE INDEX idx_orders_delivery_location ON orders USING GIST(delivery_location);

CREATE INDEX idx_order_items_order ON order_items(order_id);
CREATE INDEX idx_order_items_product ON order_items(product_id);

CREATE INDEX idx_delivery_riders_city ON delivery_riders(service_city_id);
CREATE INDEX idx_delivery_riders_status ON delivery_riders(current_status);
CREATE INDEX idx_delivery_riders_location ON delivery_riders USING GIST(current_location);
CREATE INDEX idx_delivery_riders_active ON delivery_riders(is_active);
CREATE INDEX idx_delivery_riders_verified ON delivery_riders(is_verified);
CREATE INDEX idx_delivery_riders_rating ON delivery_riders(average_rating);

CREATE INDEX idx_delivery_tracking_order ON delivery_tracking(order_id);
CREATE INDEX idx_delivery_tracking_rider ON delivery_tracking(rider_id);
CREATE INDEX idx_delivery_tracking_timestamp ON delivery_tracking(timestamp);
CREATE INDEX idx_delivery_tracking_location ON delivery_tracking USING GIST(location);

CREATE INDEX idx_ratings_order ON ratings(order_id);
CREATE INDEX idx_ratings_rater ON ratings(rater_id);
CREATE INDEX idx_ratings_rated ON ratings(rated_id, rated_type);
CREATE INDEX idx_ratings_rating ON ratings(rating);

CREATE INDEX idx_promotions_code ON promotions(code) WHERE code IS NOT NULL;
CREATE INDEX idx_promotions_merchant ON promotions(merchant_id);
CREATE INDEX idx_promotions_active ON promotions(is_active);
CREATE INDEX idx_promotions_validity ON promotions(valid_from, valid_until);

CREATE INDEX idx_promotion_uses_promotion ON promotion_uses(promotion_id);
CREATE INDEX idx_promotion_uses_user ON promotion_uses(user_id);
CREATE INDEX idx_promotion_uses_order ON promotion_uses(order_id);

-- Contraintes de validation
ALTER TABLE orders ADD CONSTRAINT chk_amounts_positive 
    CHECK (subtotal_amount >= 0 AND delivery_fee >= 0 AND service_fee >= 0 
           AND tax_amount >= 0 AND discount_amount >= 0 AND tip_amount >= 0 
           AND total_amount >= 0);

ALTER TABLE orders ADD CONSTRAINT chk_total_calculation 
    CHECK (total_amount = subtotal_amount + delivery_fee + service_fee + tax_amount - discount_amount + tip_amount);

ALTER TABLE order_items ADD CONSTRAINT chk_quantity_positive 
    CHECK (quantity > 0);

ALTER TABLE order_items ADD CONSTRAINT chk_prices_positive 
    CHECK (unit_price > 0 AND total_price > 0);

ALTER TABLE order_items ADD CONSTRAINT chk_total_price_calculation 
    CHECK (total_price = unit_price * quantity);

ALTER TABLE delivery_riders ADD CONSTRAINT chk_max_distance 
    CHECK (max_delivery_distance_km > 0 AND max_delivery_distance_km <= 100);

ALTER TABLE delivery_riders ADD CONSTRAINT chk_deliveries_count 
    CHECK (successful_deliveries <= total_deliveries);

ALTER TABLE delivery_riders ADD CONSTRAINT chk_earnings_non_negative 
    CHECK (earnings_balance >= 0 AND pending_earnings >= 0);

ALTER TABLE promotions ADD CONSTRAINT chk_discount_value_positive 
    CHECK (discount_value > 0);

ALTER TABLE promotions ADD CONSTRAINT chk_validity_period 
    CHECK (valid_until > valid_from);

ALTER TABLE promotions ADD CONSTRAINT chk_max_uses_positive 
    CHECK (max_uses_total IS NULL OR max_uses_total > 0);

ALTER TABLE promotion_uses ADD CONSTRAINT chk_discount_amount_positive 
    CHECK (discount_amount > 0);
