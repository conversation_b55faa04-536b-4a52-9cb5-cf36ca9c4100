// =====================================================
// SERVICE DE BASE DE DONNÉES SQLITE NATIF
// =====================================================

import * as SQLite from 'expo-sqlite';
import { Platform } from 'react-native';

// Types pour les données
export interface Order {
  id: string;
  userId: string;
  status: 'pending' | 'confirmed' | 'in_transit' | 'delivered' | 'cancelled';
  items: OrderItem[];
  totalAmount: number;
  deliveryAddress: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
}

export interface DeliveryPerson {
  id: string;
  name: string;
  phone: string;
  isAvailable: boolean;
  currentLocation?: {
    latitude: number;
    longitude: number;
  };
}

// Service de base de données
export class DatabaseService {
  private static instance: DatabaseService;
  private db: SQLite.SQLiteDatabase | null = null;
  private isInitialized = false;

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // Initialiser la base de données
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized) return;

      console.log('🗄️ Initialisation de la base de données SQLite...');

      // Ouvrir la base de données
      this.db = await SQLite.openDatabaseAsync('delivery_app.db');

      // Créer les tables
      await this.createTables();
      
      this.isInitialized = true;
      console.log('✅ Base de données initialisée');
    } catch (error) {
      console.error('❌ Erreur initialisation base de données:', error);
      throw error;
    }
  }

  // Créer les tables
  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Base de données non initialisée');

    // Table des commandes
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        status TEXT NOT NULL,
        items TEXT NOT NULL,
        totalAmount REAL NOT NULL,
        deliveryAddress TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      );
    `);

    // Table des livreurs
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS delivery_persons (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phone TEXT NOT NULL,
        isAvailable INTEGER NOT NULL DEFAULT 1,
        latitude REAL,
        longitude REAL,
        updatedAt TEXT NOT NULL
      );
    `);

    // Table des paramètres utilisateur
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS user_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      );
    `);

    console.log('✅ Tables créées');
  }

  // === GESTION DES COMMANDES ===

  // Créer une commande
  async createOrder(order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> {
    if (!this.db) throw new Error('Base de données non initialisée');

    const newOrder: Order = {
      ...order,
      id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await this.db.runAsync(
      `INSERT INTO orders (id, userId, status, items, totalAmount, deliveryAddress, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        newOrder.id,
        newOrder.userId,
        newOrder.status,
        JSON.stringify(newOrder.items),
        newOrder.totalAmount,
        newOrder.deliveryAddress,
        newOrder.createdAt,
        newOrder.updatedAt,
      ]
    );

    console.log('✅ Commande créée:', newOrder.id);
    return newOrder;
  }

  // Récupérer les commandes d'un utilisateur
  async getUserOrders(userId: string): Promise<Order[]> {
    if (!this.db) throw new Error('Base de données non initialisée');

    const result = await this.db.getAllAsync(
      'SELECT * FROM orders WHERE userId = ? ORDER BY createdAt DESC',
      [userId]
    );

    return result.map((row: any) => ({
      ...row,
      items: JSON.parse(row.items),
    })) as Order[];
  }

  // Mettre à jour le statut d'une commande
  async updateOrderStatus(orderId: string, status: Order['status']): Promise<void> {
    if (!this.db) throw new Error('Base de données non initialisée');

    await this.db.runAsync(
      'UPDATE orders SET status = ?, updatedAt = ? WHERE id = ?',
      [status, new Date().toISOString(), orderId]
    );

    console.log('✅ Statut commande mis à jour:', orderId, status);
  }

  // === GESTION DES LIVREURS ===

  // Ajouter un livreur
  async addDeliveryPerson(person: Omit<DeliveryPerson, 'id'>): Promise<DeliveryPerson> {
    if (!this.db) throw new Error('Base de données non initialisée');

    const newPerson: DeliveryPerson = {
      ...person,
      id: `delivery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };

    await this.db.runAsync(
      `INSERT INTO delivery_persons (id, name, phone, isAvailable, latitude, longitude, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        newPerson.id,
        newPerson.name,
        newPerson.phone,
        newPerson.isAvailable ? 1 : 0,
        newPerson.currentLocation?.latitude || null,
        newPerson.currentLocation?.longitude || null,
        new Date().toISOString(),
      ]
    );

    console.log('✅ Livreur ajouté:', newPerson.name);
    return newPerson;
  }

  // Récupérer les livreurs disponibles
  async getAvailableDeliveryPersons(): Promise<DeliveryPerson[]> {
    if (!this.db) throw new Error('Base de données non initialisée');

    const result = await this.db.getAllAsync(
      'SELECT * FROM delivery_persons WHERE isAvailable = 1'
    );

    return result.map((row: any) => ({
      id: row.id,
      name: row.name,
      phone: row.phone,
      isAvailable: row.isAvailable === 1,
      currentLocation: row.latitude && row.longitude ? {
        latitude: row.latitude,
        longitude: row.longitude,
      } : undefined,
    })) as DeliveryPerson[];
  }

  // === GESTION DES PARAMÈTRES ===

  // Sauvegarder un paramètre
  async setSetting(key: string, value: string): Promise<void> {
    if (!this.db) throw new Error('Base de données non initialisée');

    await this.db.runAsync(
      `INSERT OR REPLACE INTO user_settings (key, value, updatedAt)
       VALUES (?, ?, ?)`,
      [key, value, new Date().toISOString()]
    );
  }

  // Récupérer un paramètre
  async getSetting(key: string): Promise<string | null> {
    if (!this.db) throw new Error('Base de données non initialisée');

    const result = await this.db.getFirstAsync(
      'SELECT value FROM user_settings WHERE key = ?',
      [key]
    ) as { value: string } | null;

    return result?.value || null;
  }

  // === UTILITAIRES ===

  // Nettoyer les anciennes données
  async cleanup(): Promise<void> {
    if (!this.db) throw new Error('Base de données non initialisée');

    // Supprimer les commandes de plus de 30 jours
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
    
    await this.db.runAsync(
      'DELETE FROM orders WHERE createdAt < ? AND status IN (?, ?)',
      [thirtyDaysAgo, 'delivered', 'cancelled']
    );

    console.log('✅ Nettoyage de la base de données effectué');
  }

  // Obtenir des statistiques
  async getStats(): Promise<{
    totalOrders: number;
    pendingOrders: number;
    deliveredOrders: number;
    availableDeliveryPersons: number;
  }> {
    if (!this.db) throw new Error('Base de données non initialisée');

    const totalOrders = await this.db.getFirstAsync(
      'SELECT COUNT(*) as count FROM orders'
    ) as { count: number };

    const pendingOrders = await this.db.getFirstAsync(
      'SELECT COUNT(*) as count FROM orders WHERE status = ?',
      ['pending']
    ) as { count: number };

    const deliveredOrders = await this.db.getFirstAsync(
      'SELECT COUNT(*) as count FROM orders WHERE status = ?',
      ['delivered']
    ) as { count: number };

    const availableDeliveryPersons = await this.db.getFirstAsync(
      'SELECT COUNT(*) as count FROM delivery_persons WHERE isAvailable = 1'
    ) as { count: number };

    return {
      totalOrders: totalOrders.count,
      pendingOrders: pendingOrders.count,
      deliveredOrders: deliveredOrders.count,
      availableDeliveryPersons: availableDeliveryPersons.count,
    };
  }
}

// Instance singleton
export const databaseService = DatabaseService.getInstance();
export default databaseService;
