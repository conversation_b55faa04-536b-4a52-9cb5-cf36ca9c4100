{"version": 3, "file": "scheduleNotificationAsync.js", "sourceRoot": "", "sources": ["../src/scheduleNotificationAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAExE,OAAO,qBAAqB,MAAM,yBAAyB,CAAC;AAW5D,OAAO,EAGL,4BAA4B,GAC7B,MAAM,uBAAuB,CAAC;AAE/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2DG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,yBAAyB,CACrD,OAAiC;IAEjC,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,EAAE,CAAC;QACrD,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,2BAA2B,CAAC,CAAC;IAC9E,CAAC;IAED,OAAO,MAAM,qBAAqB,CAAC,yBAAyB,CAC1D,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,EAAE,EAAE,EAC/B,OAAO,CAAC,OAAO,EACf,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAC9B,CAAC;AACJ,CAAC;AAID,MAAM,UAAU,YAAY,CAC1B,iBAA2C;IAE3C,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;QACpC,MAAM,IAAI,SAAS,CACjB,yIAAyI,CAC1I,CAAC;IACJ,CAAC;IAED,MAAM,WAAW,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;IACxD,IAAI,WAAW,EAAE,CAAC;QAChB,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,MAAM,eAAe,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;IAChE,IAAI,eAAe,EAAE,CAAC;QACpB,OAAO,eAAe,CAAC;IACzB,CAAC;IACD,MAAM,YAAY,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;IAC1D,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,YAAY,CAAC;IACtB,CAAC;IACD,MAAM,aAAa,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;IAC5D,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,MAAM,cAAc,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;IAC9D,IAAI,cAAc,EAAE,CAAC;QACnB,OAAO,cAAc,CAAC;IACxB,CAAC;IACD,MAAM,aAAa,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;IAC5D,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,MAAM,mBAAmB,GAAG,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;IACxE,IAAI,mBAAmB,EAAE,CAAC;QACxB,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IACD,OAAO,QAAQ,CAAC,MAAM,CAAC;QACrB,OAAO,EAAE,IAAI,EAAE,iEAAiE;QAChF,OAAO,EAAE;YACP,IAAI,EAAE,SAAS;YACf,SAAS,EACP,OAAO,iBAAiB,KAAK,QAAQ;gBACrC,iBAAiB,KAAK,IAAI;gBAC1B,CAAC,CAAC,iBAAiB,YAAY,IAAI,CAAC;gBAClC,CAAC,CAAC,iBAAiB,EAAE,SAAS;gBAC9B,CAAC,CAAC,SAAS;SAChB;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,oBAAoB,CAC3B,OAAiC;IAEjC,IACE,OAAO,KAAK,IAAI;QAChB,OAAO,OAAO,KAAK,QAAQ;QAC3B,MAAM,IAAI,OAAO;QACjB,OAAO,CAAC,IAAI,KAAK,4BAA4B,CAAC,QAAQ,EACtD,CAAC;QACD,MAAM,EAAE,OAAO,EAAE,GAAG,eAAe,EAAE,GAAG,OAAO,CAAC;QAChD,OAAO,EAAE,GAAG,eAAe,EAAE,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;IACtE,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAiC;IACzD,IAAI,OAAO,YAAY,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC3D,8DAA8D;QAC9D,iFAAiF;QACjF,OAAO,CAAC,IAAI,CACV,8CAA8C,OAAO,kFAAkF,CACxI,CAAC;QACF,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;IAC3D,CAAC;SAAM,IACL,OAAO,OAAO,KAAK,QAAQ;QAC3B,OAAO,KAAK,IAAI;QAChB,MAAM,IAAI,OAAO;QACjB,OAAO,CAAC,IAAI,KAAK,4BAA4B,CAAC,IAAI;QAClD,MAAM,IAAI,OAAO,EACjB,CAAC;QACD,MAAM,MAAM,GAA2B;YACrC,IAAI,EAAE,MAAM;YACZ,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;SACrC,CAAC;QACF,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;SAAM,CAAC;QACN,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAS,WAAW,CAAC,IAAmB;IACtC,IAAI,IAAI,YAAY,IAAI,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,iBAAiB,CAAC,OAAiC;IAC1D,IACE,OAAO,KAAK,IAAI;QAChB,OAAO,OAAO,KAAK,QAAQ;QAC3B,MAAM,IAAI,OAAO;QACjB,OAAO,CAAC,IAAI,KAAK,4BAA4B,CAAC,KAAK,EACnD,CAAC;QACD,+BAA+B,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC7D,MAAM,MAAM,GAA4B;YACtC,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,6BAA6B;YACnD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,6BAA6B;SACxD,CAAC;QACF,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,kBAAkB,CACzB,OAAiC;IAEjC,IACE,OAAO,KAAK,IAAI;QAChB,OAAO,OAAO,KAAK,QAAQ;QAC3B,MAAM,IAAI,OAAO;QACjB,OAAO,CAAC,IAAI,KAAK,4BAA4B,CAAC,MAAM,EACpD,CAAC;QACD,+BAA+B,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;QACxE,MAAM,MAAM,GAA6B;YACvC,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,6BAA6B;YACzD,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,6BAA6B;YACnD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,6BAA6B;SACxD,CAAC;QACF,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAAiC;IAEjC,IACE,OAAO,KAAK,IAAI;QAChB,OAAO,OAAO,KAAK,QAAQ;QAC3B,MAAM,IAAI,OAAO;QACjB,OAAO,CAAC,IAAI,KAAK,4BAA4B,CAAC,OAAO,EACrD,CAAC;QACD,+BAA+B,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;QACpE,MAAM,MAAM,GAA8B;YACxC,IAAI,EAAE,SAAS;YACf,GAAG,EAAE,OAAO,CAAC,GAAG,IAAI,6BAA6B;YACjD,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,6BAA6B;YACnD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,6BAA6B;SACxD,CAAC;QACF,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,kBAAkB,CACzB,OAAiC;IAEjC,IACE,OAAO,KAAK,IAAI;QAChB,OAAO,OAAO,KAAK,QAAQ;QAC3B,MAAM,IAAI,OAAO;QACjB,OAAO,CAAC,IAAI,KAAK,4BAA4B,CAAC,MAAM,EACpD,CAAC;QACD,+BAA+B,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC7E,MAAM,MAAM,GAA6B;YACvC,IAAI,EAAE,QAAQ;YACd,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,6BAA6B;YACrD,GAAG,EAAE,OAAO,CAAC,GAAG,IAAI,6BAA6B;YACjD,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,6BAA6B;YACnD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,6BAA6B;SACxD,CAAC;QACF,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,wBAAwB,CAC/B,OAAiC;IAEjC,IACE,OAAO,KAAK,IAAI;QAChB,OAAO,OAAO,KAAK,QAAQ;QAC3B,MAAM,IAAI,OAAO;QACjB,OAAO,CAAC,IAAI,KAAK,4BAA4B,CAAC,aAAa;QAC3D,SAAS,IAAI,OAAO;QACpB,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EACnC,CAAC;QACD,MAAM,MAAM,GAAmC;YAC7C,IAAI,EAAE,cAAc;YACpB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;SAClC,CAAC;QACF,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACvC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,wEAAwE;AACxE,MAAM,6BAA6B,GAAG,CAAC,IAAI,CAAC;AAE5C,SAAS,+BAA+B,CACtC,OAA8C,EAC9C,UAAiD;IAEjD,MAAM,cAAc,GAAG,OAAc,CAAC;IACtC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;QAC/B,IAAI,CAAC,CAAC,SAAS,IAAI,cAAc,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,SAAS,CAAC,OAAO,SAAS,gCAAgC,CAAC,CAAC;QACxE,CAAC;QACD,IAAI,OAAO,cAAc,CAAC,SAAS,CAAC,KAAK,QAAQ,EAAE,CAAC;YAClD,MAAM,IAAI,SAAS,CAAC,OAAO,SAAS,+BAA+B,CAAC,CAAC;QACvE,CAAC;QACD,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,OAAO,CAAC,CAAC,CAAC;gBACb,MAAM,EAAE,KAAK,EAAE,GAAG,cAAc,CAAC;gBACjC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;oBAC5B,MAAM,IAAI,UAAU,CAAC,4DAA4D,KAAK,EAAE,CAAC,CAAC;gBAC5F,CAAC;gBACD,MAAM;YACR,CAAC;YACD,KAAK,KAAK,CAAC,CAAC,CAAC;gBACX,MAAM,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;gBAC/B,MAAM,KAAK,GACT,cAAc,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;gBACpF,MAAM,gBAAgB,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;gBAC5C,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,gBAAgB,EAAE,CAAC;oBACtC,MAAM,IAAI,UAAU,CAClB,+BAA+B,KAAK,0BAA0B,gBAAgB,YAAY,GAAG,EAAE,CAChG,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,CAAC;YACD,KAAK,SAAS,CAAC,CAAC,CAAC;gBACf,MAAM,EAAE,OAAO,EAAE,GAAG,cAAc,CAAC;gBACnC,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;oBAC/B,MAAM,IAAI,UAAU,CAClB,6DAA6D,OAAO,EAAE,CACvE,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,CAAC;YACD,KAAK,MAAM,CAAC,CAAC,CAAC;gBACZ,MAAM,EAAE,IAAI,EAAE,GAAG,cAAc,CAAC;gBAChC,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;oBAC1B,MAAM,IAAI,UAAU,CAAC,2DAA2D,IAAI,EAAE,CAAC,CAAC;gBAC1F,CAAC;gBACD,MAAM;YACR,CAAC;YACD,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;gBAClC,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,EAAE,CAAC;oBAC9B,MAAM,IAAI,UAAU,CAClB,6DAA6D,MAAM,EAAE,CACtE,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,SAAS,WAAW,CAAC,QAAgB,CAAC,EAAE,IAAa;IACnD,OAAO,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACxD,CAAC", "sourcesContent": ["import { Platform, UnavailabilityError, uuid } from 'expo-modules-core';\n\nimport NotificationScheduler from './NotificationScheduler';\nimport {\n  NativeCalendarTriggerInput,\n  NativeDailyTriggerInput,\n  NativeDateTriggerInput,\n  NativeNotificationTriggerInput,\n  NativeTimeIntervalTriggerInput,\n  NativeWeeklyTriggerInput,\n  NativeMonthlyTriggerInput,\n  NativeYearlyTriggerInput,\n} from './NotificationScheduler.types';\nimport {\n  NotificationRequestInput,\n  NotificationTriggerInput,\n  SchedulableTriggerInputTypes,\n} from './Notifications.types';\n\n/**\n * Schedules a notification to be triggered in the future.\n * > **Note:** Please note that this does not mean that the notification will be presented when it is triggered.\n * For the notification to be presented you have to set a notification handler with [`setNotificationHandler`](#setnotificationhandlerhandler)\n * that will return an appropriate notification behavior. For more information see the example below.\n * @param request An object describing the notification to be triggered.\n * @return Returns a Promise resolving to a string which is a notification identifier you can later use to cancel the notification or to identify an incoming notification.\n * @example\n * # Schedule the notification that will trigger once, in one minute from now\n * ```ts\n * import * as Notifications from 'expo-notifications';\n *\n * Notifications.scheduleNotificationAsync({\n *   content: {\n *     title: \"Time's up!\",\n *     body: 'Change sides!',\n *   },\n *   trigger: {\n *     type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,\n *     seconds: 60,\n *   },\n * });\n * ```\n *\n * # Schedule the notification that will trigger repeatedly, every 20 minutes\n * ```ts\n * import * as Notifications from 'expo-notifications';\n *\n * Notifications.scheduleNotificationAsync({\n *   content: {\n *     title: 'Remember to drink water!',\n *   },\n *   trigger: {\n *     type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,\n *     seconds: 60 * 20,\n *     repeats: true,\n *   },\n * });\n * ```\n *\n * # Schedule the notification that will trigger once, at the beginning of next hour\n * ```ts\n * import * as Notifications from 'expo-notifications';\n *\n * const date = new Date(Date.now() + 60 * 60 * 1000);\n * date.setMinutes(0);\n * date.setSeconds(0);\n *\n * Notifications.scheduleNotificationAsync({\n *   content: {\n *     title: 'Happy new hour!',\n *   },\n *   trigger: {\n *     type: Notifications.SchedulableTriggerInputTypes.DATE,\n *     date\n *   },\n * });\n * ```\n * @header schedule\n */\nexport default async function scheduleNotificationAsync(\n  request: NotificationRequestInput\n): Promise<string> {\n  if (!NotificationScheduler.scheduleNotificationAsync) {\n    throw new UnavailabilityError('Notifications', 'scheduleNotificationAsync');\n  }\n\n  return await NotificationScheduler.scheduleNotificationAsync(\n    request.identifier ?? uuid.v4(),\n    request.content,\n    parseTrigger(request.trigger)\n  );\n}\n\ntype ValidTriggerDateComponents = 'month' | 'day' | 'weekday' | 'hour' | 'minute';\n\nexport function parseTrigger(\n  userFacingTrigger: NotificationTriggerInput\n): NativeNotificationTriggerInput {\n  if (userFacingTrigger === null) {\n    return null;\n  }\n\n  if (userFacingTrigger === undefined) {\n    throw new TypeError(\n      'Encountered an `undefined` notification trigger. If you want to trigger the notification immediately, pass in an explicit `null` value.'\n    );\n  }\n\n  const dateTrigger = parseDateTrigger(userFacingTrigger);\n  if (dateTrigger) {\n    return dateTrigger;\n  }\n  const calendarTrigger = parseCalendarTrigger(userFacingTrigger);\n  if (calendarTrigger) {\n    return calendarTrigger;\n  }\n  const dailyTrigger = parseDailyTrigger(userFacingTrigger);\n  if (dailyTrigger) {\n    return dailyTrigger;\n  }\n  const weeklyTrigger = parseWeeklyTrigger(userFacingTrigger);\n  if (weeklyTrigger) {\n    return weeklyTrigger;\n  }\n  const monthlyTrigger = parseMonthlyTrigger(userFacingTrigger);\n  if (monthlyTrigger) {\n    return monthlyTrigger;\n  }\n  const yearlyTrigger = parseYearlyTrigger(userFacingTrigger);\n  if (yearlyTrigger) {\n    return yearlyTrigger;\n  }\n  const timeIntervalTrigger = parseTimeIntervalTrigger(userFacingTrigger);\n  if (timeIntervalTrigger) {\n    return timeIntervalTrigger;\n  }\n  return Platform.select({\n    default: null, // There's no notion of channels on platforms other than Android.\n    android: {\n      type: 'channel',\n      channelId:\n        typeof userFacingTrigger === 'object' &&\n        userFacingTrigger !== null &&\n        !(userFacingTrigger instanceof Date)\n          ? userFacingTrigger?.channelId\n          : undefined,\n    },\n  });\n}\n\nfunction parseCalendarTrigger(\n  trigger: NotificationTriggerInput\n): NativeCalendarTriggerInput | undefined {\n  if (\n    trigger !== null &&\n    typeof trigger === 'object' &&\n    'type' in trigger &&\n    trigger.type === SchedulableTriggerInputTypes.CALENDAR\n  ) {\n    const { repeats, ...calendarTrigger } = trigger;\n    return { ...calendarTrigger, repeats: !!repeats, type: 'calendar' };\n  }\n  return undefined;\n}\n\nfunction parseDateTrigger(trigger: NotificationTriggerInput): NativeDateTriggerInput | undefined {\n  if (trigger instanceof Date || typeof trigger === 'number') {\n    // TODO @vonovak this branch is not be used by people using TS\n    // but was part of the public api previously so we keep it for a bit for JS users\n    console.warn(\n      `You are using a deprecated parameter type (${trigger}) for the notification trigger. Use \"{ type: 'date', date: someValue }\" instead.`\n    );\n    return { type: 'date', timestamp: toTimestamp(trigger) };\n  } else if (\n    typeof trigger === 'object' &&\n    trigger !== null &&\n    'type' in trigger &&\n    trigger.type === SchedulableTriggerInputTypes.DATE &&\n    'date' in trigger\n  ) {\n    const result: NativeDateTriggerInput = {\n      type: 'date',\n      timestamp: toTimestamp(trigger.date),\n    };\n    if (trigger.channelId) {\n      result.channelId = trigger.channelId;\n    }\n    return result;\n  } else {\n    return undefined;\n  }\n}\n\nfunction toTimestamp(date: number | Date) {\n  if (date instanceof Date) {\n    return date.getTime();\n  }\n  return date;\n}\n\nfunction parseDailyTrigger(trigger: NotificationTriggerInput): NativeDailyTriggerInput | undefined {\n  if (\n    trigger !== null &&\n    typeof trigger === 'object' &&\n    'type' in trigger &&\n    trigger.type === SchedulableTriggerInputTypes.DAILY\n  ) {\n    validateDateComponentsInTrigger(trigger, ['hour', 'minute']);\n    const result: NativeDailyTriggerInput = {\n      type: 'daily',\n      hour: trigger.hour ?? placeholderDateComponentValue,\n      minute: trigger.minute ?? placeholderDateComponentValue,\n    };\n    if (trigger.channelId) {\n      result.channelId = trigger.channelId;\n    }\n    return result;\n  }\n  return undefined;\n}\n\nfunction parseWeeklyTrigger(\n  trigger: NotificationTriggerInput\n): NativeWeeklyTriggerInput | undefined {\n  if (\n    trigger !== null &&\n    typeof trigger === 'object' &&\n    'type' in trigger &&\n    trigger.type === SchedulableTriggerInputTypes.WEEKLY\n  ) {\n    validateDateComponentsInTrigger(trigger, ['weekday', 'hour', 'minute']);\n    const result: NativeWeeklyTriggerInput = {\n      type: 'weekly',\n      weekday: trigger.weekday ?? placeholderDateComponentValue,\n      hour: trigger.hour ?? placeholderDateComponentValue,\n      minute: trigger.minute ?? placeholderDateComponentValue,\n    };\n    if (trigger.channelId) {\n      result.channelId = trigger.channelId;\n    }\n    return result;\n  }\n  return undefined;\n}\n\nfunction parseMonthlyTrigger(\n  trigger: NotificationTriggerInput\n): NativeMonthlyTriggerInput | undefined {\n  if (\n    trigger !== null &&\n    typeof trigger === 'object' &&\n    'type' in trigger &&\n    trigger.type === SchedulableTriggerInputTypes.MONTHLY\n  ) {\n    validateDateComponentsInTrigger(trigger, ['day', 'hour', 'minute']);\n    const result: NativeMonthlyTriggerInput = {\n      type: 'monthly',\n      day: trigger.day ?? placeholderDateComponentValue,\n      hour: trigger.hour ?? placeholderDateComponentValue,\n      minute: trigger.minute ?? placeholderDateComponentValue,\n    };\n    if (trigger.channelId) {\n      result.channelId = trigger.channelId;\n    }\n    return result;\n  }\n  return undefined;\n}\n\nfunction parseYearlyTrigger(\n  trigger: NotificationTriggerInput\n): NativeYearlyTriggerInput | undefined {\n  if (\n    trigger !== null &&\n    typeof trigger === 'object' &&\n    'type' in trigger &&\n    trigger.type === SchedulableTriggerInputTypes.YEARLY\n  ) {\n    validateDateComponentsInTrigger(trigger, ['month', 'day', 'hour', 'minute']);\n    const result: NativeYearlyTriggerInput = {\n      type: 'yearly',\n      month: trigger.month ?? placeholderDateComponentValue,\n      day: trigger.day ?? placeholderDateComponentValue,\n      hour: trigger.hour ?? placeholderDateComponentValue,\n      minute: trigger.minute ?? placeholderDateComponentValue,\n    };\n    if (trigger.channelId) {\n      result.channelId = trigger.channelId;\n    }\n    return result;\n  }\n  return undefined;\n}\n\nfunction parseTimeIntervalTrigger(\n  trigger: NotificationTriggerInput\n): NativeTimeIntervalTriggerInput | undefined {\n  if (\n    trigger !== null &&\n    typeof trigger === 'object' &&\n    'type' in trigger &&\n    trigger.type === SchedulableTriggerInputTypes.TIME_INTERVAL &&\n    'seconds' in trigger &&\n    typeof trigger.seconds === 'number'\n  ) {\n    const result: NativeTimeIntervalTriggerInput = {\n      type: 'timeInterval',\n      seconds: trigger.seconds,\n      repeats: trigger.repeats ?? false,\n    };\n    if (trigger.channelId) {\n      result.channelId = trigger.channelId;\n    }\n    return result;\n  }\n  return undefined;\n}\n\n// Needed only to satisfy Typescript types for validated date components\nconst placeholderDateComponentValue = -9999;\n\nfunction validateDateComponentsInTrigger(\n  trigger: NonNullable<NotificationTriggerInput>,\n  components: readonly ValidTriggerDateComponents[]\n) {\n  const anyTriggerType = trigger as any;\n  components.forEach((component) => {\n    if (!(component in anyTriggerType)) {\n      throw new TypeError(`The ${component} parameter needs to be present`);\n    }\n    if (typeof anyTriggerType[component] !== 'number') {\n      throw new TypeError(`The ${component} parameter should be a number`);\n    }\n    switch (component) {\n      case 'month': {\n        const { month } = anyTriggerType;\n        if (month < 0 || month > 11) {\n          throw new RangeError(`The month parameter needs to be between 0 and 11. Found: ${month}`);\n        }\n        break;\n      }\n      case 'day': {\n        const day = anyTriggerType.day;\n        const month =\n          anyTriggerType.month !== undefined ? anyTriggerType.month : new Date().getMonth();\n        const daysInGivenMonth = daysInMonth(month);\n        if (day < 1 || day > daysInGivenMonth) {\n          throw new RangeError(\n            `The day parameter for month ${month} must be between 1 and ${daysInGivenMonth}. Found: ${day}`\n          );\n        }\n        break;\n      }\n      case 'weekday': {\n        const { weekday } = anyTriggerType;\n        if (weekday < 1 || weekday > 7) {\n          throw new RangeError(\n            `The weekday parameter needs to be between 1 and 7. Found: ${weekday}`\n          );\n        }\n        break;\n      }\n      case 'hour': {\n        const { hour } = anyTriggerType;\n        if (hour < 0 || hour > 23) {\n          throw new RangeError(`The hour parameter needs to be between 0 and 23. Found: ${hour}`);\n        }\n        break;\n      }\n      case 'minute': {\n        const { minute } = anyTriggerType;\n        if (minute < 0 || minute > 59) {\n          throw new RangeError(\n            `The minute parameter needs to be between 0 and 59. Found: ${minute}`\n          );\n        }\n        break;\n      }\n    }\n  });\n}\n\n/**\n * Determines the number of days in the given month (or January if omitted).\n * If year is specified, it will include leap year logic, else it will always assume a leap year\n */\nfunction daysInMonth(month: number = 0, year?: number) {\n  return new Date(year ?? 2000, month + 1, 0).getDate();\n}\n"]}