{"version": 3, "file": "setNotificationChannelGroupAsync.js", "sourceRoot": "", "sources": ["../src/setNotificationChannelGroupAsync.ts"], "names": [], "mappings": "AAKA;;;;;;;;GAQG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,gCAAgC,CAC5D,OAAe,EACf,KAAoC;IAEpC,OAAO,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;IAC7E,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import {\n  NotificationChannelGroup,\n  NotificationChannelGroupInput,\n} from './NotificationChannelGroupManager.types';\n\n/**\n * Assigns the channel group configuration to a channel group of a specified name (creating it if need be).\n * @param groupId The channel group's identifier.\n * @param group Object representing the channel group configuration.\n * @return A `Promise` resolving to the object (of type [`NotificationChannelGroup`](#notificationchannelgroup))\n * describing the modified channel group or to `null` if the platform does not support notification channels.\n * @platform android\n * @header channels\n */\nexport default async function setNotificationChannelGroupAsync(\n  groupId: string,\n  group: NotificationChannelGroupInput\n): Promise<NotificationChannelGroup | null> {\n  console.debug('Notification channels feature is only supported on Android.');\n  return null;\n}\n"]}