// =====================================================
// CONFIGURATION WEBHOOKS PAIEMENTS
// =====================================================

import { createClient } from '@supabase/supabase-js'

// Types pour les configurations de webhooks
interface WebhookConfig {
  provider: string
  endpoint_url: string
  secret_key: string
  events: string[]
  headers?: Record<string, string>
  retry_config?: {
    max_retries: number
    retry_delay_ms: number
    backoff_multiplier: number
  }
}

interface PaymentProvider {
  name: string
  type: 'mobile_money' | 'card' | 'bank_transfer'
  countries: string[]
  webhook_config: WebhookConfig
  api_config: {
    base_url: string
    auth_type: 'api_key' | 'oauth' | 'basic'
    credentials: Record<string, string>
  }
}

// =====================================================
// CONFIGURATIONS DES PROVIDERS
// =====================================================

export const PaymentProviders: PaymentProvider[] = [
  // M-Pesa (Kenya, Tanzanie)
  {
    name: 'mpesa',
    type: 'mobile_money',
    countries: ['KEN', 'TZA'],
    webhook_config: {
      provider: 'mpesa',
      endpoint_url: `${process.env.SUPABASE_URL}/functions/v1/payment-webhook/mpesa`,
      secret_key: process.env.MPESA_WEBHOOK_SECRET || '',
      events: ['payment.completed', 'payment.failed', 'payment.cancelled'],
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DeliveryApp-Webhook/1.0'
      },
      retry_config: {
        max_retries: 3,
        retry_delay_ms: 5000,
        backoff_multiplier: 2
      }
    },
    api_config: {
      base_url: 'https://sandbox.safaricom.co.ke', // Production: https://api.safaricom.co.ke
      auth_type: 'oauth',
      credentials: {
        consumer_key: process.env.MPESA_CONSUMER_KEY || '',
        consumer_secret: process.env.MPESA_CONSUMER_SECRET || '',
        shortcode: process.env.MPESA_SHORTCODE || '',
        passkey: process.env.MPESA_PASSKEY || ''
      }
    }
  },

  // Orange Money (Multi-pays Afrique)
  {
    name: 'orange_money',
    type: 'mobile_money',
    countries: ['SEN', 'CIV', 'MLI', 'BFA', 'CMR'],
    webhook_config: {
      provider: 'orange-money',
      endpoint_url: `${process.env.SUPABASE_URL}/functions/v1/payment-webhook/orange-money`,
      secret_key: process.env.ORANGE_MONEY_WEBHOOK_SECRET || '',
      events: ['payment.success', 'payment.failed', 'payment.pending'],
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.ORANGE_MONEY_API_KEY}`
      }
    },
    api_config: {
      base_url: 'https://api.orange.com/orange-money-webpay/dev/v1',
      auth_type: 'oauth',
      credentials: {
        client_id: process.env.ORANGE_MONEY_CLIENT_ID || '',
        client_secret: process.env.ORANGE_MONEY_CLIENT_SECRET || '',
        merchant_key: process.env.ORANGE_MONEY_MERCHANT_KEY || ''
      }
    }
  },

  // Wave (Sénégal, Côte d'Ivoire)
  {
    name: 'wave',
    type: 'mobile_money',
    countries: ['SEN', 'CIV'],
    webhook_config: {
      provider: 'wave',
      endpoint_url: `${process.env.SUPABASE_URL}/functions/v1/payment-webhook/wave`,
      secret_key: process.env.WAVE_WEBHOOK_SECRET || '',
      events: ['checkout.completed', 'checkout.failed'],
      headers: {
        'Content-Type': 'application/json'
      }
    },
    api_config: {
      base_url: 'https://api.wave.com/v1',
      auth_type: 'api_key',
      credentials: {
        api_key: process.env.WAVE_API_KEY || '',
        secret_key: process.env.WAVE_SECRET_KEY || ''
      }
    }
  },

  // MTN Mobile Money (Ghana, Nigeria, Ouganda)
  {
    name: 'mtn_momo',
    type: 'mobile_money',
    countries: ['GHA', 'NGA', 'UGA'],
    webhook_config: {
      provider: 'mtn-momo',
      endpoint_url: `${process.env.SUPABASE_URL}/functions/v1/payment-webhook/mtn-momo`,
      secret_key: process.env.MTN_MOMO_WEBHOOK_SECRET || '',
      events: ['payment.successful', 'payment.failed'],
      headers: {
        'Content-Type': 'application/json',
        'Ocp-Apim-Subscription-Key': process.env.MTN_MOMO_SUBSCRIPTION_KEY || ''
      }
    },
    api_config: {
      base_url: 'https://sandbox.momodeveloper.mtn.com', // Production: https://momodeveloper.mtn.com
      auth_type: 'api_key',
      credentials: {
        subscription_key: process.env.MTN_MOMO_SUBSCRIPTION_KEY || '',
        user_id: process.env.MTN_MOMO_USER_ID || '',
        api_key: process.env.MTN_MOMO_API_KEY || ''
      }
    }
  },

  // Paystack (Nigeria, Ghana, Afrique du Sud)
  {
    name: 'paystack',
    type: 'card',
    countries: ['NGA', 'GHA', 'ZAF'],
    webhook_config: {
      provider: 'paystack',
      endpoint_url: `${process.env.SUPABASE_URL}/functions/v1/payment-webhook/paystack`,
      secret_key: process.env.PAYSTACK_WEBHOOK_SECRET || '',
      events: ['charge.success', 'charge.failed', 'transfer.success', 'transfer.failed'],
      headers: {
        'Content-Type': 'application/json'
      }
    },
    api_config: {
      base_url: 'https://api.paystack.co',
      auth_type: 'api_key',
      credentials: {
        secret_key: process.env.PAYSTACK_SECRET_KEY || '',
        public_key: process.env.PAYSTACK_PUBLIC_KEY || ''
      }
    }
  },

  // Flutterwave (Multi-pays Afrique)
  {
    name: 'flutterwave',
    type: 'card',
    countries: ['NGA', 'GHA', 'KEN', 'UGA', 'ZAF', 'RWA'],
    webhook_config: {
      provider: 'flutterwave',
      endpoint_url: `${process.env.SUPABASE_URL}/functions/v1/payment-webhook/flutterwave`,
      secret_key: process.env.FLUTTERWAVE_WEBHOOK_SECRET || '',
      events: ['charge.completed', 'charge.failed', 'transfer.completed'],
      headers: {
        'Content-Type': 'application/json'
      }
    },
    api_config: {
      base_url: 'https://api.flutterwave.com/v3',
      auth_type: 'api_key',
      credentials: {
        secret_key: process.env.FLUTTERWAVE_SECRET_KEY || '',
        public_key: process.env.FLUTTERWAVE_PUBLIC_KEY || '',
        encryption_key: process.env.FLUTTERWAVE_ENCRYPTION_KEY || ''
      }
    }
  }
]

// =====================================================
// FONCTIONS DE CONFIGURATION
// =====================================================

export async function setupWebhooks(supabase: any): Promise<void> {
  console.log('Setting up payment provider webhooks...')

  for (const provider of PaymentProviders) {
    try {
      await registerWebhook(provider)
      await saveProviderConfig(supabase, provider)
      console.log(`✅ Webhook configured for ${provider.name}`)
    } catch (error) {
      console.error(`❌ Failed to configure webhook for ${provider.name}:`, error)
    }
  }
}

async function registerWebhook(provider: PaymentProvider): Promise<void> {
  const { webhook_config, api_config } = provider

  switch (provider.name) {
    case 'mpesa':
      await registerMpesaWebhook(webhook_config, api_config)
      break
    
    case 'orange_money':
      await registerOrangeMoneyWebhook(webhook_config, api_config)
      break
    
    case 'wave':
      await registerWaveWebhook(webhook_config, api_config)
      break
    
    case 'paystack':
      await registerPaystackWebhook(webhook_config, api_config)
      break
    
    case 'flutterwave':
      await registerFlutterwaveWebhook(webhook_config, api_config)
      break
    
    default:
      console.log(`No webhook registration needed for ${provider.name}`)
  }
}

async function registerMpesaWebhook(webhook: WebhookConfig, api: any): Promise<void> {
  // M-Pesa utilise des URLs de callback configurées dans le dashboard
  // Pas d'API pour enregistrer dynamiquement les webhooks
  console.log('M-Pesa webhook URL should be configured in Safaricom dashboard:', webhook.endpoint_url)
}

async function registerOrangeMoneyWebhook(webhook: WebhookConfig, api: any): Promise<void> {
  // Orange Money webhook configuration via API
  const response = await fetch(`${api.base_url}/webhook`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${api.credentials.client_id}:${api.credentials.client_secret}`
    },
    body: JSON.stringify({
      url: webhook.endpoint_url,
      events: webhook.events
    })
  })

  if (!response.ok) {
    throw new Error(`Orange Money webhook registration failed: ${response.statusText}`)
  }
}

async function registerWaveWebhook(webhook: WebhookConfig, api: any): Promise<void> {
  // Wave webhook configuration
  const response = await fetch(`${api.base_url}/webhooks`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${api.credentials.api_key}`
    },
    body: JSON.stringify({
      url: webhook.endpoint_url,
      events: webhook.events
    })
  })

  if (!response.ok) {
    throw new Error(`Wave webhook registration failed: ${response.statusText}`)
  }
}

async function registerPaystackWebhook(webhook: WebhookConfig, api: any): Promise<void> {
  // Paystack webhook configuration via dashboard
  console.log('Paystack webhook URL should be configured in dashboard:', webhook.endpoint_url)
}

async function registerFlutterwaveWebhook(webhook: WebhookConfig, api: any): Promise<void> {
  // Flutterwave webhook configuration via dashboard
  console.log('Flutterwave webhook URL should be configured in dashboard:', webhook.endpoint_url)
}

async function saveProviderConfig(supabase: any, provider: PaymentProvider): Promise<void> {
  // Sauvegarder la configuration dans la base de données
  for (const countryCode of provider.countries) {
    const { error } = await supabase
      .from('payment_configurations')
      .upsert({
        country_id: await getCountryId(supabase, countryCode),
        payment_method: provider.type,
        provider_name: provider.name,
        is_enabled: true,
        api_config: {
          base_url: provider.api_config.base_url,
          auth_type: provider.api_config.auth_type,
          webhook_url: provider.webhook_config.endpoint_url,
          supported_events: provider.webhook_config.events
        }
      })

    if (error) {
      console.error(`Failed to save config for ${provider.name} in ${countryCode}:`, error)
    }
  }
}

async function getCountryId(supabase: any, countryCode: string): Promise<string> {
  const { data, error } = await supabase
    .from('countries')
    .select('id')
    .eq('code', countryCode)
    .single()

  if (error || !data) {
    throw new Error(`Country not found: ${countryCode}`)
  }

  return data.id
}

// =====================================================
// VALIDATION DES WEBHOOKS
// =====================================================

export function validateWebhookSignature(
  provider: string,
  payload: string,
  signature: string,
  secret: string
): boolean {
  
  const providerConfig = PaymentProviders.find(p => p.name === provider)
  if (!providerConfig) {
    return false
  }

  switch (provider) {
    case 'mpesa':
      return validateMpesaSignature(payload, signature, secret)
    
    case 'orange_money':
      return validateOrangeMoneySignature(payload, signature, secret)
    
    case 'wave':
      return validateWaveSignature(payload, signature, secret)
    
    case 'paystack':
      return validatePaystackSignature(payload, signature, secret)
    
    case 'flutterwave':
      return validateFlutterwaveSignature(payload, signature, secret)
    
    default:
      return false
  }
}

function validateMpesaSignature(payload: string, signature: string, secret: string): boolean {
  // M-Pesa utilise une validation basée sur les paramètres de la requête
  // Implémentation spécifique selon la documentation Safaricom
  return true // Placeholder
}

function validateOrangeMoneySignature(payload: string, signature: string, secret: string): boolean {
  // Orange Money signature validation
  const crypto = require('crypto')
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex')
  
  return signature === expectedSignature
}

function validateWaveSignature(payload: string, signature: string, secret: string): boolean {
  // Wave signature validation
  const crypto = require('crypto')
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex')
  
  return signature === expectedSignature
}

function validatePaystackSignature(payload: string, signature: string, secret: string): boolean {
  // Paystack signature validation
  const crypto = require('crypto')
  const expectedSignature = crypto
    .createHmac('sha512', secret)
    .update(payload)
    .digest('hex')
  
  return signature === expectedSignature
}

function validateFlutterwaveSignature(payload: string, signature: string, secret: string): boolean {
  // Flutterwave signature validation
  const crypto = require('crypto')
  const expectedSignature = crypto
    .createHash('sha256')
    .update(payload + secret)
    .digest('hex')
  
  return signature === expectedSignature
}

// =====================================================
// GESTION DES ERREURS ET RETRY
// =====================================================

export async function handleWebhookError(
  supabase: any,
  provider: string,
  error: any,
  payload: any
): Promise<void> {
  
  await supabase
    .from('webhook_errors')
    .insert({
      provider,
      error_type: error.name || 'UnknownError',
      error_message: error.message,
      payload,
      created_at: new Date().toISOString()
    })

  // Envoyer une alerte aux administrateurs
  await supabase.functions.invoke('send-notification', {
    body: {
      user_id: 'admin', // ID admin système
      type: 'system_error',
      title: 'Webhook Error',
      body: `Webhook error for ${provider}: ${error.message}`,
      channels: ['email'],
      priority: 8
    }
  })
}

// =====================================================
// MONITORING ET MÉTRIQUES
// =====================================================

export async function logWebhookMetrics(
  supabase: any,
  provider: string,
  event_type: string,
  processing_time_ms: number,
  success: boolean
): Promise<void> {
  
  await supabase
    .from('webhook_metrics')
    .insert({
      provider,
      event_type,
      processing_time_ms,
      success,
      timestamp: new Date().toISOString()
    })
}

// =====================================================
// SCRIPT D'INITIALISATION
// =====================================================

export async function initializePaymentProviders(): Promise<void> {
  const supabase = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  )

  try {
    console.log('🚀 Initializing payment providers...')
    
    // Configurer les webhooks
    await setupWebhooks(supabase)
    
    // Vérifier les configurations
    await validateConfigurations(supabase)
    
    console.log('✅ Payment providers initialized successfully')
    
  } catch (error) {
    console.error('❌ Failed to initialize payment providers:', error)
    throw error
  }
}

async function validateConfigurations(supabase: any): Promise<void> {
  const { data: configs, error } = await supabase
    .from('payment_configurations')
    .select('*')
    .eq('is_enabled', true)

  if (error) {
    throw new Error('Failed to validate payment configurations')
  }

  console.log(`📊 Found ${configs.length} active payment configurations`)
  
  for (const config of configs) {
    console.log(`  - ${config.provider_name} for ${config.payment_method} in country ${config.country_id}`)
  }
}

// Exporter la fonction d'initialisation pour utilisation dans les scripts
if (require.main === module) {
  initializePaymentProviders()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}
