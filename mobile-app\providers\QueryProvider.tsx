// =====================================================
// REACT QUERY PROVIDER - AFRICAN DELIVERY APP
// =====================================================

import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createAsyncStoragePersister } from '@tanstack/query-async-storage-persister';
import { persistQueryClient } from '@tanstack/react-query-persist-client';

// Create a client with African market optimizations
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Optimized for low bandwidth scenarios
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 60 * 24, // 24 hours
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry up to 3 times for network errors
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Enable background refetch for better UX
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      refetchOnMount: true,
      // Network mode for offline support
      networkMode: 'offlineFirst',
    },
    mutations: {
      retry: (failureCount, error: any) => {
        // Don't retry mutations on client errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry up to 2 times for network errors
        return failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
      networkMode: 'offlineFirst',
    },
  },
});

// Create persister for offline support
const asyncStoragePersister = createAsyncStoragePersister({
  storage: AsyncStorage,
  key: 'REACT_QUERY_OFFLINE_CACHE',
  serialize: JSON.stringify,
  deserialize: JSON.parse,
});

// Persist query client for offline functionality
persistQueryClient({
  queryClient,
  persister: asyncStoragePersister,
  maxAge: 1000 * 60 * 60 * 24 * 7, // 7 days
  hydrateOptions: {
    // Only hydrate successful queries
    defaultOptions: {
      queries: {
        cacheTime: 1000 * 60 * 60 * 24, // 24 hours
      },
    },
  },
});

interface QueryProviderProps {
  children: React.ReactNode;
}

export const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

// Export query client for direct access if needed
export { queryClient };

// Utility functions for cache management
export const cacheUtils = {
  // Clear all cached data
  clearCache: () => {
    queryClient.clear();
  },

  // Clear specific cache keys
  clearCacheByKey: (queryKey: string[]) => {
    queryClient.removeQueries({ queryKey });
  },

  // Invalidate and refetch data
  invalidateQueries: (queryKey: string[]) => {
    queryClient.invalidateQueries({ queryKey });
  },

  // Get cache size (approximate)
  getCacheSize: () => {
    const cache = queryClient.getQueryCache();
    return cache.getAll().length;
  },

  // Prefetch data for offline use
  prefetchForOffline: async () => {
    try {
      // Prefetch essential data that users might need offline
      await Promise.allSettled([
        queryClient.prefetchQuery({
          queryKey: ['categories'],
          staleTime: 1000 * 60 * 60, // 1 hour
        }),
        queryClient.prefetchQuery({
          queryKey: ['merchants'],
          staleTime: 1000 * 60 * 30, // 30 minutes
        }),
        queryClient.prefetchQuery({
          queryKey: ['promotions'],
          staleTime: 1000 * 60 * 30, // 30 minutes
        }),
      ]);
      console.log('✅ Offline data prefetched successfully');
    } catch (error) {
      console.error('❌ Error prefetching offline data:', error);
    }
  },

  // Check if device is online and sync data
  syncWhenOnline: () => {
    // This will be called when network connection is restored
    queryClient.resumePausedMutations();
    queryClient.invalidateQueries();
  },

  // Get cached data without triggering a fetch
  getCachedData: (queryKey: string[]) => {
    return queryClient.getQueryData(queryKey);
  },

  // Set data in cache manually
  setCachedData: (queryKey: string[], data: any) => {
    queryClient.setQueryData(queryKey, data);
  },

  // Check if query is loading
  isQueryLoading: (queryKey: string[]) => {
    const query = queryClient.getQueryState(queryKey);
    return query?.status === 'loading';
  },

  // Check if query has error
  hasQueryError: (queryKey: string[]) => {
    const query = queryClient.getQueryState(queryKey);
    return query?.status === 'error';
  },

  // Get query error
  getQueryError: (queryKey: string[]) => {
    const query = queryClient.getQueryState(queryKey);
    return query?.error;
  },

  // Optimistic updates for better UX
  optimisticUpdate: (queryKey: string[], updater: (old: any) => any) => {
    queryClient.setQueryData(queryKey, updater);
  },

  // Cancel outgoing queries
  cancelQueries: (queryKey: string[]) => {
    queryClient.cancelQueries({ queryKey });
  },
};

// Network status utilities
export const networkUtils = {
  // Check if any queries are currently fetching
  isFetching: () => {
    return queryClient.isFetching() > 0;
  },

  // Check if any mutations are currently running
  isMutating: () => {
    return queryClient.isMutating() > 0;
  },

  // Get all failed queries
  getFailedQueries: () => {
    return queryClient.getQueryCache().getAll().filter(
      query => query.state.status === 'error'
    );
  },

  // Retry all failed queries
  retryFailedQueries: () => {
    const failedQueries = networkUtils.getFailedQueries();
    failedQueries.forEach(query => {
      queryClient.invalidateQueries({ queryKey: query.queryKey });
    });
  },

  // Get network statistics
  getNetworkStats: () => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    return {
      totalQueries: queries.length,
      successfulQueries: queries.filter(q => q.state.status === 'success').length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      loadingQueries: queries.filter(q => q.state.status === 'loading').length,
      staleQueries: queries.filter(q => q.isStale()).length,
    };
  },
};

// African market specific optimizations
export const africanMarketUtils = {
  // Low bandwidth mode - reduce cache time and increase stale time
  enableLowBandwidthMode: () => {
    queryClient.setDefaultOptions({
      queries: {
        staleTime: 1000 * 60 * 10, // 10 minutes
        cacheTime: 1000 * 60 * 30, // 30 minutes
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
      },
    });
  },

  // High bandwidth mode - normal settings
  enableHighBandwidthMode: () => {
    queryClient.setDefaultOptions({
      queries: {
        staleTime: 1000 * 60 * 5, // 5 minutes
        cacheTime: 1000 * 60 * 60 * 24, // 24 hours
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
      },
    });
  },

  // Preload essential data for offline use
  preloadEssentialData: async () => {
    const essentialQueries = [
      ['categories'],
      ['merchants'],
      ['promotions'],
      ['user'],
    ];

    await Promise.allSettled(
      essentialQueries.map(queryKey =>
        queryClient.prefetchQuery({
          queryKey,
          staleTime: 1000 * 60 * 60, // 1 hour
        })
      )
    );
  },

  // Clear non-essential cache to save storage
  clearNonEssentialCache: () => {
    const essentialKeys = ['categories', 'user', 'orders'];
    const cache = queryClient.getQueryCache();
    
    cache.getAll().forEach(query => {
      const isEssential = essentialKeys.some(key => 
        query.queryKey.includes(key)
      );
      
      if (!isEssential) {
        queryClient.removeQueries({ queryKey: query.queryKey });
      }
    });
  },
};

export default QueryProvider;
