{"name": "expo-camera", "version": "16.1.6", "description": "A React component that renders a preview for the device's either front or back camera. Camera's parameters like zoom, auto focus, white balance and flash mode are adjustable. With expo-camera, one can also take photos and record videos that are saved to the app's cache. Morever, the component is also capable of detecting faces and bar codes appearing on the preview.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "camera"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-camera"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/camera/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"invariant": "^2.2.4"}, "devDependencies": {"expo-module-scripts": "^4.1.6"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*", "react-native-web": "*"}, "peerDependenciesMeta": {"react-native-web": {"optional": true}}, "gitHead": "84355076bc31a356aa3d23257f387f221885f53d"}