// src/asyncThrottle.ts
import { noop } from "./utils.js";
function asyncThrottle(func, { interval = 1e3, onError = noop } = {}) {
  if (typeof func !== "function") throw new Error("argument is not function.");
  let nextExecutionTime = 0;
  let lastArgs = null;
  let isExecuting = false;
  let isScheduled = false;
  return async (...args) => {
    lastArgs = args;
    if (isScheduled) return;
    isScheduled = true;
    while (isExecuting) {
      await new Promise((done) => setTimeout(done, interval));
    }
    while (Date.now() < nextExecutionTime) {
      await new Promise(
        (done) => setTimeout(done, nextExecutionTime - Date.now())
      );
    }
    isScheduled = false;
    isExecuting = true;
    try {
      await func(...lastArgs);
    } catch (error) {
      try {
        onError(error);
      } catch {
      }
    }
    nextExecutionTime = Date.now() + interval;
    isExecuting = false;
  };
}
export {
  asyncThrottle
};
//# sourceMappingURL=asyncThrottle.js.map