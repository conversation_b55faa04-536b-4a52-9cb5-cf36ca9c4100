// =====================================================
// COMPOSANT CARTE AVEC GÉOLOCALISATION
// =====================================================

import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Platform,
  Dimensions,
} from 'react-native';
import MapView, { Marker, Polyline, Region, PROVIDER_GOOGLE } from 'react-native-maps';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import Button from '../ui/Button';

interface MapViewProps {
  initialRegion?: Region;
  markers?: MapMarker[];
  route?: RouteCoordinate[];
  showUserLocation?: boolean;
  onLocationSelect?: (coordinate: { latitude: number; longitude: number }) => void;
  onRegionChange?: (region: Region) => void;
  style?: any;
  zoomEnabled?: boolean;
  scrollEnabled?: boolean;
  rotateEnabled?: boolean;
  pitchEnabled?: boolean;
}

interface MapMarker {
  id: string;
  coordinate: {
    latitude: number;
    longitude: number;
  };
  title?: string;
  description?: string;
  type?: 'user' | 'restaurant' | 'delivery' | 'pickup' | 'destination';
  icon?: string;
}

interface RouteCoordinate {
  latitude: number;
  longitude: number;
}

const { width, height } = Dimensions.get('window');

const CustomMapView: React.FC<MapViewProps> = ({
  initialRegion,
  markers = [],
  route = [],
  showUserLocation = true,
  onLocationSelect,
  onRegionChange,
  style,
  zoomEnabled = true,
  scrollEnabled = true,
  rotateEnabled = false,
  pitchEnabled = false,
}) => {
  const mapRef = useRef<MapView>(null);
  const [region, setRegion] = useState<Region>(
    initialRegion || {
      latitude: 14.6928, // Dakar, Sénégal par défaut
      longitude: -17.4467,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    }
  );
  const [userLocation, setUserLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [hasLocationPermission, setHasLocationPermission] = useState(false);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status === 'granted') {
        setHasLocationPermission(true);
        getCurrentLocation();
      } else {
        Alert.alert(
          'Permission requise',
          'Cette application a besoin d\'accéder à votre localisation pour fonctionner correctement.',
          [
            { text: 'Annuler', style: 'cancel' },
            { text: 'Paramètres', onPress: () => Location.requestForegroundPermissionsAsync() },
          ]
        );
      }
    } catch (error) {
      console.error('Erreur permission localisation:', error);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const userCoords = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      setUserLocation(userCoords);

      // Centrer la carte sur la position utilisateur
      const newRegion = {
        ...userCoords,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };

      setRegion(newRegion);
      mapRef.current?.animateToRegion(newRegion, 1000);
    } catch (error) {
      console.error('Erreur obtention localisation:', error);
      Alert.alert(
        'Erreur',
        'Impossible d\'obtenir votre localisation. Vérifiez que le GPS est activé.'
      );
    }
  };

  const handleMapPress = (event: any) => {
    if (onLocationSelect) {
      const coordinate = event.nativeEvent.coordinate;
      onLocationSelect(coordinate);
    }
  };

  const handleRegionChangeComplete = (newRegion: Region) => {
    setRegion(newRegion);
    onRegionChange?.(newRegion);
  };

  const centerOnUser = () => {
    if (userLocation) {
      const newRegion = {
        ...userLocation,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };
      mapRef.current?.animateToRegion(newRegion, 1000);
    } else {
      getCurrentLocation();
    }
  };

  const fitToMarkers = () => {
    if (markers.length > 0) {
      mapRef.current?.fitToCoordinates(
        markers.map(marker => marker.coordinate),
        {
          edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
          animated: true,
        }
      );
    }
  };

  const getMarkerIcon = (type: string) => {
    switch (type) {
      case 'user':
        return '📍';
      case 'restaurant':
        return '🏪';
      case 'delivery':
        return '🚚';
      case 'pickup':
        return '📦';
      case 'destination':
        return '🎯';
      default:
        return '📍';
    }
  };

  return (
    <View style={[styles.container, style]}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={Platform.OS === 'android' ? PROVIDER_GOOGLE : undefined}
        region={region}
        onPress={handleMapPress}
        onRegionChangeComplete={handleRegionChangeComplete}
        showsUserLocation={showUserLocation && hasLocationPermission}
        showsMyLocationButton={false}
        zoomEnabled={zoomEnabled}
        scrollEnabled={scrollEnabled}
        rotateEnabled={rotateEnabled}
        pitchEnabled={pitchEnabled}
        showsCompass={true}
        showsScale={true}
        showsBuildings={true}
        showsTraffic={false}
        mapType="standard"
      >
        {/* Marqueurs personnalisés */}
        {markers.map((marker) => (
          <Marker
            key={marker.id}
            coordinate={marker.coordinate}
            title={marker.title}
            description={marker.description}
          >
            <View style={styles.markerContainer}>
              <View style={[styles.marker, getMarkerStyle(marker.type)]}>
                <Ionicons
                  name={getMarkerIconName(marker.type)}
                  size={20}
                  color="#FFFFFF"
                />
              </View>
            </View>
          </Marker>
        ))}

        {/* Route */}
        {route.length > 1 && (
          <Polyline
            coordinates={route}
            strokeColor="#007AFF"
            strokeWidth={4}
            lineDashPattern={[5, 5]}
          />
        )}
      </MapView>

      {/* Boutons de contrôle */}
      <View style={styles.controls}>
        <Button
          title=""
          onPress={centerOnUser}
          variant="outline"
          size="small"
          icon="locate"
          style={styles.controlButton}
        />

        {markers.length > 1 && (
          <Button
            title=""
            onPress={fitToMarkers}
            variant="outline"
            size="small"
            icon="resize"
            style={styles.controlButton}
          />
        )}
      </View>
    </View>
  );
};

const getMarkerIconName = (type?: string): keyof typeof Ionicons.glyphMap => {
  switch (type) {
    case 'user':
      return 'person';
    case 'restaurant':
      return 'restaurant';
    case 'delivery':
      return 'car';
    case 'pickup':
      return 'cube';
    case 'destination':
      return 'flag';
    default:
      return 'location';
  }
};

const getMarkerStyle = (type?: string) => {
  switch (type) {
    case 'user':
      return styles.userMarker;
    case 'restaurant':
      return styles.restaurantMarker;
    case 'delivery':
      return styles.deliveryMarker;
    case 'pickup':
      return styles.pickupMarker;
    case 'destination':
      return styles.destinationMarker;
    default:
      return styles.userMarker;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    width: '100%',
    height: '100%',
  },
  controls: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    gap: 10,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  markerContainer: {
    alignItems: 'center',
  },
  marker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  userMarker: {
    backgroundColor: '#007AFF',
  },
  restaurantMarker: {
    backgroundColor: '#34C759',
  },
  deliveryMarker: {
    backgroundColor: '#FF9500',
  },
  pickupMarker: {
    backgroundColor: '#5856D6',
  },
  destinationMarker: {
    backgroundColor: '#FF3B30',
  },
});

export default CustomMapView;
