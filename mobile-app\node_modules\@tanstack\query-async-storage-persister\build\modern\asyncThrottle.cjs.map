{"version": 3, "sources": ["../../src/asyncThrottle.ts"], "sourcesContent": ["import { noop } from './utils'\n\ninterface AsyncThrottleOptions {\n  interval?: number\n  onError?: (error: unknown) => void\n}\n\nexport function asyncThrottle<TArgs extends ReadonlyArray<unknown>>(\n  func: (...args: TArgs) => Promise<void>,\n  { interval = 1000, onError = noop }: AsyncThrottleOptions = {},\n) {\n  if (typeof func !== 'function') throw new Error('argument is not function.')\n\n  let nextExecutionTime = 0\n  let lastArgs = null\n  let isExecuting = false\n  let isScheduled = false\n\n  return async (...args: TArgs) => {\n    lastArgs = args\n    if (isScheduled) return\n    isScheduled = true\n    while (isExecuting) {\n      await new Promise((done) => setTimeout(done, interval))\n    }\n    while (Date.now() < nextExecutionTime) {\n      await new Promise((done) =>\n        setTimeout(done, nextExecutionTime - Date.now()),\n      )\n    }\n    isScheduled = false\n    isExecuting = true\n    try {\n      await func(...lastArgs)\n    } catch (error) {\n      try {\n        onError(error)\n      } catch {}\n    }\n    nextExecutionTime = Date.now() + interval\n    isExecuting = false\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAqB;AAOd,SAAS,cACd,MACA,EAAE,WAAW,KAAM,UAAU,kBAAK,IAA0B,CAAC,GAC7D;AACA,MAAI,OAAO,SAAS,WAAY,OAAM,IAAI,MAAM,2BAA2B;AAE3E,MAAI,oBAAoB;AACxB,MAAI,WAAW;AACf,MAAI,cAAc;AAClB,MAAI,cAAc;AAElB,SAAO,UAAU,SAAgB;AAC/B,eAAW;AACX,QAAI,YAAa;AACjB,kBAAc;AACd,WAAO,aAAa;AAClB,YAAM,IAAI,QAAQ,CAAC,SAAS,WAAW,MAAM,QAAQ,CAAC;AAAA,IACxD;AACA,WAAO,KAAK,IAAI,IAAI,mBAAmB;AACrC,YAAM,IAAI;AAAA,QAAQ,CAAC,SACjB,WAAW,MAAM,oBAAoB,KAAK,IAAI,CAAC;AAAA,MACjD;AAAA,IACF;AACA,kBAAc;AACd,kBAAc;AACd,QAAI;AACF,YAAM,KAAK,GAAG,QAAQ;AAAA,IACxB,SAAS,OAAO;AACd,UAAI;AACF,gBAAQ,KAAK;AAAA,MACf,QAAQ;AAAA,MAAC;AAAA,IACX;AACA,wBAAoB,KAAK,IAAI,IAAI;AACjC,kBAAc;AAAA,EAChB;AACF;", "names": []}