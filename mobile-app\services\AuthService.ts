// =====================================================
// SERVICE D'AUTHENTIFICATION NATIF EXPO
// =====================================================

import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Types pour l'authentification
export interface User {
  id: string;
  email: string;
  role: 'client' | 'deliverer' | 'merchant' | 'admin';
  name?: string;
  phone?: string;
  createdAt: string;
}

export interface AuthSession {
  user: User;
  token: string;
  expiresAt: string;
}

// Clés de stockage sécurisé
const STORAGE_KEYS = {
  SESSION: 'auth_session',
  USER: 'auth_user',
  TOKEN: 'auth_token',
} as const;

// Service d'authentification natif
export class AuthService {
  private static instance: AuthService;
  private currentSession: AuthSession | null = null;
  private listeners: Array<(session: AuthSession | null) => void> = [];

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // Stockage sécurisé conditionnel
  private async setSecureItem(key: string, value: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Utiliser localStorage sur web
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, value);
        }
      } else {
        // Utiliser SecureStore sur mobile
        await SecureStore.setItemAsync(key, value);
      }
    } catch (error) {
      console.error('Erreur lors du stockage sécurisé:', error);
    }
  }

  private async getSecureItem(key: string): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        // Utiliser localStorage sur web
        if (typeof window !== 'undefined') {
          return window.localStorage.getItem(key);
        }
        return null;
      } else {
        // Utiliser SecureStore sur mobile
        return await SecureStore.getItemAsync(key);
      }
    } catch (error) {
      console.error('Erreur lors de la lecture sécurisée:', error);
      return null;
    }
  }

  private async deleteSecureItem(key: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Utiliser localStorage sur web
        if (typeof window !== 'undefined') {
          window.localStorage.removeItem(key);
        }
      } else {
        // Utiliser SecureStore sur mobile
        await SecureStore.deleteItemAsync(key);
      }
    } catch (error) {
      console.error('Erreur lors de la suppression sécurisée:', error);
    }
  }

  // Générer un token simple pour le développement
  private generateToken(): string {
    return `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Générer un ID utilisateur
  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Inscription
  async signUp(email: string, password: string, role: User['role'] = 'client'): Promise<{ user: User | null; error: string | null }> {
    try {
      console.log('🔐 Inscription:', { email, role });

      // Validation simple
      if (!email || !password) {
        return { user: null, error: 'Email et mot de passe requis' };
      }

      if (password.length < 6) {
        return { user: null, error: 'Le mot de passe doit contenir au moins 6 caractères' };
      }

      // Créer un nouvel utilisateur
      const user: User = {
        id: this.generateUserId(),
        email,
        role,
        name: email.split('@')[0], // Nom par défaut
        createdAt: new Date().toISOString(),
      };

      // Créer une session
      const session: AuthSession = {
        user,
        token: this.generateToken(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 jours
      };

      // Sauvegarder la session
      await this.saveSession(session);
      this.currentSession = session;
      this.notifyListeners(session);

      console.log('✅ Inscription réussie:', user.email);
      return { user, error: null };
    } catch (error) {
      console.error('❌ Erreur inscription:', error);
      return { user: null, error: 'Erreur lors de l\'inscription' };
    }
  }

  // Connexion
  async signIn(email: string, password: string): Promise<{ user: User | null; error: string | null }> {
    try {
      console.log('🔐 Connexion:', email);

      // Validation simple
      if (!email || !password) {
        return { user: null, error: 'Email et mot de passe requis' };
      }

      // Simuler une vérification (en production, vérifier avec une API)
      const user: User = {
        id: this.generateUserId(),
        email,
        role: 'client', // Rôle par défaut
        name: email.split('@')[0],
        createdAt: new Date().toISOString(),
      };

      // Créer une session
      const session: AuthSession = {
        user,
        token: this.generateToken(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      };

      // Sauvegarder la session
      await this.saveSession(session);
      this.currentSession = session;
      this.notifyListeners(session);

      console.log('✅ Connexion réussie:', user.email);
      return { user, error: null };
    } catch (error) {
      console.error('❌ Erreur connexion:', error);
      return { user: null, error: 'Erreur lors de la connexion' };
    }
  }

  // Déconnexion
  async signOut(): Promise<{ error: string | null }> {
    try {
      console.log('🔐 Déconnexion');

      // Supprimer la session stockée
      await this.deleteSecureItem(STORAGE_KEYS.SESSION);
      await this.deleteSecureItem(STORAGE_KEYS.USER);
      await this.deleteSecureItem(STORAGE_KEYS.TOKEN);

      this.currentSession = null;
      this.notifyListeners(null);

      console.log('✅ Déconnexion réussie');
      return { error: null };
    } catch (error) {
      console.error('❌ Erreur déconnexion:', error);
      return { error: 'Erreur lors de la déconnexion' };
    }
  }

  // Récupérer la session actuelle
  async getCurrentSession(): Promise<AuthSession | null> {
    try {
      if (this.currentSession) {
        return this.currentSession;
      }

      // Charger depuis le stockage sécurisé
      const sessionData = await this.getSecureItem(STORAGE_KEYS.SESSION);
      if (sessionData) {
        const session: AuthSession = JSON.parse(sessionData);
        
        // Vérifier si la session n'a pas expiré
        if (new Date(session.expiresAt) > new Date()) {
          this.currentSession = session;
          return session;
        } else {
          // Session expirée, la supprimer
          await this.signOut();
        }
      }

      return null;
    } catch (error) {
      console.error('❌ Erreur récupération session:', error);
      return null;
    }
  }

  // Sauvegarder la session
  private async saveSession(session: AuthSession): Promise<void> {
    await this.setSecureItem(STORAGE_KEYS.SESSION, JSON.stringify(session));
    await this.setSecureItem(STORAGE_KEYS.USER, JSON.stringify(session.user));
    await this.setSecureItem(STORAGE_KEYS.TOKEN, session.token);
  }

  // Écouter les changements d'état d'authentification
  onAuthStateChange(callback: (session: AuthSession | null) => void): () => void {
    this.listeners.push(callback);
    
    // Retourner une fonction de désabonnement
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  // Notifier les listeners
  private notifyListeners(session: AuthSession | null): void {
    this.listeners.forEach(listener => {
      try {
        listener(session);
      } catch (error) {
        console.error('Erreur dans le listener d\'authentification:', error);
      }
    });
  }

  // Vérifier si l'utilisateur est connecté
  async isAuthenticated(): Promise<boolean> {
    const session = await this.getCurrentSession();
    return session !== null;
  }

  // Obtenir l'utilisateur actuel
  async getCurrentUser(): Promise<User | null> {
    const session = await this.getCurrentSession();
    return session?.user || null;
  }
}

// Instance singleton
export const authService = AuthService.getInstance();
export default authService;
