// =====================================================
// CONFIGURATION SUPABASE SIMPLIFIÉE
// =====================================================

// Version simplifiée pour éviter les erreurs de WebSocket
// À utiliser pendant le développement initial

export const supabaseConfig = {
  url: process.env.EXPO_PUBLIC_SUPABASE_URL ?? 'https://demo.supabase.co',
  anonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ?? 'demo-key',
};

// Mock client pour le développement
export const supabase = {
  auth: {
    signUp: async (credentials: any) => {
      console.log('✅ Mock signUp:', credentials.email);
      return {
        data: {
          user: {
            id: 'mock-user-' + Date.now(),
            email: credentials.email
          }
        },
        error: null
      };
    },
    signInWithPassword: async (credentials: any) => {
      console.log('✅ Mock signInWithPassword:', credentials.email);
      return {
        data: {
          user: {
            id: 'mock-user-' + Date.now(),
            email: credentials.email
          }
        },
        error: null
      };
    },
    signOut: async () => {
      console.log('✅ Mock signOut');
      return { error: null };
    },
    getSession: async () => {
      console.log('✅ Mock getSession');
      return { data: { session: null }, error: null };
    },
    getUser: async () => {
      console.log('✅ Mock getUser');
      return { data: { user: null }, error: null };
    },
    onAuthStateChange: (callback: any) => {
      console.log('✅ Mock onAuthStateChange setup');
      // Simuler un callback immédiat
      setTimeout(() => {
        callback('SIGNED_OUT', null);
      }, 100);
      return {
        data: {
          subscription: {
            unsubscribe: () => console.log('✅ Mock unsubscribe')
          }
        }
      };
    },
  },
  from: (table: string) => ({
    select: (columns?: string) => ({
      eq: (column: string, value: any) => ({
        single: async () => {
          console.log(`Mock select from ${table}:`, { columns, column, value });
          return { data: null, error: null };
        },
        limit: (count: number) => ({
          async: async () => {
            console.log(`Mock select from ${table} limit ${count}`);
            return { data: [], error: null };
          },
        }),
      }),
      async: async () => {
        console.log(`Mock select from ${table}`);
        return { data: [], error: null };
      },
    }),
    insert: (data: any) => ({
      async: async () => {
        console.log(`Mock insert into ${table}:`, data);
        return { data: null, error: null };
      },
    }),
    update: (data: any) => ({
      eq: (column: string, value: any) => ({
        async: async () => {
          console.log(`Mock update ${table}:`, { data, column, value });
          return { data: null, error: null };
        },
      }),
    }),
    delete: () => ({
      eq: (column: string, value: any) => ({
        async: async () => {
          console.log(`Mock delete from ${table}:`, { column, value });
          return { data: null, error: null };
        },
      }),
    }),
  }),
  functions: {
    invoke: async (functionName: string, options?: any) => {
      console.log(`Mock function invoke: ${functionName}`, options);
      return { data: null, error: null };
    },
  },
  storage: {
    from: (bucket: string) => ({
      upload: async (path: string, file: any) => {
        console.log(`Mock storage upload to ${bucket}/${path}`);
        return { data: null, error: null };
      },
      download: async (path: string) => {
        console.log(`Mock storage download from ${bucket}/${path}`);
        return { data: null, error: null };
      },
      getPublicUrl: (path: string) => {
        console.log(`Mock storage getPublicUrl: ${bucket}/${path}`);
        return { data: { publicUrl: `https://demo.supabase.co/storage/v1/object/public/${bucket}/${path}` } };
      },
    }),
  },
};

export default supabase;
