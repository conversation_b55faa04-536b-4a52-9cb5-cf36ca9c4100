{"version": 3, "file": "NotificationPermissionsModule.native.js", "sourceRoot": "", "sources": ["../src/NotificationPermissionsModule.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAChC,mCAAmC,CACpC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { NotificationPermissionsModule } from './NotificationPermissionsModule.types';\n\nexport default requireNativeModule<NotificationPermissionsModule>(\n  'ExpoNotificationPermissionsModule'\n);\n"]}