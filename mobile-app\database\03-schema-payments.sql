-- =====================================================
-- SCHÉMA PAIEMENTS ET MOBILE MONEY
-- =====================================================

-- Table des transactions de paiement
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id),
    
    -- Informations de base
    payment_method payment_method NOT NULL,
    provider_name VARCHAR(50), -- 'mpesa', 'orange_money', 'visa', etc.
    amount INTEGER NOT NULL, -- Montant en centimes
    currency_code VARCHAR(3) NOT NULL DEFAULT 'XOF',
    
    -- Statut et timing
    status payment_status NOT NULL DEFAULT 'pending',
    
    -- Références externes
    external_transaction_id VARCHAR(100), -- ID chez le provider
    provider_reference VARCHAR(100), -- Référence provider
    merchant_reference VARCHAR(100), -- Notre référence
    
    -- Métadonnées du paiement
    payment_metadata JSONB DEFAULT '{}',
    
    -- Callbacks et webhooks
    callback_url TEXT,
    webhook_received_at TIMESTAMP WITH TIME ZONE,
    webhook_data JSONB,
    
    -- Timing
    initiated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Messages d'erreur
    error_code VARCHAR(50),
    error_message TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table spécifique Mobile Money
CREATE TABLE mobile_money_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
    
    -- Provider Mobile Money
    provider mobile_money_provider NOT NULL,
    
    -- Numéro de téléphone
    phone_number VARCHAR(20) NOT NULL,
    country_code VARCHAR(3) NOT NULL,
    
    -- Détails de la transaction
    transaction_type VARCHAR(20) NOT NULL DEFAULT 'payment', -- 'payment', 'refund'
    
    -- Données spécifiques au provider
    provider_data JSONB DEFAULT '{}',
    
    -- STK Push / USSD details (pour M-Pesa, etc.)
    stk_push_id VARCHAR(100),
    ussd_code VARCHAR(20),
    
    -- Confirmation
    confirmation_code VARCHAR(50),
    receipt_number VARCHAR(50),
    
    -- Frais
    transaction_fee INTEGER DEFAULT 0, -- Frais en centimes
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des paiements par carte
CREATE TABLE card_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
    
    -- Informations carte (tokenisées)
    card_token VARCHAR(100) NOT NULL, -- Token sécurisé
    card_last_four VARCHAR(4),
    card_brand VARCHAR(20), -- 'visa', 'mastercard', etc.
    card_type VARCHAR(20), -- 'credit', 'debit'
    
    -- Gateway de paiement
    gateway_name VARCHAR(50) NOT NULL, -- 'stripe', 'paystack', 'flutterwave'
    gateway_transaction_id VARCHAR(100),
    
    -- 3D Secure
    three_d_secure_status VARCHAR(20), -- 'required', 'passed', 'failed', 'not_required'
    
    -- Autorisation
    authorization_code VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des remboursements
CREATE TABLE refunds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID NOT NULL REFERENCES payments(id),
    order_id UUID NOT NULL REFERENCES orders(id),
    
    -- Montant du remboursement
    amount INTEGER NOT NULL, -- En centimes
    currency_code VARCHAR(3) NOT NULL,
    
    -- Raison du remboursement
    reason VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- Statut
    status payment_status NOT NULL DEFAULT 'pending',
    
    -- Références externes
    external_refund_id VARCHAR(100),
    provider_reference VARCHAR(100),
    
    -- Timing
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Métadonnées
    refund_metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des portefeuilles utilisateurs
CREATE TABLE user_wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Solde en centimes
    balance INTEGER NOT NULL DEFAULT 0,
    currency_code VARCHAR(3) NOT NULL DEFAULT 'XOF',
    
    -- Limites
    daily_limit INTEGER DEFAULT 100000, -- 1000 XOF en centimes
    monthly_limit INTEGER DEFAULT 5000000, -- 50000 XOF en centimes
    
    -- Statut
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    
    -- Dernière transaction
    last_transaction_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Un seul portefeuille par utilisateur et devise
    UNIQUE(user_id, currency_code)
);

-- Table des transactions de portefeuille
CREATE TABLE wallet_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wallet_id UUID NOT NULL REFERENCES user_wallets(id),
    
    -- Type de transaction
    transaction_type VARCHAR(20) NOT NULL, -- 'credit', 'debit'
    amount INTEGER NOT NULL, -- Montant en centimes (toujours positif)
    
    -- Solde avant et après
    balance_before INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    
    -- Référence
    reference_type VARCHAR(20), -- 'order', 'refund', 'topup', 'cashout'
    reference_id UUID, -- ID de la commande, remboursement, etc.
    
    -- Description
    description TEXT NOT NULL,
    
    -- Métadonnées
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des configurations de paiement par pays
CREATE TABLE payment_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    country_id UUID NOT NULL REFERENCES countries(id),
    
    -- Provider et méthode
    payment_method payment_method NOT NULL,
    provider_name VARCHAR(50) NOT NULL,
    
    -- Configuration
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    is_default BOOLEAN NOT NULL DEFAULT false,
    
    -- Limites
    min_amount INTEGER NOT NULL DEFAULT 100, -- 1 XOF en centimes
    max_amount INTEGER NOT NULL DEFAULT 10000000, -- 100000 XOF en centimes
    
    -- Frais
    fixed_fee INTEGER DEFAULT 0, -- Frais fixe en centimes
    percentage_fee DECIMAL(5, 4) DEFAULT 0, -- Frais en pourcentage (ex: 0.025 = 2.5%)
    
    -- Configuration technique
    api_config JSONB NOT NULL DEFAULT '{}',
    
    -- Ordre d'affichage
    sort_order INTEGER NOT NULL DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Un seul provider par défaut par méthode et pays
    EXCLUDE (country_id, payment_method WITH =) WHERE (is_default = true)
);

-- Table des logs de paiement pour audit
CREATE TABLE payment_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_id UUID NOT NULL REFERENCES payments(id),
    
    -- Événement
    event_type VARCHAR(50) NOT NULL, -- 'initiated', 'callback_received', 'status_changed', etc.
    old_status payment_status,
    new_status payment_status,
    
    -- Données de l'événement
    event_data JSONB DEFAULT '{}',
    
    -- Source
    source VARCHAR(50), -- 'api', 'webhook', 'admin', 'system'
    user_agent TEXT,
    ip_address INET,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour optimiser les performances
CREATE INDEX idx_payments_order ON payments(order_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_method ON payments(payment_method);
CREATE INDEX idx_payments_provider ON payments(provider_name);
CREATE INDEX idx_payments_external_id ON payments(external_transaction_id);
CREATE INDEX idx_payments_created_at ON payments(created_at);

CREATE INDEX idx_mobile_money_payment ON mobile_money_payments(payment_id);
CREATE INDEX idx_mobile_money_provider ON mobile_money_payments(provider);
CREATE INDEX idx_mobile_money_phone ON mobile_money_payments(phone_number);
CREATE INDEX idx_mobile_money_confirmation ON mobile_money_payments(confirmation_code);

CREATE INDEX idx_card_payments_payment ON card_payments(payment_id);
CREATE INDEX idx_card_payments_token ON card_payments(card_token);
CREATE INDEX idx_card_payments_gateway ON card_payments(gateway_name);

CREATE INDEX idx_refunds_payment ON refunds(payment_id);
CREATE INDEX idx_refunds_order ON refunds(order_id);
CREATE INDEX idx_refunds_status ON refunds(status);
CREATE INDEX idx_refunds_created_at ON refunds(created_at);

CREATE INDEX idx_user_wallets_user ON user_wallets(user_id);
CREATE INDEX idx_user_wallets_currency ON user_wallets(currency_code);
CREATE INDEX idx_user_wallets_active ON user_wallets(is_active);

CREATE INDEX idx_wallet_transactions_wallet ON wallet_transactions(wallet_id);
CREATE INDEX idx_wallet_transactions_type ON wallet_transactions(transaction_type);
CREATE INDEX idx_wallet_transactions_reference ON wallet_transactions(reference_type, reference_id);
CREATE INDEX idx_wallet_transactions_created_at ON wallet_transactions(created_at);

CREATE INDEX idx_payment_configurations_country ON payment_configurations(country_id);
CREATE INDEX idx_payment_configurations_method ON payment_configurations(payment_method);
CREATE INDEX idx_payment_configurations_enabled ON payment_configurations(is_enabled);

CREATE INDEX idx_payment_logs_payment ON payment_logs(payment_id);
CREATE INDEX idx_payment_logs_event ON payment_logs(event_type);
CREATE INDEX idx_payment_logs_created_at ON payment_logs(created_at);

-- Contraintes de validation
ALTER TABLE payments ADD CONSTRAINT chk_amount_positive 
    CHECK (amount > 0);

ALTER TABLE mobile_money_payments ADD CONSTRAINT chk_phone_format 
    CHECK (phone_number ~ '^\+[1-9]\d{1,14}$');

ALTER TABLE mobile_money_payments ADD CONSTRAINT chk_transaction_fee_non_negative 
    CHECK (transaction_fee >= 0);

ALTER TABLE card_payments ADD CONSTRAINT chk_card_last_four_format 
    CHECK (card_last_four ~ '^\d{4}$');

ALTER TABLE refunds ADD CONSTRAINT chk_refund_amount_positive 
    CHECK (amount > 0);

ALTER TABLE user_wallets ADD CONSTRAINT chk_balance_non_negative 
    CHECK (balance >= 0);

ALTER TABLE user_wallets ADD CONSTRAINT chk_limits_positive 
    CHECK (daily_limit > 0 AND monthly_limit > 0 AND monthly_limit >= daily_limit);

ALTER TABLE wallet_transactions ADD CONSTRAINT chk_transaction_amount_positive 
    CHECK (amount > 0);

ALTER TABLE wallet_transactions ADD CONSTRAINT chk_balance_calculation 
    CHECK (
        (transaction_type = 'credit' AND balance_after = balance_before + amount) OR
        (transaction_type = 'debit' AND balance_after = balance_before - amount)
    );

ALTER TABLE payment_configurations ADD CONSTRAINT chk_amount_limits 
    CHECK (min_amount > 0 AND max_amount > min_amount);

ALTER TABLE payment_configurations ADD CONSTRAINT chk_fees_non_negative 
    CHECK (fixed_fee >= 0 AND percentage_fee >= 0 AND percentage_fee <= 1);
