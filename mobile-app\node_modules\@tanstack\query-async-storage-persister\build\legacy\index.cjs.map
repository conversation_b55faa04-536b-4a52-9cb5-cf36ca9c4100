{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["import { asyncThrottle } from './asyncThrottle'\nimport { noop } from './utils'\nimport type {\n  AsyncStorage,\n  MaybePromise,\n  PersistedClient,\n  Persister,\n  Promisable,\n} from '@tanstack/query-persist-client-core'\n\nexport type AsyncPersistRetryer = (props: {\n  persistedClient: PersistedClient\n  error: Error\n  errorCount: number\n}) => Promisable<PersistedClient | undefined>\n\ninterface CreateAsyncStoragePersisterOptions {\n  /** The storage client used for setting and retrieving items from cache.\n   * For SSR pass in `undefined`. Note that window.localStorage can be\n   * `null` in Android WebViews depending on how they are configured.\n   */\n  storage: AsyncStorage<string> | undefined | null\n  /** The key to use when storing the cache */\n  key?: string\n  /** To avoid spamming,\n   * pass a time in ms to throttle saving the cache to disk */\n  throttleTime?: number\n  /**\n   * How to serialize the data to storage.\n   * @default `JSON.stringify`\n   */\n  serialize?: (client: PersistedClient) => MaybePromise<string>\n  /**\n   * How to deserialize the data from storage.\n   * @default `JSON.parse`\n   */\n  deserialize?: (cachedString: string) => MaybePromise<PersistedClient>\n\n  retry?: AsyncPersistRetryer\n}\n\nexport const createAsyncStoragePersister = ({\n  storage,\n  key = `REACT_QUERY_OFFLINE_CACHE`,\n  throttleTime = 1000,\n  serialize = JSON.stringify,\n  deserialize = JSON.parse,\n  retry,\n}: CreateAsyncStoragePersisterOptions): Persister => {\n  if (storage) {\n    const trySave = async (\n      persistedClient: PersistedClient,\n    ): Promise<Error | undefined> => {\n      try {\n        const serialized = await serialize(persistedClient)\n        await storage.setItem(key, serialized)\n        return\n      } catch (error) {\n        return error as Error\n      }\n    }\n\n    return {\n      persistClient: asyncThrottle(\n        async (persistedClient) => {\n          let client: PersistedClient | undefined = persistedClient\n          let error = await trySave(client)\n          let errorCount = 0\n          while (error && client) {\n            errorCount++\n            client = await retry?.({\n              persistedClient: client,\n              error,\n              errorCount,\n            })\n\n            if (client) {\n              error = await trySave(client)\n            }\n          }\n        },\n        { interval: throttleTime },\n      ),\n      restoreClient: async () => {\n        const cacheString = await storage.getItem(key)\n\n        if (!cacheString) {\n          return\n        }\n\n        return await deserialize(cacheString)\n      },\n      removeClient: () => storage.removeItem(key),\n    }\n  }\n\n  return {\n    persistClient: noop,\n    restoreClient: () => Promise.resolve(undefined),\n    removeClient: noop,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAA8B;AAC9B,mBAAqB;AAwCd,IAAM,8BAA8B,CAAC;AAAA,EAC1C;AAAA,EACA,MAAM;AAAA,EACN,eAAe;AAAA,EACf,YAAY,KAAK;AAAA,EACjB,cAAc,KAAK;AAAA,EACnB;AACF,MAAqD;AACnD,MAAI,SAAS;AACX,UAAM,UAAU,OACd,oBAC+B;AAC/B,UAAI;AACF,cAAM,aAAa,MAAM,UAAU,eAAe;AAClD,cAAM,QAAQ,QAAQ,KAAK,UAAU;AACrC;AAAA,MACF,SAAS,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,MACL,mBAAe;AAAA,QACb,OAAO,oBAAoB;AACzB,cAAI,SAAsC;AAC1C,cAAI,QAAQ,MAAM,QAAQ,MAAM;AAChC,cAAI,aAAa;AACjB,iBAAO,SAAS,QAAQ;AACtB;AACA,qBAAS,OAAM,+BAAQ;AAAA,cACrB,iBAAiB;AAAA,cACjB;AAAA,cACA;AAAA,YACF;AAEA,gBAAI,QAAQ;AACV,sBAAQ,MAAM,QAAQ,MAAM;AAAA,YAC9B;AAAA,UACF;AAAA,QACF;AAAA,QACA,EAAE,UAAU,aAAa;AAAA,MAC3B;AAAA,MACA,eAAe,YAAY;AACzB,cAAM,cAAc,MAAM,QAAQ,QAAQ,GAAG;AAE7C,YAAI,CAAC,aAAa;AAChB;AAAA,QACF;AAEA,eAAO,MAAM,YAAY,WAAW;AAAA,MACtC;AAAA,MACA,cAAc,MAAM,QAAQ,WAAW,GAAG;AAAA,IAC5C;AAAA,EACF;AAEA,SAAO;AAAA,IACL,eAAe;AAAA,IACf,eAAe,MAAM,QAAQ,QAAQ,MAAS;AAAA,IAC9C,cAAc;AAAA,EAChB;AACF;", "names": []}