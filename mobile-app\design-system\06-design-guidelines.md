# DESIGN GUIDELINES - APPLICATION LIVRAISON AFRIQUE

## PHILOSOPHIE DU DESIGN

### Principes Fondamentaux Ubuntu
Notre design system s'inspire de la philosophie Ubuntu : "Je suis parce que nous sommes". Chaque décision de design doit servir la communauté et favoriser l'inclusion.

#### 1. SIMPLICITÉ INTENTIONNELLE
- **Réduction cognitive** : Interfaces épurées pour tous niveaux de littératie
- **Hiérarchie claire** : Information organisée par ordre d'importance
- **Actions évidentes** : Boutons et interactions immédiatement compréhensibles

#### 2. ACCESSIBILITÉ UNIVERSELLE
- **Inclusion totale** : Utilisable par tous, partout, tout le temps
- **Adaptabilité** : S'ajuste aux capacités et contextes variés
- **Résilience** : Fonctionne dans toutes les conditions

#### 3. PERFORMANCE OPTIMISÉE
- **<PERSON>ffica<PERSON><PERSON> réseau** : Minimal data usage
- **Batterie préservée** : Animations et couleurs optimisées
- **Hardware adapté** : Performance sur appareils bas de gamme

## GUIDELINES COULEURS

### Palette Primaire - Orange Soleil
**Utilisation :** Actions principales, éléments interactifs, brand identity

```css
/* Recommandations d'usage */
--primary-500: #F97316; /* Boutons principaux, liens actifs */
--primary-100: #FFEDD5; /* Backgrounds subtils, états hover */
--primary-700: #C2410C; /* États pressed, texte sur fond clair */
```

**Bonnes pratiques :**
- ✅ Utiliser pour les CTA principaux
- ✅ Appliquer aux éléments de navigation actifs
- ✅ Employer pour les indicateurs de progression
- ❌ Éviter sur de grandes surfaces
- ❌ Ne pas utiliser pour le texte de lecture

### Palette Secondaire - Vert Baobab
**Utilisation :** États de succès, éléments naturels, actions secondaires

```css
/* Recommandations d'usage */
--secondary-500: #22C55E; /* Confirmations, succès */
--secondary-100: #DCFCE7; /* Backgrounds de succès */
--secondary-700: #15803D; /* Texte de confirmation */
```

### Couleurs Sémantiques
**Codes couleur universels pour feedback utilisateur**

#### Succès (Vert Malachite)
- **Usage :** Confirmations, validations, états positifs
- **Contraste :** Minimum 4.5:1 sur fond blanc
- **Associations :** Croissance, nature, prospérité

#### Attention (Jaune Safran)
- **Usage :** Avertissements, actions requises
- **Contraste :** Minimum 3:1 pour texte large
- **Associations :** Soleil, richesse, attention

#### Erreur (Rouge Corail)
- **Usage :** Erreurs, échecs, actions destructives
- **Contraste :** Minimum 4.5:1 sur fond blanc
- **Associations :** Urgence, arrêt, danger

### Mode Sombre - Économie Batterie
**Optimisé pour écrans OLED et économie d'énergie**

```css
/* Palette sombre */
--dark-bg-primary: #000000;    /* Noir pur OLED */
--dark-bg-secondary: #0F0F0F;  /* Noir profond */
--dark-text-primary: #FFFFFF;  /* Blanc pur */
--dark-text-secondary: #D4D4D4; /* Gris clair */
```

**Avantages :**
- 🔋 Économie batterie jusqu'à 30% sur OLED
- 👁️ Réduction fatigue oculaire en faible luminosité
- 🌙 Meilleure visibilité nocturne
- ⚡ Performance améliorée sur hardware limité

## GUIDELINES TYPOGRAPHIE

### Hiérarchie Typographique
**Système à 5 niveaux pour clarté maximale**

#### Niveau 1 - Display (Héros)
```css
font-size: 32px;
font-weight: 700;
line-height: 1.25;
usage: "Titres principaux, écrans d'accueil"
```

#### Niveau 2 - Heading (Sections)
```css
font-size: 24px;
font-weight: 600;
line-height: 1.3;
usage: "Titres de sections, headers"
```

#### Niveau 3 - Body (Contenu)
```css
font-size: 16px;
font-weight: 400;
line-height: 1.5;
usage: "Texte principal, descriptions"
```

#### Niveau 4 - Label (Métadonnées)
```css
font-size: 14px;
font-weight: 500;
line-height: 1.4;
usage: "Labels, boutons, navigation"
```

#### Niveau 5 - Caption (Détails)
```css
font-size: 12px;
font-weight: 400;
line-height: 1.3;
usage: "Timestamps, métadonnées"
```

### Lisibilité Optimisée
**Adaptations pour contexte africain**

#### Tailles Minimales
- **Texte de lecture :** 16px minimum
- **Boutons :** 16px minimum
- **Labels :** 14px minimum
- **Métadonnées :** 12px minimum

#### Contrastes Élevés
- **Texte principal :** 7:1 (AAA)
- **Texte secondaire :** 4.5:1 (AA)
- **Texte large :** 3:1 (AA Large)

#### Espacement Généreux
- **Interligne :** 1.5x minimum pour texte de lecture
- **Espacement lettres :** 0.025em pour améliorer lisibilité
- **Marges :** 16px minimum entre blocs de texte

## GUIDELINES ESPACEMENT

### Système 8pt Grid
**Harmonie visuelle et cohérence**

```css
/* Échelle d'espacement */
--space-xs: 4px;   /* Micro-espacements */
--space-sm: 8px;   /* Espacements serrés */
--space-md: 16px;  /* Espacement standard */
--space-lg: 24px;  /* Espacements larges */
--space-xl: 32px;  /* Séparations importantes */
--space-xxl: 48px; /* Séparations majeures */
```

### Zones de Touch
**Optimisées pour doigts et usage une main**

#### Tailles Cibles
- **Minimum :** 44px × 44px (Apple HIG)
- **Confortable :** 48px × 48px (Material Design)
- **Optimal :** 56px × 56px (recommandé Afrique)
- **Large :** 64px × 64px (seniors, accessibilité)

#### Espacements Touch
- **Entre boutons :** 8px minimum
- **Marges écran :** 16px minimum
- **Zone de préhension :** 72px depuis bords

### Layout Responsive
**Adaptation multi-device**

#### Breakpoints
```css
/* Mobile first approach */
--mobile-s: 320px;  /* Petits téléphones */
--mobile-m: 375px;  /* Téléphones standard */
--mobile-l: 414px;  /* Grands téléphones */
--tablet: 768px;    /* Tablettes */
--desktop: 1024px;  /* Desktop */
```

## GUIDELINES ICONOGRAPHIE

### Principes Iconographiques
**Universalité et reconnaissance culturelle**

#### Style Visuel
- **Trait :** 2px standard, 1.5px pour petites tailles
- **Style :** Outline, minimal, géométrique
- **Coins :** Arrondis (2px radius)
- **Proportions :** Carrées, centrées dans grille 24×24

#### Tailles Standard
```css
--icon-xs: 16px;  /* Inline avec texte */
--icon-sm: 20px;  /* Boutons secondaires */
--icon-md: 24px;  /* Boutons principaux */
--icon-lg: 32px;  /* Headers, navigation */
--icon-xl: 48px;  /* Illustrations, états vides */
```

### Iconographie Culturelle
**Symboles adaptés au contexte africain**

#### Transports
- **Moto :** Transport urbain dominant
- **Vélo :** Transport écologique émergent
- **Marche :** Transport rural/périurbain
- **Taxi :** Transport collectif

#### Commerce
- **Marché :** Commerce traditionnel
- **Boutique :** Commerce moderne
- **Pharmacie :** Santé accessible
- **Restaurant :** Restauration locale

#### Paiement
- **Mobile Money :** Dominant en Afrique
- **Espèces :** Toujours important
- **Carte :** Émergent urbain

## GUIDELINES INTERACTIONS

### Feedback Utilisateur
**Retour immédiat et clair**

#### États Visuels
```css
/* États boutons */
.button {
  /* Normal */
  opacity: 1;
  transform: scale(1);
  
  /* Hover/Focus */
  opacity: 0.9;
  
  /* Pressed */
  opacity: 0.8;
  transform: scale(0.98);
  
  /* Disabled */
  opacity: 0.5;
}
```

#### Animations
- **Durée :** 150-250ms (rapide et fluide)
- **Courbe :** ease-out (naturelle)
- **Propriétés :** opacity, transform (performance)

### Gestuelle Intuitive
**Patterns familiers et forgiveness**

#### Gestes Supportés
- **Tap :** Action principale
- **Long press :** Actions secondaires/contextuelles
- **Swipe :** Navigation, suppression
- **Pull to refresh :** Actualisation
- **Pinch to zoom :** Cartes, images

#### Zones de Confort
- **Pouce droit :** 75% utilisateurs
- **Zone facile :** Bas droite écran
- **Zone difficile :** Haut gauche écran
- **Navigation :** Bas d'écran privilégié

## GUIDELINES ACCESSIBILITÉ

### Contrastes et Visibilité
**Lisibilité maximale en toutes conditions**

#### Ratios de Contraste
- **AAA Normal :** 7:1 (texte < 18px)
- **AAA Large :** 4.5:1 (texte ≥ 18px)
- **AA Normal :** 4.5:1 (minimum légal)
- **AA Large :** 3:1 (minimum légal large)

#### Visibilité Solaire
- **Luminosité :** Contrastes élevés privilégiés
- **Couleurs :** Éviter bleus clairs, jaunes pâles
- **Texte :** Noir sur blanc optimal
- **Ombres :** Subtiles pour profondeur

### Support Multi-Sensoriel
**Au-delà du visuel**

#### Feedback Haptique
- **Succès :** Vibration courte (100ms)
- **Erreur :** Vibration double (100ms + 100ms)
- **Navigation :** Vibration légère (50ms)

#### Support Audio
- **Screen readers :** Labels descriptifs
- **Feedback vocal :** Confirmations importantes
- **Sons système :** Notifications discrètes

### Adaptabilité
**Personnalisation selon besoins**

#### Options Utilisateur
- **Taille texte :** 75% à 150% de base
- **Contraste élevé :** Mode haute visibilité
- **Animations réduites :** Respect prefer-reduced-motion
- **Mode daltonien :** Patterns en plus des couleurs

## GUIDELINES PERFORMANCE

### Optimisations Visuelles
**Performance sur hardware limité**

#### Images
- **Format :** WebP avec fallback JPEG
- **Compression :** 80% qualité standard
- **Lazy loading :** Images hors viewport
- **Responsive :** Tailles adaptées device

#### Animations
- **Propriétés :** transform, opacity uniquement
- **GPU :** will-change pour animations complexes
- **Durée :** 150-250ms maximum
- **FPS :** 60fps cible, 30fps acceptable

### Économie Ressources
**Batterie et data préservées**

#### Mode Sombre
- **OLED :** Noir pur (#000000)
- **Économie :** 20-30% batterie
- **Activation :** Automatique 20h-6h

#### Data Usage
- **Images :** Compression aggressive
- **Fonts :** Subset caractères utilisés
- **Icons :** SVG vectoriels
- **Cache :** Stratégie aggressive

## VALIDATION ET TESTS

### Checklist Design
**Validation avant implémentation**

#### Accessibilité
- [ ] Contrastes validés (WebAIM)
- [ ] Navigation clavier possible
- [ ] Screen reader compatible
- [ ] Touch targets ≥ 44px

#### Performance
- [ ] Images optimisées
- [ ] Animations fluides
- [ ] Chargement < 3s sur 3G
- [ ] Batterie préservée

#### Usabilité
- [ ] Navigation intuitive
- [ ] Feedback immédiat
- [ ] Erreurs explicites
- [ ] Succès confirmés

### Tests Utilisateurs
**Validation terrain**

#### Contextes de Test
- **Extérieur :** Visibilité solaire
- **Mouvement :** Usage en transport
- **Distraction :** Environnement bruyant
- **Stress :** Situations urgentes

#### Métriques Succès
- **Task completion :** >90%
- **Error rate :** <5%
- **Time on task :** <30s actions simples
- **Satisfaction :** SUS score >70

Ces guidelines garantissent une expérience utilisateur optimale, accessible et performante pour tous les utilisateurs africains, quels que soient leur niveau technique, leur device ou leur contexte d'usage.
