-- =====================================================
-- TRIGGERS ET FONCTIONS
-- =====================================================

-- Fonction pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Fonction pour générer un numéro de commande unique
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TRIGGER AS $$
DECLARE
    date_part TEXT;
    sequence_part TEXT;
    new_number TEXT;
BEGIN
    -- Format: ORD-YYYYMMDD-XXXX
    date_part := TO_CHAR(NOW(), 'YYYYMMDD');
    
    -- Obtenir le prochain numéro de séquence pour aujourd'hui
    SELECT LPAD(
        (COUNT(*) + 1)::TEXT, 
        4, 
        '0'
    ) INTO sequence_part
    FROM orders 
    WHERE DATE(created_at) = CURRENT_DATE;
    
    new_number := 'ORD-' || date_part || '-' || sequence_part;
    
    -- Vérifier l'unicité (au cas où)
    WHILE EXISTS (SELECT 1 FROM orders WHERE order_number = new_number) LOOP
        sequence_part := LPAD((sequence_part::INTEGER + 1)::TEXT, 4, '0');
        new_number := 'ORD-' || date_part || '-' || sequence_part;
    END LOOP;
    
    NEW.order_number := new_number;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Fonction pour générer un code de tracking
CREATE OR REPLACE FUNCTION generate_tracking_code()
RETURNS TRIGGER AS $$
DECLARE
    code TEXT;
BEGIN
    -- Générer un code de 8 caractères alphanumériques
    code := UPPER(SUBSTRING(MD5(RANDOM()::TEXT || NOW()::TEXT) FROM 1 FOR 8));
    
    -- Vérifier l'unicité
    WHILE EXISTS (SELECT 1 FROM orders WHERE tracking_code = code) LOOP
        code := UPPER(SUBSTRING(MD5(RANDOM()::TEXT || NOW()::TEXT) FROM 1 FOR 8));
    END LOOP;
    
    NEW.tracking_code := code;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Fonction pour calculer le total d'une commande
CREATE OR REPLACE FUNCTION calculate_order_total()
RETURNS TRIGGER AS $$
BEGIN
    NEW.total_amount := NEW.subtotal_amount + NEW.delivery_fee + NEW.service_fee + NEW.tax_amount - NEW.discount_amount + NEW.tip_amount;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Fonction pour mettre à jour le sous-total d'une commande
CREATE OR REPLACE FUNCTION update_order_subtotal()
RETURNS TRIGGER AS $$
DECLARE
    new_subtotal INTEGER;
BEGIN
    -- Calculer le nouveau sous-total
    SELECT COALESCE(SUM(total_price), 0)
    INTO new_subtotal
    FROM order_items
    WHERE order_id = COALESCE(NEW.order_id, OLD.order_id);
    
    -- Mettre à jour la commande
    UPDATE orders 
    SET subtotal_amount = new_subtotal,
        updated_at = NOW()
    WHERE id = COALESCE(NEW.order_id, OLD.order_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Fonction pour mettre à jour les ratings moyens
CREATE OR REPLACE FUNCTION update_average_rating()
RETURNS TRIGGER AS $$
DECLARE
    target_table TEXT;
    target_id UUID;
    avg_rating DECIMAL(3,2);
    total_count INTEGER;
BEGIN
    target_id := NEW.rated_id;
    
    -- Déterminer la table cible
    IF NEW.rated_type = 'merchant' THEN
        target_table := 'merchants';
    ELSIF NEW.rated_type = 'rider' THEN
        target_table := 'delivery_riders';
    ELSE
        RETURN NEW;
    END IF;
    
    -- Calculer la nouvelle moyenne
    SELECT 
        ROUND(AVG(rating)::NUMERIC, 2),
        COUNT(*)
    INTO avg_rating, total_count
    FROM ratings 
    WHERE rated_id = target_id AND rated_type = NEW.rated_type;
    
    -- Mettre à jour la table appropriée
    IF target_table = 'merchants' THEN
        UPDATE merchants 
        SET average_rating = avg_rating,
            total_ratings = total_count,
            updated_at = NOW()
        WHERE id = target_id;
    ELSIF target_table = 'delivery_riders' THEN
        UPDATE delivery_riders 
        SET average_rating = avg_rating,
            total_ratings = total_count,
            updated_at = NOW()
        WHERE id = target_id;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Fonction pour créer un log d'audit
CREATE OR REPLACE FUNCTION create_audit_log()
RETURNS TRIGGER AS $$
DECLARE
    operation_type TEXT;
    table_name TEXT;
    record_id UUID;
    old_data JSONB;
    new_data JSONB;
    changes_data JSONB;
BEGIN
    -- Déterminer le type d'opération
    IF TG_OP = 'INSERT' THEN
        operation_type := 'create';
        record_id := NEW.id;
        new_data := to_jsonb(NEW);
        old_data := NULL;
        changes_data := new_data;
    ELSIF TG_OP = 'UPDATE' THEN
        operation_type := 'update';
        record_id := NEW.id;
        old_data := to_jsonb(OLD);
        new_data := to_jsonb(NEW);
        -- Calculer les changements
        SELECT jsonb_object_agg(key, value)
        INTO changes_data
        FROM (
            SELECT key, value
            FROM jsonb_each(new_data)
            WHERE new_data->key IS DISTINCT FROM old_data->key
        ) t;
    ELSIF TG_OP = 'DELETE' THEN
        operation_type := 'delete';
        record_id := OLD.id;
        old_data := to_jsonb(OLD);
        new_data := NULL;
        changes_data := old_data;
    END IF;
    
    table_name := TG_TABLE_NAME;
    
    -- Insérer le log d'audit
    INSERT INTO audit_logs (
        action,
        resource_type,
        resource_id,
        old_values,
        new_values,
        changes
    ) VALUES (
        operation_type,
        table_name,
        record_id,
        old_data,
        new_data,
        changes_data
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Fonction pour mettre à jour le solde du portefeuille
CREATE OR REPLACE FUNCTION update_wallet_balance()
RETURNS TRIGGER AS $$
DECLARE
    wallet_record RECORD;
    new_balance INTEGER;
BEGIN
    -- Obtenir le portefeuille
    SELECT * INTO wallet_record
    FROM user_wallets
    WHERE id = NEW.wallet_id;
    
    -- Calculer le nouveau solde
    IF NEW.transaction_type = 'credit' THEN
        new_balance := wallet_record.balance + NEW.amount;
    ELSE
        new_balance := wallet_record.balance - NEW.amount;
    END IF;
    
    -- Vérifier que le solde ne devient pas négatif
    IF new_balance < 0 THEN
        RAISE EXCEPTION 'Insufficient wallet balance';
    END IF;
    
    -- Mettre à jour le solde avant et après dans la transaction
    NEW.balance_before := wallet_record.balance;
    NEW.balance_after := new_balance;
    
    -- Mettre à jour le portefeuille
    UPDATE user_wallets
    SET balance = new_balance,
        last_transaction_at = NOW(),
        updated_at = NOW()
    WHERE id = NEW.wallet_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Fonction pour créer une notification
CREATE OR REPLACE FUNCTION create_notification(
    p_user_id UUID,
    p_notification_type notification_type,
    p_title TEXT,
    p_body TEXT,
    p_data JSONB DEFAULT '{}',
    p_reference_type TEXT DEFAULT NULL,
    p_reference_id UUID DEFAULT NULL,
    p_channels notification_channel[] DEFAULT ARRAY['push']
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
    user_prefs RECORD;
    final_channels notification_channel[];
BEGIN
    -- Obtenir les préférences utilisateur
    SELECT * INTO user_prefs
    FROM notification_preferences
    WHERE user_id = p_user_id AND notification_type = p_notification_type;
    
    -- Si pas de préférences, utiliser les canaux par défaut
    IF user_prefs IS NULL THEN
        final_channels := p_channels;
    ELSE
        -- Intersection des canaux demandés et autorisés
        SELECT ARRAY(
            SELECT UNNEST(p_channels)
            INTERSECT
            SELECT UNNEST(user_prefs.enabled_channels)
        ) INTO final_channels;
    END IF;
    
    -- Ne pas créer de notification si aucun canal autorisé
    IF array_length(final_channels, 1) IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Créer la notification
    INSERT INTO notifications (
        user_id,
        notification_type,
        title,
        body,
        data,
        reference_type,
        reference_id,
        channels
    ) VALUES (
        p_user_id,
        p_notification_type,
        p_title,
        p_body,
        p_data,
        p_reference_type,
        p_reference_id,
        final_channels
    ) RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ language 'plpgsql';

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Triggers pour updated_at
CREATE TRIGGER trigger_update_countries_updated_at
    BEFORE UPDATE ON countries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_cities_updated_at
    BEFORE UPDATE ON cities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_delivery_zones_updated_at
    BEFORE UPDATE ON delivery_zones
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_addresses_updated_at
    BEFORE UPDATE ON addresses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_merchants_updated_at
    BEFORE UPDATE ON merchants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_orders_updated_at
    BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_delivery_riders_updated_at
    BEFORE UPDATE ON delivery_riders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Triggers pour les commandes
CREATE TRIGGER trigger_generate_order_number
    BEFORE INSERT ON orders
    FOR EACH ROW EXECUTE FUNCTION generate_order_number();

CREATE TRIGGER trigger_generate_tracking_code
    BEFORE INSERT ON orders
    FOR EACH ROW EXECUTE FUNCTION generate_tracking_code();

CREATE TRIGGER trigger_calculate_order_total
    BEFORE INSERT OR UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION calculate_order_total();

-- Triggers pour les articles de commande
CREATE TRIGGER trigger_update_order_subtotal_insert
    AFTER INSERT ON order_items
    FOR EACH ROW EXECUTE FUNCTION update_order_subtotal();

CREATE TRIGGER trigger_update_order_subtotal_update
    AFTER UPDATE ON order_items
    FOR EACH ROW EXECUTE FUNCTION update_order_subtotal();

CREATE TRIGGER trigger_update_order_subtotal_delete
    AFTER DELETE ON order_items
    FOR EACH ROW EXECUTE FUNCTION update_order_subtotal();

-- Triggers pour les ratings
CREATE TRIGGER trigger_update_average_rating
    AFTER INSERT ON ratings
    FOR EACH ROW EXECUTE FUNCTION update_average_rating();

-- Triggers pour les transactions de portefeuille
CREATE TRIGGER trigger_update_wallet_balance
    BEFORE INSERT ON wallet_transactions
    FOR EACH ROW EXECUTE FUNCTION update_wallet_balance();

-- Triggers d'audit pour les tables importantes
CREATE TRIGGER trigger_audit_orders
    AFTER INSERT OR UPDATE OR DELETE ON orders
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

CREATE TRIGGER trigger_audit_payments
    AFTER INSERT OR UPDATE OR DELETE ON payments
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

CREATE TRIGGER trigger_audit_profiles
    AFTER INSERT OR UPDATE OR DELETE ON profiles
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

CREATE TRIGGER trigger_audit_merchants
    AFTER INSERT OR UPDATE OR DELETE ON merchants
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();

CREATE TRIGGER trigger_audit_products
    AFTER INSERT OR UPDATE OR DELETE ON products
    FOR EACH ROW EXECUTE FUNCTION create_audit_log();
