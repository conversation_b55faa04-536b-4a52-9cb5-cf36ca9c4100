// =====================================================
// PAGE DE TEST - POUR TESTER L'ONBOARDING
// =====================================================

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../constants/Colors';

export default function TestScreen() {
  const resetOnboarding = async () => {
    try {
      await AsyncStorage.removeItem('hasSeenOnboarding');
      Alert.alert(
        'Succès',
        'L\'onboarding a été réinitialisé. Redé<PERSON>rez l\'application pour voir l\'onboarding.',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de réinitialiser l\'onboarding');
    }
  };

  const goToOnboarding = () => {
    router.push('/(auth)/onboarding');
  };

  const goToSplash = () => {
    router.replace('/');
  };

  const checkOnboardingStatus = async () => {
    try {
      const hasSeenOnboarding = await AsyncStorage.getItem('hasSeenOnboarding');
      Alert.alert(
        'Statut Onboarding',
        hasSeenOnboarding
          ? 'L\'utilisateur a déjà vu l\'onboarding'
          : 'L\'utilisateur n\'a pas encore vu l\'onboarding'
      );
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de vérifier le statut');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>🧪 Page de Test</Text>
        <Text style={styles.subtitle}>
          Utilisez cette page pour tester les différents écrans
        </Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={checkOnboardingStatus}>
            <Text style={styles.buttonText}>📊 Vérifier statut onboarding</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={resetOnboarding}>
            <Text style={styles.buttonText}>🔄 Réinitialiser onboarding</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={goToOnboarding}>
            <Text style={styles.buttonText}>👀 Voir onboarding</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.button} onPress={goToSplash}>
            <Text style={styles.buttonText}>🚀 Retour au splash</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.info}>
          <Text style={styles.infoTitle}>ℹ️ Instructions :</Text>
          <Text style={styles.infoText}>
            1. Cliquez sur "Réinitialiser onboarding" pour effacer le flag
          </Text>
          <Text style={styles.infoText}>
            2. Cliquez sur "Retour au splash" pour redémarrer l'app
          </Text>
          <Text style={styles.infoText}>
            3. L'onboarding devrait s'afficher automatiquement
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutral[50],
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.neutral[900],
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.neutral[600],
    textAlign: 'center',
    marginBottom: 40,
  },
  buttonContainer: {
    gap: 16,
    marginBottom: 40,
  },
  button: {
    backgroundColor: Colors.secondary[500],
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonText: {
    color: Colors.neutral[0],
    fontSize: 16,
    fontWeight: '600',
  },
  info: {
    backgroundColor: Colors.neutral[100],
    padding: 20,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.secondary[500],
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral[900],
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: Colors.neutral[700],
    marginBottom: 4,
    lineHeight: 20,
  },
});
