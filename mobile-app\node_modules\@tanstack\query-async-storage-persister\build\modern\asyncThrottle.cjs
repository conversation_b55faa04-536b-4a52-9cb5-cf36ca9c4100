"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/asyncThrottle.ts
var asyncThrottle_exports = {};
__export(asyncThrottle_exports, {
  asyncThrottle: () => asyncThrottle
});
module.exports = __toCommonJS(asyncThrottle_exports);
var import_utils = require("./utils.cjs");
function asyncThrottle(func, { interval = 1e3, onError = import_utils.noop } = {}) {
  if (typeof func !== "function") throw new Error("argument is not function.");
  let nextExecutionTime = 0;
  let lastArgs = null;
  let isExecuting = false;
  let isScheduled = false;
  return async (...args) => {
    lastArgs = args;
    if (isScheduled) return;
    isScheduled = true;
    while (isExecuting) {
      await new Promise((done) => setTimeout(done, interval));
    }
    while (Date.now() < nextExecutionTime) {
      await new Promise(
        (done) => setTimeout(done, nextExecutionTime - Date.now())
      );
    }
    isScheduled = false;
    isExecuting = true;
    try {
      await func(...lastArgs);
    } catch (error) {
      try {
        onError(error);
      } catch {
      }
    }
    nextExecutionTime = Date.now() + interval;
    isExecuting = false;
  };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  asyncThrottle
});
//# sourceMappingURL=asyncThrottle.cjs.map