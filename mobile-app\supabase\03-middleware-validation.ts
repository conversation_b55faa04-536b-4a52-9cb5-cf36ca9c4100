// =====================================================
// MIDDLEWARE DE VALIDATION ET SÉCURISATION
// =====================================================

import { createClient } from '@supabase/supabase-js'

// Types pour la validation
interface ValidationRule {
  field: string
  type: 'string' | 'number' | 'email' | 'phone' | 'uuid' | 'array' | 'object' | 'boolean'
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  enum?: string[]
  custom?: (value: any) => boolean | string
}

interface RateLimitConfig {
  windowMs: number
  maxRequests: number
  keyGenerator?: (req: Request) => string
  skipSuccessfulRequests?: boolean
}

// Cache pour le rate limiting
const rateLimitCache = new Map<string, { count: number; resetTime: number }>()

// =====================================================
// MIDDLEWARE DE VALIDATION DES DONNÉES
// =====================================================

export function validateRequest(rules: ValidationRule[]) {
  return async (req: Request): Promise<{ isValid: boolean; errors: string[]; data?: any }> => {
    const errors: string[] = []
    let data: any

    try {
      data = await req.json()
    } catch (error) {
      return { isValid: false, errors: ['Invalid JSON format'] }
    }

    for (const rule of rules) {
      const value = data[rule.field]
      const fieldError = validateField(rule.field, value, rule)
      
      if (fieldError) {
        errors.push(fieldError)
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      data: errors.length === 0 ? data : undefined
    }
  }
}

function validateField(fieldName: string, value: any, rule: ValidationRule): string | null {
  // Check required
  if (rule.required && (value === undefined || value === null || value === '')) {
    return `${fieldName} is required`
  }

  // Skip validation if field is not required and empty
  if (!rule.required && (value === undefined || value === null || value === '')) {
    return null
  }

  // Type validation
  switch (rule.type) {
    case 'string':
      if (typeof value !== 'string') {
        return `${fieldName} must be a string`
      }
      if (rule.min && value.length < rule.min) {
        return `${fieldName} must be at least ${rule.min} characters`
      }
      if (rule.max && value.length > rule.max) {
        return `${fieldName} must be at most ${rule.max} characters`
      }
      if (rule.pattern && !rule.pattern.test(value)) {
        return `${fieldName} format is invalid`
      }
      break

    case 'number':
      if (typeof value !== 'number' || isNaN(value)) {
        return `${fieldName} must be a valid number`
      }
      if (rule.min !== undefined && value < rule.min) {
        return `${fieldName} must be at least ${rule.min}`
      }
      if (rule.max !== undefined && value > rule.max) {
        return `${fieldName} must be at most ${rule.max}`
      }
      break

    case 'email':
      if (typeof value !== 'string' || !isValidEmail(value)) {
        return `${fieldName} must be a valid email address`
      }
      break

    case 'phone':
      if (typeof value !== 'string' || !isValidPhone(value)) {
        return `${fieldName} must be a valid phone number`
      }
      break

    case 'uuid':
      if (typeof value !== 'string' || !isValidUUID(value)) {
        return `${fieldName} must be a valid UUID`
      }
      break

    case 'array':
      if (!Array.isArray(value)) {
        return `${fieldName} must be an array`
      }
      if (rule.min && value.length < rule.min) {
        return `${fieldName} must have at least ${rule.min} items`
      }
      if (rule.max && value.length > rule.max) {
        return `${fieldName} must have at most ${rule.max} items`
      }
      break

    case 'object':
      if (typeof value !== 'object' || Array.isArray(value) || value === null) {
        return `${fieldName} must be an object`
      }
      break

    case 'boolean':
      if (typeof value !== 'boolean') {
        return `${fieldName} must be a boolean`
      }
      break
  }

  // Enum validation
  if (rule.enum && !rule.enum.includes(value)) {
    return `${fieldName} must be one of: ${rule.enum.join(', ')}`
  }

  // Custom validation
  if (rule.custom) {
    const customResult = rule.custom(value)
    if (customResult !== true) {
      return typeof customResult === 'string' ? customResult : `${fieldName} is invalid`
    }
  }

  return null
}

// =====================================================
// MIDDLEWARE DE RATE LIMITING
// =====================================================

export function rateLimit(config: RateLimitConfig) {
  return async (req: Request): Promise<{ allowed: boolean; resetTime?: number; remaining?: number }> => {
    const key = config.keyGenerator ? config.keyGenerator(req) : getDefaultKey(req)
    const now = Date.now()
    
    // Clean expired entries
    cleanExpiredEntries(now)
    
    const entry = rateLimitCache.get(key)
    
    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired one
      const resetTime = now + config.windowMs
      rateLimitCache.set(key, { count: 1, resetTime })
      
      return {
        allowed: true,
        resetTime,
        remaining: config.maxRequests - 1
      }
    }
    
    if (entry.count >= config.maxRequests) {
      return {
        allowed: false,
        resetTime: entry.resetTime,
        remaining: 0
      }
    }
    
    // Increment counter
    entry.count++
    
    return {
      allowed: true,
      resetTime: entry.resetTime,
      remaining: config.maxRequests - entry.count
    }
  }
}

function getDefaultKey(req: Request): string {
  // Extract IP from various headers
  const forwarded = req.headers.get('x-forwarded-for')
  const realIp = req.headers.get('x-real-ip')
  const cfConnectingIp = req.headers.get('cf-connecting-ip')
  
  return forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown'
}

function cleanExpiredEntries(now: number) {
  for (const [key, entry] of rateLimitCache.entries()) {
    if (now > entry.resetTime) {
      rateLimitCache.delete(key)
    }
  }
}

// =====================================================
// MIDDLEWARE D'AUTHENTIFICATION
// =====================================================

export async function authenticateUser(req: Request, supabase: any): Promise<{
  authenticated: boolean
  user?: any
  profile?: any
  error?: string
}> {
  const authHeader = req.headers.get('Authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false, error: 'Missing or invalid authorization header' }
  }

  const token = authHeader.substring(7)

  try {
    // Verify JWT token
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return { authenticated: false, error: 'Invalid or expired token' }
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return { authenticated: false, error: 'User profile not found' }
    }

    // Check if user is active
    if (!profile.is_active) {
      return { authenticated: false, error: 'User account is deactivated' }
    }

    return {
      authenticated: true,
      user,
      profile
    }
  } catch (error) {
    return { authenticated: false, error: 'Authentication failed' }
  }
}

// =====================================================
// MIDDLEWARE D'AUTORISATION
// =====================================================

export function requireRole(allowedRoles: string[]) {
  return (profile: any): { authorized: boolean; error?: string } => {
    if (!profile) {
      return { authorized: false, error: 'User profile required' }
    }

    if (!allowedRoles.includes(profile.role)) {
      return { authorized: false, error: 'Insufficient permissions' }
    }

    return { authorized: true }
  }
}

export function requireOwnership(resourceUserId: string, currentUserId: string): {
  authorized: boolean
  error?: string
} {
  if (resourceUserId !== currentUserId) {
    return { authorized: false, error: 'Access denied: not resource owner' }
  }

  return { authorized: true }
}

// =====================================================
// MIDDLEWARE DE SÉCURITÉ
// =====================================================

export function sanitizeInput(data: any): any {
  if (typeof data === 'string') {
    // Remove potentially dangerous characters
    return data
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim()
  }

  if (Array.isArray(data)) {
    return data.map(sanitizeInput)
  }

  if (typeof data === 'object' && data !== null) {
    const sanitized: any = {}
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value)
    }
    return sanitized
  }

  return data
}

export function validateCSRF(req: Request, expectedToken: string): boolean {
  const csrfToken = req.headers.get('x-csrf-token')
  return csrfToken === expectedToken
}

// =====================================================
// UTILITAIRES DE VALIDATION
// =====================================================

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

function isValidPhone(phone: string): boolean {
  // International phone number format
  const phoneRegex = /^\+[1-9]\d{1,14}$/
  return phoneRegex.test(phone)
}

function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

// =====================================================
// RÈGLES DE VALIDATION PRÉDÉFINIES
// =====================================================

export const ValidationRules = {
  // User validation
  userRegistration: [
    { field: 'email', type: 'email' as const, required: true },
    { field: 'password', type: 'string' as const, required: true, min: 8, max: 128 },
    { field: 'first_name', type: 'string' as const, required: true, min: 1, max: 100 },
    { field: 'last_name', type: 'string' as const, required: true, min: 1, max: 100 },
    { field: 'phone_number', type: 'phone' as const, required: true },
    { field: 'preferred_language', type: 'string' as const, enum: ['fr', 'en', 'wo', 'sw', 'ha'] }
  ],

  // Order validation
  createOrder: [
    { field: 'merchant_id', type: 'uuid' as const, required: true },
    { field: 'delivery_address_id', type: 'uuid' as const, required: true },
    { field: 'items', type: 'array' as const, required: true, min: 1 },
    { field: 'delivery_instructions', type: 'string' as const, max: 500 },
    { field: 'payment_method', type: 'string' as const, required: true, enum: ['mobile_money', 'card', 'cash'] }
  ],

  // Product validation
  createProduct: [
    { field: 'name', type: 'string' as const, required: true, min: 1, max: 200 },
    { field: 'description', type: 'string' as const, max: 1000 },
    { field: 'price', type: 'number' as const, required: true, min: 0 },
    { field: 'category_id', type: 'uuid' as const },
    { field: 'is_available', type: 'boolean' as const }
  ],

  // Payment validation
  processPayment: [
    { field: 'order_id', type: 'uuid' as const, required: true },
    { field: 'payment_method', type: 'string' as const, required: true },
    { field: 'amount', type: 'number' as const, required: true, min: 1 },
    { field: 'currency', type: 'string' as const, required: true, enum: ['XOF', 'KES', 'NGN', 'GHS'] }
  ]
}

// =====================================================
// CONFIGURATION RATE LIMITING
// =====================================================

export const RateLimitConfigs = {
  // API générale
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100
  },

  // Authentification
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    keyGenerator: (req: Request) => {
      const ip = getDefaultKey(req)
      const email = req.headers.get('x-user-email') || 'unknown'
      return `auth:${ip}:${email}`
    }
  },

  // Paiements
  payment: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 3,
    keyGenerator: (req: Request) => {
      const userId = req.headers.get('x-user-id') || getDefaultKey(req)
      return `payment:${userId}`
    }
  },

  // Notifications
  notification: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
    keyGenerator: (req: Request) => {
      const userId = req.headers.get('x-user-id') || getDefaultKey(req)
      return `notification:${userId}`
    }
  },

  // Recherche
  search: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30
  }
}

// =====================================================
// HELPER POUR EDGE FUNCTIONS
// =====================================================

export function createSecureHandler(
  handler: (req: Request, context: any) => Promise<Response>,
  options: {
    requireAuth?: boolean
    allowedRoles?: string[]
    rateLimitConfig?: RateLimitConfig
    validationRules?: ValidationRule[]
    requireCSRF?: boolean
  } = {}
) {
  return async (req: Request): Promise<Response> => {
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-csrf-token',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
    }

    // Handle CORS preflight
    if (req.method === 'OPTIONS') {
      return new Response('ok', { headers: corsHeaders })
    }

    try {
      const supabase = createClient(
        Deno.env.get('SUPABASE_URL') ?? '',
        Deno.env.get('SUPABASE_ANON_KEY') ?? ''
      )

      const context: any = { supabase }

      // Rate limiting
      if (options.rateLimitConfig) {
        const rateLimitResult = await rateLimit(options.rateLimitConfig)(req)
        if (!rateLimitResult.allowed) {
          return new Response(
            JSON.stringify({ error: 'Rate limit exceeded' }),
            {
              status: 429,
              headers: {
                ...corsHeaders,
                'Content-Type': 'application/json',
                'X-RateLimit-Reset': rateLimitResult.resetTime?.toString() || ''
              }
            }
          )
        }
      }

      // Authentication
      if (options.requireAuth) {
        const authResult = await authenticateUser(req, supabase)
        if (!authResult.authenticated) {
          return new Response(
            JSON.stringify({ error: authResult.error }),
            {
              status: 401,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }
        context.user = authResult.user
        context.profile = authResult.profile
      }

      // Authorization
      if (options.allowedRoles && context.profile) {
        const authzResult = requireRole(options.allowedRoles)(context.profile)
        if (!authzResult.authorized) {
          return new Response(
            JSON.stringify({ error: authzResult.error }),
            {
              status: 403,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }
      }

      // Input validation
      if (options.validationRules && ['POST', 'PUT', 'PATCH'].includes(req.method)) {
        const validationResult = await validateRequest(options.validationRules)(req)
        if (!validationResult.isValid) {
          return new Response(
            JSON.stringify({ error: 'Validation failed', details: validationResult.errors }),
            {
              status: 400,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          )
        }
        context.validatedData = sanitizeInput(validationResult.data)
      }

      // Call the actual handler
      const response = await handler(req, context)
      
      // Add security headers
      const securityHeaders = {
        ...corsHeaders,
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
      }

      // Merge with existing headers
      const existingHeaders = Object.fromEntries(response.headers.entries())
      
      return new Response(response.body, {
        status: response.status,
        headers: { ...securityHeaders, ...existingHeaders }
      })

    } catch (error) {
      console.error('Secure handler error:', error)
      
      return new Response(
        JSON.stringify({ error: 'Internal server error' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }
  }
}
