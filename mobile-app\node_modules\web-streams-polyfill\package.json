{"name": "web-streams-polyfill", "version": "4.1.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/ponyfill.js", "module": "dist/ponyfill.mjs", "types": "types/ponyfill.d.ts", "exports": {".": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./polyfill": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.js"}, "./polyfill/es5": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.es5.js"}, "./dist/*": "./dist/*", "./types/*": "./types/*", "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundlers", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.mjs", "test:wpt:chromium": "node ./test/wpt/browser/run.mjs --browser chromium", "test:wpt:firefox": "node ./test/wpt/browser/run.mjs --browser firefox", "test:bundlers": "npm run test:bundler:rollup && npm run test:bundler:webpack", "test:bundler:rollup": "cd test/rollup && npm install && npm test", "test:bundler:webpack": "cd test/webpack && npm install && npm test", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"*.mjs\" \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "prepare": "npm run build"}, "files": ["dist", "types"], "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@eslint/js": "^9.17.0", "@microsoft/api-extractor": "^7.48.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@stylistic/eslint-plugin": "^2.12.1", "@types/node": "^20.17.11", "eslint": "^9.17.0", "globals": "^15.14.0", "jasmine": "^5.5.0", "micromatch": "^4.0.8", "minimist": "^1.2.8", "playwright": "^1.49.1", "rollup": "^4.29.2", "st": "^3.0.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.19.0", "wpt-runner": "^6.0.0"}}