// =====================================================
// EXPO-NATIVE HTTP CLIENT FOR SUPABASE
// =====================================================

import AsyncStorage from '@react-native-async-storage/async-storage';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://ljsqjgacqrogcywqbquc.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxqc3FqZ2FjcXJvZ2N5d3FicXVjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxMjA1ODAsImV4cCI6MjA2MzY5NjU4MH0.ecAAebSetmzDeuxtxpX79qJrvNpNiNUGaGxJIItNQ9s';

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Simple HTTP client using Expo's native fetch
class ExpoSupabaseClient {
  private baseUrl: string;
  private apiKey: string;
  private accessToken: string | null = null;

  constructor(url: string, key: string) {
    this.baseUrl = url;
    this.apiKey = key;
  }

  private async getHeaders(): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'apikey': this.apiKey,
      'Authorization': `Bearer ${this.apiKey}`,
    };

    if (this.accessToken) {
      headers['Authorization'] = `Bearer ${this.accessToken}`;
    }

    return headers;
  }

  async setAccessToken(token: string | null) {
    this.accessToken = token;
    if (token) {
      await AsyncStorage.setItem('supabase_access_token', token);
    } else {
      await AsyncStorage.removeItem('supabase_access_token');
    }
  }

  async loadAccessToken() {
    try {
      const token = await AsyncStorage.getItem('supabase_access_token');
      this.accessToken = token;
      return token;
    } catch (error) {
      console.error('Error loading access token:', error);
      return null;
    }
  }

  // Database operations
  from(table: string) {
    return new ExpoSupabaseTable(this, table);
  }

  // Auth operations
  get auth() {
    return new ExpoSupabaseAuth(this);
  }

  async request(endpoint: string, options: RequestInit = {}) {
    const headers = await this.getHeaders();

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(`HTTP ${response.status}: ${error}`);
      }

      return response.json();
    } catch (error) {
      console.error('Supabase request error:', error);
      throw error;
    }
  }
}

class ExpoSupabaseTable {
  constructor(private client: ExpoSupabaseClient, private table: string) {}

  select(columns: string = '*') {
    return new ExpoSupabaseQuery(this.client, this.table, 'GET', columns);
  }

  insert(data: any) {
    return new ExpoSupabaseQuery(this.client, this.table, 'POST', '*', data);
  }

  update(data: any) {
    return new ExpoSupabaseQuery(this.client, this.table, 'PATCH', '*', data);
  }

  delete() {
    return new ExpoSupabaseQuery(this.client, this.table, 'DELETE');
  }
}

class ExpoSupabaseQuery {
  private filters: string[] = [];
  private orderBy: string[] = [];
  private limitCount?: number;
  private isSingle = false;

  constructor(
    private client: ExpoSupabaseClient,
    private table: string,
    private method: string,
    private columns: string = '*',
    private data?: any
  ) {}

  eq(column: string, value: any) {
    this.filters.push(`${column}=eq.${encodeURIComponent(value)}`);
    return this;
  }

  gte(column: string, value: any) {
    this.filters.push(`${column}=gte.${encodeURIComponent(value)}`);
    return this;
  }

  order(column: string, options: { ascending?: boolean } = {}) {
    const direction = options.ascending === false ? 'desc' : 'asc';
    this.orderBy.push(`${column}.${direction}`);
    return this;
  }

  limit(count: number) {
    this.limitCount = count;
    return this;
  }

  single() {
    this.isSingle = true;
    return this;
  }

  async execute() {
    let endpoint = `/rest/v1/${this.table}`;
    const params = new URLSearchParams();

    if (this.method === 'GET') {
      params.append('select', this.columns);
    }

    if (this.filters.length > 0) {
      this.filters.forEach(filter => {
        const [key, value] = filter.split('=');
        params.append(key, value);
      });
    }

    if (this.orderBy.length > 0) {
      params.append('order', this.orderBy.join(','));
    }

    if (this.limitCount) {
      params.append('limit', this.limitCount.toString());
    }

    if (params.toString()) {
      endpoint += `?${params.toString()}`;
    }

    const options: RequestInit = {
      method: this.method,
    };

    if (this.data && (this.method === 'POST' || this.method === 'PATCH')) {
      options.body = JSON.stringify(this.data);
      options.headers = {
        'Prefer': 'return=representation',
      };
    }

    const result = await this.client.request(endpoint, options);

    if (this.isSingle) {
      return { data: result[0] || null, error: null };
    }

    return { data: result, error: null };
  }
}

class ExpoSupabaseAuth {
  constructor(private client: ExpoSupabaseClient) {}

  async signUp(credentials: { phone: string; password: string }) {
    try {
      const result = await this.client.request('/auth/v1/signup', {
        method: 'POST',
        body: JSON.stringify(credentials),
      });

      if (result.access_token) {
        await this.client.setAccessToken(result.access_token);
      }

      return { data: result, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  async signInWithPassword(credentials: { phone: string; password: string }) {
    try {
      const result = await this.client.request('/auth/v1/token?grant_type=password', {
        method: 'POST',
        body: JSON.stringify(credentials),
      });

      if (result.access_token) {
        await this.client.setAccessToken(result.access_token);
      }

      return { data: result, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  async signOut() {
    try {
      await this.client.request('/auth/v1/logout', {
        method: 'POST',
      });

      await this.client.setAccessToken(null);
      return { error: null };
    } catch (error: any) {
      return { error: { message: error.message } };
    }
  }

  async getUser() {
    try {
      await this.client.loadAccessToken();
      const result = await this.client.request('/auth/v1/user');
      return { data: { user: result }, error: null };
    } catch (error: any) {
      return { data: { user: null }, error: { message: error.message } };
    }
  }
}

export const supabase = new ExpoSupabaseClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          first_name: string | null;
          last_name: string | null;
          phone: string | null;
          avatar_url: string | null;
          role: 'client' | 'merchant' | 'deliverer' | 'admin';
          country_code: string;
          language: string;
          currency: string;
          is_verified: boolean;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          first_name?: string | null;
          last_name?: string | null;
          phone?: string | null;
          avatar_url?: string | null;
          role?: 'client' | 'merchant' | 'deliverer' | 'admin';
          country_code?: string;
          language?: string;
          currency?: string;
          is_verified?: boolean;
          is_active?: boolean;
        };
        Update: {
          email?: string;
          first_name?: string | null;
          last_name?: string | null;
          phone?: string | null;
          avatar_url?: string | null;
          role?: 'client' | 'merchant' | 'deliverer' | 'admin';
          country_code?: string;
          language?: string;
          currency?: string;
          is_verified?: boolean;
          is_active?: boolean;
          updated_at?: string;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          name_en: string | null;
          name_fr: string;
          emoji: string | null;
          color: string | null;
          image_url: string | null;
          description: string | null;
          is_active: boolean;
          sort_order: number;
          created_at: string;
          updated_at: string;
        };
      };
      merchants: {
        Row: {
          id: string;
          owner_id: string | null;
          name: string;
          description: string | null;
          category_id: string | null;
          image_url: string | null;
          cover_image_url: string | null;
          phone: string | null;
          email: string | null;
          address: string | null;
          latitude: number | null;
          longitude: number | null;
          city: string | null;
          country_code: string;
          rating: number;
          total_reviews: number;
          delivery_fee: number;
          min_order_amount: number;
          delivery_time_min: number;
          delivery_time_max: number;
          is_open: boolean;
          is_verified: boolean;
          is_promoted: boolean;
          opening_hours: any | null;
          created_at: string;
          updated_at: string;
        };
      };
      products: {
        Row: {
          id: string;
          merchant_id: string;
          category_id: string | null;
          name: string;
          description: string | null;
          price: number;
          original_price: number | null;
          image_url: string | null;
          images: any;
          is_available: boolean;
          is_featured: boolean;
          preparation_time: number;
          nutritional_info: any | null;
          allergens: string[] | null;
          tags: string[] | null;
          sort_order: number;
          created_at: string;
          updated_at: string;
        };
      };
      promotions: {
        Row: {
          id: string;
          merchant_id: string | null;
          title: string;
          description: string | null;
          image_url: string | null;
          discount_type: 'percentage' | 'fixed_amount' | 'free_delivery';
          discount_value: number;
          min_order_amount: number;
          max_discount_amount: number | null;
          promo_code: string | null;
          usage_limit: number | null;
          usage_count: number;
          is_active: boolean;
          starts_at: string | null;
          expires_at: string | null;
          created_at: string;
          updated_at: string;
        };
      };
      orders: {
        Row: {
          id: string;
          customer_id: string | null;
          merchant_id: string | null;
          deliverer_id: string | null;
          order_number: string;
          status: string;
          subtotal: number;
          delivery_fee: number;
          service_fee: number;
          tax_amount: number;
          discount_amount: number;
          total_amount: number;
          currency: string;
          payment_method: string | null;
          payment_status: string;
          delivery_address: any;
          delivery_instructions: string | null;
          estimated_delivery_time: string | null;
          actual_delivery_time: string | null;
          notes: string | null;
          created_at: string;
          updated_at: string;
        };
      };
    };
  };
}
