// =====================================================
// SUPABASE CLIENT CONFIGURATION FOR REACT NATIVE
// =====================================================

import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://ljsqjgacqrogcywqbquc.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxqc3FqZ2FjcXJvZ2N5d3FicXVjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxMjA1ODAsImV4cCI6MjA2MzY5NjU4MH0.ecAAebSetmzDeuxtxpX79qJrvNpNiNUGaGxJIItNQ9s';

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  realtime: {
    // Completely disable realtime to avoid Node.js module issues
    params: {
      eventsPerSecond: 0,
    },
  },
  global: {
    headers: {
      'X-Client-Info': 'supabase-js-react-native',
    },
  },
});

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          first_name: string | null;
          last_name: string | null;
          phone: string | null;
          avatar_url: string | null;
          role: 'client' | 'merchant' | 'deliverer' | 'admin';
          country_code: string;
          language: string;
          currency: string;
          is_verified: boolean;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          first_name?: string | null;
          last_name?: string | null;
          phone?: string | null;
          avatar_url?: string | null;
          role?: 'client' | 'merchant' | 'deliverer' | 'admin';
          country_code?: string;
          language?: string;
          currency?: string;
          is_verified?: boolean;
          is_active?: boolean;
        };
        Update: {
          email?: string;
          first_name?: string | null;
          last_name?: string | null;
          phone?: string | null;
          avatar_url?: string | null;
          role?: 'client' | 'merchant' | 'deliverer' | 'admin';
          country_code?: string;
          language?: string;
          currency?: string;
          is_verified?: boolean;
          is_active?: boolean;
          updated_at?: string;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          name_en: string | null;
          name_fr: string;
          emoji: string | null;
          color: string | null;
          image_url: string | null;
          description: string | null;
          is_active: boolean;
          sort_order: number;
          created_at: string;
          updated_at: string;
        };
      };
      merchants: {
        Row: {
          id: string;
          owner_id: string | null;
          name: string;
          description: string | null;
          category_id: string | null;
          image_url: string | null;
          cover_image_url: string | null;
          phone: string | null;
          email: string | null;
          address: string | null;
          latitude: number | null;
          longitude: number | null;
          city: string | null;
          country_code: string;
          rating: number;
          total_reviews: number;
          delivery_fee: number;
          min_order_amount: number;
          delivery_time_min: number;
          delivery_time_max: number;
          is_open: boolean;
          is_verified: boolean;
          is_promoted: boolean;
          opening_hours: any | null;
          created_at: string;
          updated_at: string;
        };
      };
      products: {
        Row: {
          id: string;
          merchant_id: string;
          category_id: string | null;
          name: string;
          description: string | null;
          price: number;
          original_price: number | null;
          image_url: string | null;
          images: any;
          is_available: boolean;
          is_featured: boolean;
          preparation_time: number;
          nutritional_info: any | null;
          allergens: string[] | null;
          tags: string[] | null;
          sort_order: number;
          created_at: string;
          updated_at: string;
        };
      };
      promotions: {
        Row: {
          id: string;
          merchant_id: string | null;
          title: string;
          description: string | null;
          image_url: string | null;
          discount_type: 'percentage' | 'fixed_amount' | 'free_delivery';
          discount_value: number;
          min_order_amount: number;
          max_discount_amount: number | null;
          promo_code: string | null;
          usage_limit: number | null;
          usage_count: number;
          is_active: boolean;
          starts_at: string | null;
          expires_at: string | null;
          created_at: string;
          updated_at: string;
        };
      };
      orders: {
        Row: {
          id: string;
          customer_id: string | null;
          merchant_id: string | null;
          deliverer_id: string | null;
          order_number: string;
          status: string;
          subtotal: number;
          delivery_fee: number;
          service_fee: number;
          tax_amount: number;
          discount_amount: number;
          total_amount: number;
          currency: string;
          payment_method: string | null;
          payment_status: string;
          delivery_address: any;
          delivery_instructions: string | null;
          estimated_delivery_time: string | null;
          actual_delivery_time: string | null;
          notes: string | null;
          created_at: string;
          updated_at: string;
        };
      };
    };
  };
}
