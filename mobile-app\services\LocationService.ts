// =====================================================
// SERVICE DE GÉOLOCALISATION NATIF EXPO
// =====================================================

import * as Location from 'expo-location';
import { Platform } from 'react-native';

export interface LocationCoords {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
}

export interface LocationAddress {
  street?: string;
  city?: string;
  region?: string;
  country?: string;
  postalCode?: string;
  name?: string;
}

// Service de géolocalisation
export class LocationService {
  private static instance: LocationService;
  private hasPermission = false;
  private watchId: Location.LocationSubscription | null = null;

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  // Demander les permissions de localisation
  async requestPermissions(): Promise<boolean> {
    try {
      console.log('📍 Demande de permissions de localisation...');

      // Vérifier les permissions actuelles
      const { status: existingStatus } = await Location.getForegroundPermissionsAsync();
      
      if (existingStatus === 'granted') {
        this.hasPermission = true;
        console.log('✅ Permissions déjà accordées');
        return true;
      }

      // Demander les permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status === 'granted') {
        this.hasPermission = true;
        console.log('✅ Permissions accordées');
        return true;
      } else {
        console.log('❌ Permissions refusées');
        return false;
      }
    } catch (error) {
      console.error('❌ Erreur permissions localisation:', error);
      return false;
    }
  }

  // Obtenir la position actuelle
  async getCurrentPosition(): Promise<LocationCoords | null> {
    try {
      if (!this.hasPermission) {
        const hasPermission = await this.requestPermissions();
        if (!hasPermission) {
          return null;
        }
      }

      console.log('📍 Obtention de la position actuelle...');

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        maximumAge: 10000, // 10 secondes
        timeout: 15000, // 15 secondes
      });

      const coords: LocationCoords = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || undefined,
        altitude: location.coords.altitude || undefined,
        heading: location.coords.heading || undefined,
        speed: location.coords.speed || undefined,
      };

      console.log('✅ Position obtenue:', coords);
      return coords;
    } catch (error) {
      console.error('❌ Erreur obtention position:', error);
      return null;
    }
  }

  // Surveiller la position en temps réel
  async watchPosition(
    callback: (coords: LocationCoords) => void,
    errorCallback?: (error: string) => void
  ): Promise<boolean> {
    try {
      if (!this.hasPermission) {
        const hasPermission = await this.requestPermissions();
        if (!hasPermission) {
          errorCallback?.('Permissions de localisation refusées');
          return false;
        }
      }

      console.log('📍 Démarrage de la surveillance de position...');

      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 5000, // 5 secondes
          distanceInterval: 10, // 10 mètres
        },
        (location) => {
          const coords: LocationCoords = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy || undefined,
            altitude: location.coords.altitude || undefined,
            heading: location.coords.heading || undefined,
            speed: location.coords.speed || undefined,
          };
          callback(coords);
        }
      );

      console.log('✅ Surveillance de position démarrée');
      return true;
    } catch (error) {
      console.error('❌ Erreur surveillance position:', error);
      errorCallback?.('Erreur lors de la surveillance de position');
      return false;
    }
  }

  // Arrêter la surveillance de position
  stopWatchingPosition(): void {
    if (this.watchId) {
      this.watchId.remove();
      this.watchId = null;
      console.log('✅ Surveillance de position arrêtée');
    }
  }

  // Géocodage inverse (coordonnées vers adresse)
  async reverseGeocode(coords: LocationCoords): Promise<LocationAddress | null> {
    try {
      console.log('📍 Géocodage inverse:', coords);

      const addresses = await Location.reverseGeocodeAsync({
        latitude: coords.latitude,
        longitude: coords.longitude,
      });

      if (addresses.length > 0) {
        const address = addresses[0];
        const result: LocationAddress = {
          street: address.street || undefined,
          city: address.city || undefined,
          region: address.region || undefined,
          country: address.country || undefined,
          postalCode: address.postalCode || undefined,
          name: address.name || undefined,
        };

        console.log('✅ Adresse trouvée:', result);
        return result;
      }

      return null;
    } catch (error) {
      console.error('❌ Erreur géocodage inverse:', error);
      return null;
    }
  }

  // Géocodage (adresse vers coordonnées)
  async geocode(address: string): Promise<LocationCoords | null> {
    try {
      console.log('📍 Géocodage:', address);

      const locations = await Location.geocodeAsync(address);

      if (locations.length > 0) {
        const location = locations[0];
        const coords: LocationCoords = {
          latitude: location.latitude,
          longitude: location.longitude,
        };

        console.log('✅ Coordonnées trouvées:', coords);
        return coords;
      }

      return null;
    } catch (error) {
      console.error('❌ Erreur géocodage:', error);
      return null;
    }
  }

  // Calculer la distance entre deux points (en mètres)
  calculateDistance(coords1: LocationCoords, coords2: LocationCoords): number {
    const R = 6371e3; // Rayon de la Terre en mètres
    const φ1 = (coords1.latitude * Math.PI) / 180;
    const φ2 = (coords2.latitude * Math.PI) / 180;
    const Δφ = ((coords2.latitude - coords1.latitude) * Math.PI) / 180;
    const Δλ = ((coords2.longitude - coords1.longitude) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance en mètres
  }

  // Vérifier si une position est dans un rayon donné
  isWithinRadius(
    center: LocationCoords,
    point: LocationCoords,
    radiusInMeters: number
  ): boolean {
    const distance = this.calculateDistance(center, point);
    return distance <= radiusInMeters;
  }

  // Obtenir des coordonnées par défaut pour les tests (Dakar, Sénégal)
  getDefaultLocation(): LocationCoords {
    return {
      latitude: 14.6928,
      longitude: -17.4467,
      accuracy: 100,
    };
  }

  // Vérifier si les services de localisation sont activés
  async isLocationEnabled(): Promise<boolean> {
    try {
      const enabled = await Location.hasServicesEnabledAsync();
      console.log('📍 Services de localisation activés:', enabled);
      return enabled;
    } catch (error) {
      console.error('❌ Erreur vérification services localisation:', error);
      return false;
    }
  }

  // Nettoyer les ressources
  cleanup(): void {
    this.stopWatchingPosition();
    this.hasPermission = false;
    console.log('✅ Service de localisation nettoyé');
  }
}

// Instance singleton
export const locationService = LocationService.getInstance();
export default locationService;
