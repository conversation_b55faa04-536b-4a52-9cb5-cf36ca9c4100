import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { crypto } from "https://deno.land/std@0.168.0/crypto/mod.ts"

interface WebhookPayload {
  provider: string
  event_type: string
  transaction_id: string
  reference: string
  amount: number
  currency: string
  status: string
  customer_phone?: string
  customer_email?: string
  metadata?: Record<string, any>
  timestamp: string
  signature?: string
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-signature',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '', // Use service role for webhooks
    )

    const provider = req.url.split('/').pop() || 'unknown'
    const signature = req.headers.get('x-signature') || req.headers.get('x-webhook-signature')
    const rawBody = await req.text()
    
    // Verify webhook signature
    if (!await verifyWebhookSignature(provider, rawBody, signature)) {
      console.error('Invalid webhook signature')
      return new Response('Unauthorized', { status: 401 })
    }

    let webhookData: WebhookPayload
    
    // Parse webhook based on provider
    switch (provider) {
      case 'mpesa':
        webhookData = parseMpesaWebhook(JSON.parse(rawBody))
        break
      case 'orange-money':
        webhookData = parseOrangeMoneyWebhook(JSON.parse(rawBody))
        break
      case 'wave':
        webhookData = parseWaveWebhook(JSON.parse(rawBody))
        break
      case 'paystack':
        webhookData = parsePaystackWebhook(JSON.parse(rawBody))
        break
      case 'flutterwave':
        webhookData = parseFlutterwaveWebhook(JSON.parse(rawBody))
        break
      default:
        throw new Error(`Unsupported payment provider: ${provider}`)
    }

    console.log('Processing webhook:', { provider, event_type: webhookData.event_type, reference: webhookData.reference })

    // Find the payment record
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select(`
        *,
        orders (
          id,
          customer_id,
          merchant_id,
          total_amount,
          status
        )
      `)
      .or(`external_transaction_id.eq.${webhookData.transaction_id},merchant_reference.eq.${webhookData.reference}`)
      .single()

    if (paymentError || !payment) {
      console.error('Payment not found:', { transaction_id: webhookData.transaction_id, reference: webhookData.reference })
      
      // Log unknown webhook for investigation
      await supabase
        .from('webhook_logs')
        .insert({
          provider,
          event_type: webhookData.event_type,
          transaction_id: webhookData.transaction_id,
          reference: webhookData.reference,
          status: 'payment_not_found',
          raw_data: JSON.parse(rawBody),
          processed_at: new Date().toISOString()
        })

      return new Response('Payment not found', { status: 404 })
    }

    // Log the webhook
    await supabase
      .from('payment_logs')
      .insert({
        payment_id: payment.id,
        event_type: 'webhook_received',
        old_status: payment.status,
        new_status: mapProviderStatusToInternal(webhookData.status),
        event_data: JSON.parse(rawBody),
        source: 'webhook',
        created_at: new Date().toISOString()
      })

    // Process the webhook based on event type
    await processWebhookEvent(supabase, payment, webhookData)

    return new Response(
      JSON.stringify({ success: true, message: 'Webhook processed successfully' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Webhook processing error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error',
        code: 'WEBHOOK_ERROR'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function verifyWebhookSignature(provider: string, body: string, signature: string | null): Promise<boolean> {
  if (!signature) return false

  const secrets = {
    'mpesa': Deno.env.get('MPESA_WEBHOOK_SECRET'),
    'orange-money': Deno.env.get('ORANGE_MONEY_WEBHOOK_SECRET'),
    'wave': Deno.env.get('WAVE_WEBHOOK_SECRET'),
    'paystack': Deno.env.get('PAYSTACK_WEBHOOK_SECRET'),
    'flutterwave': Deno.env.get('FLUTTERWAVE_WEBHOOK_SECRET')
  }

  const secret = secrets[provider]
  if (!secret) return false

  try {
    const encoder = new TextEncoder()
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(secret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    )

    const signatureBuffer = await crypto.subtle.sign('HMAC', key, encoder.encode(body))
    const expectedSignature = Array.from(new Uint8Array(signatureBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')

    // Remove any prefix from signature (like "sha256=")
    const cleanSignature = signature.replace(/^sha256=/, '')
    
    return expectedSignature === cleanSignature
  } catch (error) {
    console.error('Signature verification error:', error)
    return false
  }
}

function parseMpesaWebhook(data: any): WebhookPayload {
  const body = data.Body?.stkCallback || data.Body?.CallbackMetadata
  
  return {
    provider: 'mpesa',
    event_type: body?.ResultCode === 0 ? 'payment.completed' : 'payment.failed',
    transaction_id: body?.CheckoutRequestID || data.TransactionID,
    reference: body?.MerchantRequestID || data.BillRefNumber,
    amount: body?.CallbackMetadata?.Item?.find((item: any) => item.Name === 'Amount')?.Value || 0,
    currency: 'KES',
    status: body?.ResultCode === 0 ? 'completed' : 'failed',
    customer_phone: body?.CallbackMetadata?.Item?.find((item: any) => item.Name === 'PhoneNumber')?.Value,
    metadata: data,
    timestamp: new Date().toISOString()
  }
}

function parseOrangeMoneyWebhook(data: any): WebhookPayload {
  return {
    provider: 'orange-money',
    event_type: data.status === 'SUCCESS' ? 'payment.completed' : 'payment.failed',
    transaction_id: data.txnid || data.transaction_id,
    reference: data.reference || data.merchant_reference,
    amount: parseFloat(data.amount) * 100, // Convert to cents
    currency: data.currency || 'XOF',
    status: data.status === 'SUCCESS' ? 'completed' : 'failed',
    customer_phone: data.msisdn || data.phone,
    metadata: data,
    timestamp: data.timestamp || new Date().toISOString()
  }
}

function parseWaveWebhook(data: any): WebhookPayload {
  return {
    provider: 'wave',
    event_type: data.type || 'payment.completed',
    transaction_id: data.id,
    reference: data.client_reference,
    amount: data.amount, // Wave already in cents
    currency: data.currency || 'XOF',
    status: data.status === 'completed' ? 'completed' : 'failed',
    customer_phone: data.sender_msisdn,
    metadata: data,
    timestamp: data.when || new Date().toISOString()
  }
}

function parsePaystackWebhook(data: any): WebhookPayload {
  const eventData = data.data
  
  return {
    provider: 'paystack',
    event_type: data.event,
    transaction_id: eventData.id,
    reference: eventData.reference,
    amount: eventData.amount, // Paystack in kobo (cents)
    currency: eventData.currency || 'NGN',
    status: eventData.status === 'success' ? 'completed' : 'failed',
    customer_email: eventData.customer?.email,
    metadata: data,
    timestamp: eventData.created_at || new Date().toISOString()
  }
}

function parseFlutterwaveWebhook(data: any): WebhookPayload {
  const eventData = data.data
  
  return {
    provider: 'flutterwave',
    event_type: data.event,
    transaction_id: eventData.id,
    reference: eventData.tx_ref,
    amount: eventData.amount * 100, // Convert to cents
    currency: eventData.currency,
    status: eventData.status === 'successful' ? 'completed' : 'failed',
    customer_email: eventData.customer?.email,
    customer_phone: eventData.customer?.phone_number,
    metadata: data,
    timestamp: eventData.created_at || new Date().toISOString()
  }
}

function mapProviderStatusToInternal(providerStatus: string): string {
  const statusMap = {
    'completed': 'completed',
    'success': 'completed',
    'successful': 'completed',
    'SUCCESS': 'completed',
    'failed': 'failed',
    'FAILED': 'failed',
    'error': 'failed',
    'cancelled': 'cancelled',
    'pending': 'processing'
  }
  
  return statusMap[providerStatus] || 'failed'
}

async function processWebhookEvent(supabase: any, payment: any, webhookData: WebhookPayload) {
  const newStatus = mapProviderStatusToInternal(webhookData.status)
  
  // Update payment record
  const { error: updateError } = await supabase
    .from('payments')
    .update({
      status: newStatus,
      external_transaction_id: webhookData.transaction_id,
      webhook_received_at: new Date().toISOString(),
      webhook_data: webhookData.metadata,
      completed_at: newStatus === 'completed' ? new Date().toISOString() : null,
      failed_at: newStatus === 'failed' ? new Date().toISOString() : null,
      updated_at: new Date().toISOString()
    })
    .eq('id', payment.id)

  if (updateError) {
    throw new Error('Failed to update payment status')
  }

  // Update mobile money payment details if applicable
  if (webhookData.provider !== 'paystack' && webhookData.provider !== 'flutterwave') {
    await supabase
      .from('mobile_money_payments')
      .update({
        confirmation_code: webhookData.metadata?.confirmation_code || webhookData.transaction_id,
        receipt_number: webhookData.metadata?.receipt_number,
        provider_data: webhookData.metadata,
        updated_at: new Date().toISOString()
      })
      .eq('payment_id', payment.id)
  }

  // Process based on payment status
  if (newStatus === 'completed') {
    await handleSuccessfulPayment(supabase, payment, webhookData)
  } else if (newStatus === 'failed') {
    await handleFailedPayment(supabase, payment, webhookData)
  }
}

async function handleSuccessfulPayment(supabase: any, payment: any, webhookData: WebhookPayload) {
  const order = payment.orders
  
  // Update order status if payment was pending
  if (order.status === 'pending') {
    await supabase
      .from('orders')
      .update({
        status: 'confirmed',
        confirmed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', order.id)

    // Trigger order confirmation workflow
    await supabase.functions.invoke('rider-matching', {
      body: {
        order_id: order.id,
        pickup_location: { latitude: 0, longitude: 0 }, // Get from order
        delivery_location: { latitude: 0, longitude: 0 }, // Get from order
        delivery_type: 'food', // Get from order
        estimated_value: order.total_amount,
        urgency_level: 'medium'
      }
    })
  }

  // Send success notifications
  await Promise.all([
    // Notify customer
    supabase.functions.invoke('send-notification', {
      body: {
        user_id: order.customer_id,
        type: 'payment_successful',
        title: 'Paiement confirmé',
        body: `Votre paiement de ${webhookData.amount / 100} ${webhookData.currency} a été confirmé`,
        data: {
          payment_id: payment.id,
          order_id: order.id,
          amount: webhookData.amount,
          currency: webhookData.currency
        },
        channels: ['push', 'sms']
      }
    }),

    // Notify merchant
    supabase.functions.invoke('send-notification', {
      body: {
        user_id: order.merchant_id,
        type: 'order_confirmed',
        title: 'Nouvelle commande payée',
        body: `Commande ${order.id} confirmée et payée`,
        data: {
          order_id: order.id,
          amount: webhookData.amount,
          currency: webhookData.currency
        },
        channels: ['push']
      }
    })
  ])

  // Update merchant earnings
  const merchantFee = Math.floor(webhookData.amount * 0.03) // 3% commission
  const merchantEarnings = webhookData.amount - merchantFee

  await supabase
    .from('merchant_earnings')
    .insert({
      merchant_id: order.merchant_id,
      order_id: order.id,
      payment_id: payment.id,
      gross_amount: webhookData.amount,
      commission_amount: merchantFee,
      net_amount: merchantEarnings,
      currency: webhookData.currency,
      status: 'pending',
      created_at: new Date().toISOString()
    })
}

async function handleFailedPayment(supabase: any, payment: any, webhookData: WebhookPayload) {
  const order = payment.orders

  // Send failure notification to customer
  await supabase.functions.invoke('send-notification', {
    body: {
      user_id: order.customer_id,
      type: 'payment_failed',
      title: 'Échec du paiement',
      body: 'Votre paiement a échoué. Veuillez réessayer.',
      data: {
        payment_id: payment.id,
        order_id: order.id,
        error_reason: webhookData.metadata?.error_message || 'Payment failed'
      },
      channels: ['push', 'sms']
    }
  })

  // If order is still pending, keep it pending for retry
  // Don't automatically cancel to allow payment retry
}
