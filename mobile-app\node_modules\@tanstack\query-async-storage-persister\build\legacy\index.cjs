"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  createAsyncStoragePersister: () => createAsyncStoragePersister
});
module.exports = __toCommonJS(index_exports);
var import_asyncThrottle = require("./asyncThrottle.cjs");
var import_utils = require("./utils.cjs");
var createAsyncStoragePersister = ({
  storage,
  key = `REACT_QUERY_OFFLINE_CACHE`,
  throttleTime = 1e3,
  serialize = JSON.stringify,
  deserialize = JSON.parse,
  retry
}) => {
  if (storage) {
    const trySave = async (persistedClient) => {
      try {
        const serialized = await serialize(persistedClient);
        await storage.setItem(key, serialized);
        return;
      } catch (error) {
        return error;
      }
    };
    return {
      persistClient: (0, import_asyncThrottle.asyncThrottle)(
        async (persistedClient) => {
          let client = persistedClient;
          let error = await trySave(client);
          let errorCount = 0;
          while (error && client) {
            errorCount++;
            client = await (retry == null ? void 0 : retry({
              persistedClient: client,
              error,
              errorCount
            }));
            if (client) {
              error = await trySave(client);
            }
          }
        },
        { interval: throttleTime }
      ),
      restoreClient: async () => {
        const cacheString = await storage.getItem(key);
        if (!cacheString) {
          return;
        }
        return await deserialize(cacheString);
      },
      removeClient: () => storage.removeItem(key)
    };
  }
  return {
    persistClient: import_utils.noop,
    restoreClient: () => Promise.resolve(void 0),
    removeClient: import_utils.noop
  };
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  createAsyncStoragePersister
});
//# sourceMappingURL=index.cjs.map