// =====================================================
// TABLEAU DE BORD LIVREUR - US-L003
// =====================================================

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '../../../store/authStore';
import Button from '../../../components/ui/Button';

interface DeliveryStats {
  todayDeliveries: number;
  todayEarnings: number;
  weeklyDeliveries: number;
  weeklyEarnings: number;
  rating: number;
  completionRate: number;
}

interface WorkingZone {
  id: string;
  name: string;
  isActive: boolean;
  demandLevel: 'low' | 'medium' | 'high';
  estimatedEarnings: number;
}

const mockStats: DeliveryStats = {
  todayDeliveries: 8,
  todayEarnings: 12500,
  weeklyDeliveries: 45,
  weeklyEarnings: 67500,
  rating: 4.8,
  completionRate: 96,
};

const workingZones: WorkingZone[] = [
  {
    id: '1',
    name: 'Plateau - Centre-ville',
    isActive: true,
    demandLevel: 'high',
    estimatedEarnings: 2500,
  },
  {
    id: '2',
    name: 'Almadies - Résidentiel',
    isActive: false,
    demandLevel: 'medium',
    estimatedEarnings: 1800,
  },
  {
    id: '3',
    name: 'Parcelles Assainies',
    isActive: false,
    demandLevel: 'low',
    estimatedEarnings: 1200,
  },
];

export default function DelivererDashboard() {
  const { user } = useAuthStore();
  const [isOnline, setIsOnline] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState<DeliveryStats>(mockStats);
  const [zones, setZones] = useState<WorkingZone[]>(workingZones);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simuler le rechargement des données
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleToggleOnline = () => {
    if (!isOnline) {
      Alert.alert(
        'Passer en ligne',
        'Vous allez commencer à recevoir des demandes de livraison.',
        [
          { text: 'Annuler', style: 'cancel' },
          { 
            text: 'Confirmer', 
            onPress: () => setIsOnline(true),
            style: 'default'
          },
        ]
      );
    } else {
      setIsOnline(false);
    }
  };

  const handleZoneToggle = (zoneId: string) => {
    setZones(zones.map(zone => 
      zone.id === zoneId 
        ? { ...zone, isActive: !zone.isActive }
        : zone
    ));
  };

  const getDemandColor = (level: string) => {
    switch (level) {
      case 'high': return '#FF3B30';
      case 'medium': return '#FF9500';
      case 'low': return '#34C759';
      default: return '#8E8E93';
    }
  };

  const getDemandText = (level: string) => {
    switch (level) {
      case 'high': return 'Forte demande';
      case 'medium': return 'Demande modérée';
      case 'low': return 'Faible demande';
      default: return 'Inconnue';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.greeting}>Bonjour,</Text>
          <Text style={styles.userName}>{user?.profile?.firstName || 'Livreur'} 🚚</Text>
        </View>
        <View style={styles.headerRight}>
          <View style={styles.onlineToggle}>
            <Text style={[styles.statusText, { color: isOnline ? '#34C759' : '#8E8E93' }]}>
              {isOnline ? 'En ligne' : 'Hors ligne'}
            </Text>
            <Switch
              value={isOnline}
              onValueChange={handleToggleOnline}
              trackColor={{ false: '#E5E5EA', true: '#34C759' }}
              thumbColor={isOnline ? '#FFFFFF' : '#FFFFFF'}
            />
          </View>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Statut actuel */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Ionicons 
              name={isOnline ? 'checkmark-circle' : 'pause-circle'} 
              size={24} 
              color={isOnline ? '#34C759' : '#FF9500'} 
            />
            <Text style={styles.statusTitle}>
              {isOnline ? 'Prêt à livrer' : 'En pause'}
            </Text>
          </View>
          <Text style={styles.statusDescription}>
            {isOnline 
              ? 'Vous recevez des demandes de livraison'
              : 'Activez-vous pour recevoir des commandes'
            }
          </Text>
          {!isOnline && (
            <Button
              title="Passer en ligne"
              onPress={handleToggleOnline}
              variant="primary"
              size="medium"
              style={styles.goOnlineButton}
            />
          )}
        </View>

        {/* Statistiques du jour */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📊 Aujourd'hui</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{stats.todayDeliveries}</Text>
              <Text style={styles.statLabel}>Livraisons</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{stats.todayEarnings.toLocaleString()} FCFA</Text>
              <Text style={styles.statLabel}>Revenus</Text>
            </View>
          </View>
        </View>

        {/* Statistiques de la semaine */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📈 Cette semaine</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{stats.weeklyDeliveries}</Text>
              <Text style={styles.statLabel}>Livraisons</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>{stats.weeklyEarnings.toLocaleString()} FCFA</Text>
              <Text style={styles.statLabel}>Revenus</Text>
            </View>
          </View>
        </View>

        {/* Performance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>⭐ Performance</Text>
          <View style={styles.performanceCard}>
            <View style={styles.performanceItem}>
              <View style={styles.performanceHeader}>
                <Ionicons name="star" size={20} color="#FFD700" />
                <Text style={styles.performanceLabel}>Note moyenne</Text>
              </View>
              <Text style={styles.performanceValue}>{stats.rating}/5</Text>
            </View>
            <View style={styles.performanceDivider} />
            <View style={styles.performanceItem}>
              <View style={styles.performanceHeader}>
                <Ionicons name="checkmark-circle" size={20} color="#34C759" />
                <Text style={styles.performanceLabel}>Taux de réussite</Text>
              </View>
              <Text style={styles.performanceValue}>{stats.completionRate}%</Text>
            </View>
          </View>
        </View>

        {/* Zones de travail */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🗺️ Zones de travail</Text>
          {zones.map((zone) => (
            <View key={zone.id} style={styles.zoneCard}>
              <View style={styles.zoneHeader}>
                <View style={styles.zoneInfo}>
                  <Text style={styles.zoneName}>{zone.name}</Text>
                  <View style={styles.zoneMeta}>
                    <View style={[styles.demandBadge, { backgroundColor: getDemandColor(zone.demandLevel) }]}>
                      <Text style={styles.demandText}>{getDemandText(zone.demandLevel)}</Text>
                    </View>
                    <Text style={styles.zoneEarnings}>~{zone.estimatedEarnings} FCFA/h</Text>
                  </View>
                </View>
                <Switch
                  value={zone.isActive}
                  onValueChange={() => handleZoneToggle(zone.id)}
                  trackColor={{ false: '#E5E5EA', true: '#34C759' }}
                  thumbColor={zone.isActive ? '#FFFFFF' : '#FFFFFF'}
                />
              </View>
            </View>
          ))}
        </View>

        {/* Actions rapides */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>⚡ Actions rapides</Text>
          <View style={styles.quickActions}>
            <Button
              title="Voir commandes"
              onPress={() => router.push('/(deliverer)/(tabs)/orders')}
              variant="outline"
              size="medium"
              icon="list"
              style={styles.quickActionButton}
            />
            <Button
              title="Mes revenus"
              onPress={() => router.push('/(deliverer)/(tabs)/earnings')}
              variant="outline"
              size="medium"
              icon="wallet"
              style={styles.quickActionButton}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    fontSize: 14,
    color: '#8E8E93',
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1C1E',
  },
  headerRight: {
    alignItems: 'flex-end',
  },
  onlineToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  statusCard: {
    backgroundColor: '#FFFFFF',
    margin: 24,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1E',
  },
  statusDescription: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 16,
  },
  goOnlineButton: {
    alignSelf: 'flex-start',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1C1C1E',
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#34C759',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
  },
  performanceCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 24,
    padding: 20,
    borderRadius: 16,
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  performanceItem: {
    flex: 1,
    alignItems: 'center',
  },
  performanceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  performanceLabel: {
    fontSize: 14,
    color: '#8E8E93',
  },
  performanceValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1C1E',
  },
  performanceDivider: {
    width: 1,
    backgroundColor: '#E5E5EA',
    marginHorizontal: 20,
  },
  zoneCard: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 24,
    marginBottom: 12,
    padding: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  zoneHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  zoneInfo: {
    flex: 1,
  },
  zoneName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  zoneMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  demandBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  demandText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  zoneEarnings: {
    fontSize: 12,
    color: '#34C759',
    fontWeight: '500',
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
  },
});
