-- =====================================================
-- SCHÉMA NOTIFICATIONS ET AUDIT
-- =====================================================

-- Types de notifications
CREATE TYPE notification_type AS ENUM (
    'order_confirmed',      -- Commande confirmée
    'order_preparing',      -- Commande en préparation
    'order_ready',          -- Commande prête
    'order_picked_up',      -- Commande récupérée
    'order_delivered',      -- Commande livrée
    'order_cancelled',      -- Commande annulée
    'payment_successful',   -- Paiement réussi
    'payment_failed',       -- Paiement écho<PERSON>
    'rider_assigned',       -- Livreur assigné
    'rider_nearby',         -- Livreur proche
    'promotion_available',  -- Promotion disponible
    'rating_request',       -- Demande d'évaluation
    'system_maintenance',   -- Maintenance système
    'account_verified',     -- Compte v<PERSON>ri<PERSON>
    'low_balance',          -- Solde faible
    'new_message'           -- Nouveau message
);

-- Canaux de notification
CREATE TYPE notification_channel AS ENUM (
    'push',                 -- Notification push
    'sms',                  -- SMS
    'email',                -- Email
    'in_app',               -- Dans l'application
    'whatsapp'              -- WhatsApp (futur)
);

-- Statuts de notification
CREATE TYPE notification_status AS ENUM (
    'pending',              -- En attente d'envoi
    'sent',                 -- Envoyée
    'delivered',            -- Délivrée
    'read',                 -- Lue
    'failed',               -- Échec d'envoi
    'expired'               -- Expirée
);

-- Table des templates de notifications
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Identification
    template_key VARCHAR(100) UNIQUE NOT NULL, -- Ex: 'order_confirmed_customer'
    notification_type notification_type NOT NULL,
    target_role user_role NOT NULL, -- À qui s'adresse ce template
    
    -- Contenu par langue
    title_translations JSONB NOT NULL DEFAULT '{}', -- {"fr": "Commande confirmée", "en": "Order confirmed"}
    body_translations JSONB NOT NULL DEFAULT '{}',
    
    -- Configuration par canal
    channels notification_channel[] NOT NULL DEFAULT ARRAY['push'],
    
    -- Timing
    delay_minutes INTEGER DEFAULT 0, -- Délai avant envoi
    expires_after_hours INTEGER DEFAULT 24, -- Expiration
    
    -- Conditions d'envoi
    conditions JSONB DEFAULT '{}', -- Conditions pour envoyer la notification
    
    -- Métadonnées
    is_active BOOLEAN NOT NULL DEFAULT true,
    priority INTEGER NOT NULL DEFAULT 5, -- 1 = très haute, 10 = très basse
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Destinataire
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Type et template
    notification_type notification_type NOT NULL,
    template_id UUID REFERENCES notification_templates(id),
    
    -- Contenu
    title VARCHAR(200) NOT NULL,
    body TEXT NOT NULL,
    
    -- Données contextuelles
    data JSONB DEFAULT '{}', -- Données pour deep linking, etc.
    
    -- Référence
    reference_type VARCHAR(50), -- 'order', 'payment', 'promotion'
    reference_id UUID,
    
    -- Canaux et statut
    channels notification_channel[] NOT NULL,
    status notification_status NOT NULL DEFAULT 'pending',
    
    -- Timing
    scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Métadonnées d'envoi
    send_attempts INTEGER NOT NULL DEFAULT 0,
    last_error TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des logs de notification par canal
CREATE TABLE notification_delivery_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
    
    -- Canal utilisé
    channel notification_channel NOT NULL,
    
    -- Statut de livraison
    status notification_status NOT NULL,
    
    -- Détails de l'envoi
    provider VARCHAR(50), -- 'firebase', 'twilio', 'sendgrid', etc.
    external_id VARCHAR(100), -- ID chez le provider
    
    -- Métadonnées
    metadata JSONB DEFAULT '{}',
    
    -- Messages d'erreur
    error_code VARCHAR(50),
    error_message TEXT,
    
    -- Timing
    attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivered_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des préférences de notification
CREATE TABLE notification_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Préférences par type de notification
    notification_type notification_type NOT NULL,
    
    -- Canaux autorisés
    enabled_channels notification_channel[] NOT NULL DEFAULT ARRAY['push'],
    
    -- Timing
    quiet_hours_start TIME, -- Ex: 22:00
    quiet_hours_end TIME,   -- Ex: 07:00
    timezone VARCHAR(50),   -- Fuseau horaire utilisateur
    
    -- Fréquence
    max_per_day INTEGER DEFAULT 10,
    
    -- Statut
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Une préférence par utilisateur et type
    UNIQUE(user_id, notification_type)
);

-- Table des logs d'audit
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Qui a fait l'action
    user_id UUID REFERENCES profiles(id),
    user_role user_role,
    
    -- Action effectuée
    action VARCHAR(100) NOT NULL, -- 'create', 'update', 'delete', 'login', etc.
    resource_type VARCHAR(50) NOT NULL, -- 'order', 'product', 'user', etc.
    resource_id UUID,
    
    -- Détails de l'action
    old_values JSONB,
    new_values JSONB,
    changes JSONB, -- Diff des changements
    
    -- Contexte
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    
    -- Métadonnées
    metadata JSONB DEFAULT '{}',
    
    -- Géolocalisation (optionnelle)
    location GEOMETRY(POINT, 4326),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des sessions utilisateur
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Informations de session
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE,
    
    -- Device et contexte
    device_id VARCHAR(100),
    device_type VARCHAR(50), -- 'mobile', 'web', 'tablet'
    device_name VARCHAR(100),
    os_name VARCHAR(50),
    os_version VARCHAR(50),
    app_version VARCHAR(20),
    
    -- Réseau
    ip_address INET,
    user_agent TEXT,
    
    -- Géolocalisation
    location GEOMETRY(POINT, 4326),
    city_id UUID REFERENCES cities(id),
    
    -- Timing
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Statut
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des erreurs système
CREATE TABLE system_errors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Classification
    error_type VARCHAR(50) NOT NULL, -- 'api_error', 'payment_error', 'notification_error'
    severity VARCHAR(20) NOT NULL, -- 'low', 'medium', 'high', 'critical'
    
    -- Détails de l'erreur
    error_code VARCHAR(50),
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    
    -- Contexte
    user_id UUID REFERENCES profiles(id),
    session_id VARCHAR(100),
    request_id VARCHAR(100),
    
    -- Données de la requête
    endpoint VARCHAR(200),
    method VARCHAR(10),
    request_data JSONB,
    response_data JSONB,
    
    -- Environnement
    environment VARCHAR(20) DEFAULT 'production',
    server_name VARCHAR(100),
    
    -- Résolution
    is_resolved BOOLEAN NOT NULL DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour optimiser les performances
CREATE INDEX idx_notification_templates_type ON notification_templates(notification_type);
CREATE INDEX idx_notification_templates_role ON notification_templates(target_role);
CREATE INDEX idx_notification_templates_active ON notification_templates(is_active);

CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(notification_type);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_scheduled ON notifications(scheduled_for);
CREATE INDEX idx_notifications_reference ON notifications(reference_type, reference_id);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

CREATE INDEX idx_notification_delivery_logs_notification ON notification_delivery_logs(notification_id);
CREATE INDEX idx_notification_delivery_logs_channel ON notification_delivery_logs(channel);
CREATE INDEX idx_notification_delivery_logs_status ON notification_delivery_logs(status);

CREATE INDEX idx_notification_preferences_user ON notification_preferences(user_id);
CREATE INDEX idx_notification_preferences_type ON notification_preferences(notification_type);

CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_ip ON audit_logs(ip_address);

CREATE INDEX idx_user_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX idx_user_sessions_device ON user_sessions(device_id);

CREATE INDEX idx_system_errors_type ON system_errors(error_type);
CREATE INDEX idx_system_errors_severity ON system_errors(severity);
CREATE INDEX idx_system_errors_resolved ON system_errors(is_resolved);
CREATE INDEX idx_system_errors_created_at ON system_errors(created_at);
CREATE INDEX idx_system_errors_user ON system_errors(user_id);

-- Contraintes de validation
ALTER TABLE notification_templates ADD CONSTRAINT chk_priority_range 
    CHECK (priority >= 1 AND priority <= 10);

ALTER TABLE notification_templates ADD CONSTRAINT chk_delay_non_negative 
    CHECK (delay_minutes >= 0);

ALTER TABLE notifications ADD CONSTRAINT chk_send_attempts_non_negative 
    CHECK (send_attempts >= 0);

ALTER TABLE notification_preferences ADD CONSTRAINT chk_max_per_day_positive 
    CHECK (max_per_day > 0);

ALTER TABLE notification_preferences ADD CONSTRAINT chk_quiet_hours_valid 
    CHECK (
        (quiet_hours_start IS NULL AND quiet_hours_end IS NULL) OR
        (quiet_hours_start IS NOT NULL AND quiet_hours_end IS NOT NULL)
    );

ALTER TABLE user_sessions ADD CONSTRAINT chk_expires_future 
    CHECK (expires_at > created_at);
