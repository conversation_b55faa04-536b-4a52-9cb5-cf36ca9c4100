{"expo": {"name": "Delivery App Africa", "slug": "delivery-app-africa", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "deliveryapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "description": "Fast, reliable delivery service across Africa with mobile money integration and offline support", "keywords": ["delivery", "food", "africa", "mobile money", "logistics", "offline", "multilingual"], "privacy": "https://deliveryapp.africa/privacy", "category": "food-and-drink", "primaryColor": "#FF6B35", "backgroundColor": "#FFFFFF", "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#FF6B35", "imageWidth": 200}, "assetBundlePatterns": ["**/*"], "locales": {"en": "./locales/en.json", "fr": "./locales/fr.json", "sw": "./locales/sw.json", "ha": "./locales/ha.json", "wo": "./locales/wo.json"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.deliveryapp.africa", "buildNumber": "1", "requireFullScreen": false, "userInterfaceStyle": "automatic", "backgroundColor": "#FF6B35", "config": {"usesNonExemptEncryption": false}, "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs location access to show your current position and calculate delivery distances.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs background location access to track deliveries and provide real-time updates.", "NSCameraUsageDescription": "This app needs camera access to take photos for proof of delivery and profile pictures.", "NSMicrophoneUsageDescription": "This app needs microphone access for voice notes and customer communication.", "NSPhotoLibraryUsageDescription": "This app needs photo library access to select images for profile and delivery proof.", "NSContactsUsageDescription": "This app needs contacts access to easily invite friends and family.", "NSCalendarsUsageDescription": "This app needs calendar access to schedule deliveries.", "NSRemindersUsageDescription": "This app needs reminders access to set delivery notifications.", "NSFaceIDUsageDescription": "This app uses Face ID for secure authentication and payment verification.", "UIBackgroundModes": ["location", "background-fetch", "remote-notification"], "LSApplicationQueriesSchemes": ["tel", "mailto", "sms", "whatsapp", "fb", "twitter"], "ITSAppUsesNonExemptEncryption": false}, "associatedDomains": ["applinks:deliveryapp.africa", "applinks:*.deliveryapp.africa"], "entitlements": {"com.apple.developer.associated-domains": ["applinks:deliveryapp.africa"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#FF6B35"}, "package": "com.deliveryapp.africa", "versionCode": 1, "compileSdkVersion": 34, "targetSdkVersion": 34, "minSdkVersion": 23, "allowBackup": true, "edgeToEdgeEnabled": true, "backgroundColor": "#FF6B35", "navigationBarColor": "#FF6B35", "statusBarColor": "#FF6B35", "statusBarStyle": "light-content", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "CAMERA", "RECORD_AUDIO", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "READ_MEDIA_IMAGES", "READ_MEDIA_VIDEO", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "WAKE_LOCK", "INTERNET", "ACCESS_NETWORK_STATE", "ACCESS_WIFI_STATE", "CALL_PHONE", "READ_PHONE_STATE", "SEND_SMS", "READ_CONTACTS", "WRITE_CONTACTS", "USE_FINGERPRINT", "USE_BIOMETRIC", "FOREGROUND_SERVICE", "FOREGROUND_SERVICE_LOCATION", "POST_NOTIFICATIONS"], "intentFilters": [{"action": "VIEW", "data": [{"scheme": "https", "host": "deliveryapp.africa"}], "category": ["BROWSABLE", "DEFAULT"]}], "googleServicesFile": "./google-services.json"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png", "name": "Delivery App Africa", "shortName": "DeliveryApp", "lang": "en", "scope": "/", "themeColor": "#FF6B35", "backgroundColor": "#FFFFFF", "display": "standalone", "orientation": "portrait", "startUrl": "/", "preferRelatedApplications": true, "relatedApplications": [{"platform": "play", "url": "https://play.google.com/store/apps/details?id=com.deliveryapp.africa", "id": "com.deliveryapp.africa"}, {"platform": "itunes", "url": "https://apps.apple.com/app/delivery-app-africa/id123456789"}]}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#FF6B35"}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "This app needs location access to show your current position and track deliveries.", "locationAlwaysPermission": "This app needs background location access to provide real-time delivery tracking.", "locationWhenInUsePermission": "This app needs location access to calculate delivery distances and show nearby restaurants.", "isIosBackgroundLocationEnabled": true, "isAndroidBackgroundLocationEnabled": true, "isAndroidForegroundServiceEnabled": true}], ["expo-camera", {"cameraPermission": "This app needs camera access to take photos for proof of delivery and profile pictures.", "microphonePermission": "This app needs microphone access for voice notes and customer communication.", "recordAudioAndroid": true}], ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#FF6B35", "defaultChannel": "default", "sounds": ["./assets/sounds/notification.wav"], "mode": "production"}], ["expo-image-picker", {"photosPermission": "This app needs photo library access to select images for profile and delivery proof.", "cameraPermission": "This app needs camera access to take photos for proof of delivery."}], ["expo-contacts", {"contactsPermission": "This app needs contacts access to easily invite friends and family to use the delivery service."}], ["expo-local-authentication", {"faceIDPermission": "This app uses Face ID for secure authentication and payment verification."}], ["expo-linking", {"scheme": "deliveryapp"}], ["expo-font", {"fonts": ["./assets/fonts/Inter-Regular.ttf", "./assets/fonts/Inter-Bold.ttf", "./assets/fonts/Inter-SemiBold.ttf"]}], "expo-secure-store", "expo-sqlite", "expo-network", "expo-device", "expo-application", "expo-constants", "expo-file-system"], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "extra": {"eas": {"projectId": "your-eas-project-id"}, "supabaseUrl": "https://your-project.supabase.co", "supabaseAnonKey": "your-anon-key", "googleMapsApiKey": "your-google-maps-key", "oneSignalAppId": "your-onesignal-app-id", "sentryDsn": "your-sentry-dsn", "environment": "production", "apiBaseUrl": "https://api.deliveryapp.africa", "supportedCountries": ["SEN", "KEN", "GHA", "NGA", "CIV", "MLI", "BFA", "UGA", "TZA", "RWA"], "defaultCountry": "SEN", "defaultCurrency": "XOF", "defaultLanguage": "fr", "offlineSupport": true, "lowBandwidthMode": true, "compressionEnabled": true, "cacheSize": "50MB", "syncInterval": 300000, "maxRetries": 3, "requestTimeout": 30000}, "hooks": {"postPublish": [{"file": "sentry-expo/upload-sourcemaps", "config": {"organization": "delivery-app-africa", "project": "mobile-app"}}]}}}