import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
// Use native Expo services instead of Supabase
import { authService, User, AuthSession } from '../services/AuthService';
import { databaseService } from '../services/DatabaseService';

interface AuthContextType {
    isAuthenticated: boolean;
    userRole: string | null;
    user: User | null;
    loading: boolean;
    login: (email: string, password: string) => Promise<{ error: string | null }>;
    signup: (email: string, password: string, role?: User['role']) => Promise<{ error: string | null }>;
    logout: () => Promise<{ error: string | null }>;
  }

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Initialize services and check authentication
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('🔐 Initialisation de l\'authentification...');

        // Initialize database
        await databaseService.initialize();

        // Check current session
        const session = await authService.getCurrentSession();
        if (session) {
          setUser(session.user);
          setIsAuthenticated(true);
          setUserRole(session.user.role);
          console.log('✅ Session trouvée:', session.user.email);
        } else {
          setUser(null);
          setIsAuthenticated(false);
          setUserRole(null);
          console.log('ℹ️ Aucune session active');
        }
      } catch (error) {
        console.error('❌ Erreur initialisation auth:', error);
        setUser(null);
        setIsAuthenticated(false);
        setUserRole(null);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth state changes
    const unsubscribe = authService.onAuthStateChange((session: AuthSession | null) => {
      if (session) {
        setUser(session.user);
        setIsAuthenticated(true);
        setUserRole(session.user.role);
      } else {
        setUser(null);
        setIsAuthenticated(false);
        setUserRole(null);
      }
    });

    return unsubscribe;
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      const result = await authService.signIn(email, password);
      if (result.error) {
        return { error: result.error };
      }
      // State will be updated by the auth state change listener
      return { error: null };
    } catch (error) {
      console.error('❌ Erreur login:', error);
      return { error: 'Erreur lors de la connexion' };
    } finally {
      setLoading(false);
    }
  };

  const signup = async (email: string, password: string, role: User['role'] = 'client') => {
    setLoading(true);
    try {
      const result = await authService.signUp(email, password, role);
      if (result.error) {
        return { error: result.error };
      }
      // State will be updated by the auth state change listener
      return { error: null };
    } catch (error) {
      console.error('❌ Erreur signup:', error);
      return { error: 'Erreur lors de l\'inscription' };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    setLoading(true);
    try {
      const result = await authService.signOut();
      if (result.error) {
        return { error: result.error };
      }
      // State will be updated by the auth state change listener
      return { error: null };
    } catch (error) {
      console.error('❌ Erreur logout:', error);
      return { error: 'Erreur lors de la déconnexion' };
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    // Optional: Render a loading screen while checking auth status
    return null; // Or a <LoadingScreen /> component
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, userRole, user, loading, login, signup, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};