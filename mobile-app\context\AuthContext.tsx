import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
// Use the main supabase file which now points to the simplified version
import { supabase } from '../utils/supabase';

// Mock Session type for development
interface Session {
  user: { id: string; email?: string };
}

interface AuthContextType {
    isAuthenticated: boolean;
    userRole: string | null;
    loading: boolean;
    login: (email: string, password: string) => Promise<{ error: Error | null }>;
    signup: (email: string, password: string) => Promise<{ error: Error | null }>;
    logout: () => Promise<{ error: Error | null }>;
  }

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState<Session | null>(null);

  // Check initial session and set up auth state listener
  useEffect(() => {
    // Mock session check for development
    const mockSession = null; // No session initially
    setSession(mockSession);
    setIsAuthenticated(!!mockSession);
    setUserRole(null);
    setLoading(false);

    // Mock auth state change listener
    const subscription = supabase.auth.onAuthStateChange((_event: any, session: any) => {
      setSession(session);
      setIsAuthenticated(!!session);
      if (session) {
        fetchUserRole(session.user.id);
      } else {
        setUserRole(null);
      }
    });

    return () => {
      // Mock unsubscribe
      console.log('Mock unsubscribe');
    };
  }, []);

  // Function to fetch user role
  const fetchUserRole = async (userId: string) => {
    // Mock user role fetch for development
    console.log('Mock fetchUserRole for userId:', userId);

    // Simulate a default role for development
    setUserRole('client'); // Default to client role
  };

  const login = async (email: string, password: string) => {
    setLoading(true);
    console.log('Mock login attempt:', { email, password });

    // Mock successful login for development
    const mockSession = { user: { id: 'mock-user-id' } };
    setSession(mockSession);
    setIsAuthenticated(true);
    await fetchUserRole(mockSession.user.id);

    setLoading(false);
    return { error: null };
  };

  const signup = async (email: string, password: string) => {
    setLoading(true);
    console.log('Mock signup attempt:', { email, password });

    // Mock successful signup for development
    const mockSession = { user: { id: 'mock-user-id' } };
    setSession(mockSession);
    setIsAuthenticated(true);
    await fetchUserRole(mockSession.user.id);

    setLoading(false);
    return { error: null };
  };

  const logout = async () => {
    setLoading(true);
    console.log('Mock logout');

    // Mock logout
    setSession(null);
    setIsAuthenticated(false);
    setUserRole(null);

    setLoading(false);
    return { error: null };
  };

  if (loading) {
    // Optional: Render a loading screen while checking auth status
    return null; // Or a <LoadingScreen /> component
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, userRole, loading, login, signup, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};