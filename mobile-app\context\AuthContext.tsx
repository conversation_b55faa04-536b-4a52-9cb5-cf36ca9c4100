import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../utils/supabase';
import { Session } from '@supabase/supabase-js';

interface AuthContextType {
    isAuthenticated: boolean;
    userRole: string | null;
    loading: boolean;
    login: (email: string, password: string) => Promise<{ error: Error | null }>;
    signup: (email: string, password: string) => Promise<{ error: Error | null }>;
    logout: () => Promise<{ error: Error | null }>;
  }

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState<Session | null>(null);

  // Check initial session and set up auth state listener
  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setIsAuthenticated(!!session);
      if (session) {
        fetchUserRole(session.user.id);
      } else {
        setUserRole(null);
      }
      setLoading(false);
    });

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setIsAuthenticated(!!session);
      if (session) {
        fetchUserRole(session.user.id);
      } else {
        setUserRole(null);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  // Function to fetch user role
  const fetchUserRole = async (userId: string) => {
    const { data, error } = await supabase
      .from('profiles') // Assuming a 'profiles' table with user roles
      .select('role')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching user role:', error.message);
      setUserRole(null); // Set role to null on error
    } else if (data) {
      setUserRole(data.role); // Set the fetched role
    } else {
      setUserRole(null); // Set role to null if no profile found
    }
  };

  const login = async (email: string, password: string) => {
    setLoading(true);
    const { error } = await supabase.auth.signInWithPassword({ email, password });
    if (error) {
      console.error('Login error:', error.message);
      // TODO: Handle login error (e.g., show error message to user)
    }
    setLoading(false);
    return { error };
  };

  const signup = async (email: string, password: string) => {
    setLoading(true);
    const { error } = await supabase.auth.signUp({ email, password });
    if (error) {
      console.error('Signup error:', error.message);
      // TODO: Handle signup error
    } else {
      // User will need to confirm email, session will be set after confirmation
    }
    setLoading(false);
    return { error };
  };

  const logout = async () => {
    setLoading(true);
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Logout error:', error.message);
      // TODO: Handle logout error
    } else {
      // Auth state listener will set isAuthenticated to false and userRole to null
    }
    setLoading(false);
    return { error };
  };

  if (loading) {
    // Optional: Render a loading screen while checking auth status
    return null; // Or a <LoadingScreen /> component
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, userRole, login, signup, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};