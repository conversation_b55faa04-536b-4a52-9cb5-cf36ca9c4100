{"version": 3, "sources": ["../../src/retryStrategies.ts"], "sourcesContent": ["import type { PersistedClient } from './persist'\n\nexport type PersistRetryer = (props: {\n  persistedClient: PersistedClient\n  error: Error\n  errorCount: number\n}) => PersistedClient | undefined\n\nexport const removeOldestQuery: PersistRetryer = ({ persistedClient }) => {\n  const mutations = [...persistedClient.clientState.mutations]\n  const queries = [...persistedClient.clientState.queries]\n  const client: PersistedClient = {\n    ...persistedClient,\n    clientState: { mutations, queries },\n  }\n\n  // sort queries by dataUpdatedAt (oldest first)\n  const sortedQueries = [...queries].sort(\n    (a, b) => a.state.dataUpdatedAt - b.state.dataUpdatedAt,\n  )\n\n  // clean oldest query\n  if (sortedQueries.length > 0) {\n    const oldestData = sortedQueries.shift()\n    client.clientState.queries = queries.filter((q) => q !== oldestData)\n    return client\n  }\n\n  return undefined\n}\n"], "mappings": ";AAQO,IAAM,oBAAoC,CAAC,EAAE,gBAAgB,MAAM;AACxE,QAAM,YAAY,CAAC,GAAG,gBAAgB,YAAY,SAAS;AAC3D,QAAM,UAAU,CAAC,GAAG,gBAAgB,YAAY,OAAO;AACvD,QAAM,SAA0B;AAAA,IAC9B,GAAG;AAAA,IACH,aAAa,EAAE,WAAW,QAAQ;AAAA,EACpC;AAGA,QAAM,gBAAgB,CAAC,GAAG,OAAO,EAAE;AAAA,IACjC,CAAC,GAAG,MAAM,EAAE,MAAM,gBAAgB,EAAE,MAAM;AAAA,EAC5C;AAGA,MAAI,cAAc,SAAS,GAAG;AAC5B,UAAM,aAAa,cAAc,MAAM;AACvC,WAAO,YAAY,UAAU,QAAQ,OAAO,CAAC,MAAM,MAAM,UAAU;AACnE,WAAO;AAAA,EACT;AAEA,SAAO;AACT;", "names": []}