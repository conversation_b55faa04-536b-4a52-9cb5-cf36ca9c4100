/**
 * @license
 * web-streams-polyfill v4.1.0
 * Copyright 2024 <PERSON>, <PERSON><PERSON><PERSON> and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).WebStreamsPolyfill={})}(this,(function(e){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:function(e){return"Symbol(".concat(e,")")};function t(){}function n(e){return"object"==typeof e&&null!==e||"function"==typeof e}"function"==typeof SuppressedError&&SuppressedError;var o=t;function a(e,r){try{Object.defineProperty(e,"name",{value:r,configurable:!0})}catch(e){}}var i=Promise,l=Promise.resolve.bind(i),u=Promise.prototype.then,s=Promise.reject.bind(i),c=l;function f(e){return new i(e)}function d(e){return f((function(r){return r(e)}))}function b(e){return s(e)}function p(e,r,t){return u.call(e,r,t)}function h(e,r,t){p(p(e,r,t),void 0,o)}function _(e,r){h(e,r)}function m(e,r){h(e,void 0,r)}function v(e,r,t){return p(e,r,t)}function y(e){p(e,void 0,o)}var g=function(e){if("function"==typeof queueMicrotask)g=queueMicrotask;else{var r=d(void 0);g=function(e){return p(r,e)}}return g(e)};function S(e,r,t){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,r,t)}function w(e,r,t){try{return d(S(e,r,t))}catch(e){return b(e)}}var R=function(){function e(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.push=function(e){var r=this._back,t=r;16383===r._elements.length&&(t={_elements:[],_next:void 0}),r._elements.push(e),t!==r&&(this._back=t,r._next=t),++this._size},e.prototype.shift=function(){var e=this._front,r=e,t=this._cursor,n=t+1,o=e._elements,a=o[t];return 16384===n&&(r=e._next,n=0),--this._size,this._cursor=n,e!==r&&(this._front=r),o[t]=void 0,a},e.prototype.forEach=function(e){for(var r=this._cursor,t=this._front,n=t._elements;!(r===n.length&&void 0===t._next||r===n.length&&(r=0,0===(n=(t=t._next)._elements).length));)e(n[r]),++r},e.prototype.peek=function(){var e=this._front,r=this._cursor;return e._elements[r]},e}(),T=r("[[AbortSteps]]"),P=r("[[ErrorSteps]]"),C=r("[[CancelSteps]]"),q=r("[[PullSteps]]"),E=r("[[ReleaseSteps]]");function O(e,r){e._ownerReadableStream=r,r._reader=e,"readable"===r._state?k(e):"closed"===r._state?function(e){k(e),L(e)}(e):A(e,r._storedError)}function W(e,r){return Qt(e._ownerReadableStream,r)}function j(e){var r=e._ownerReadableStream;"readable"===r._state?z(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,r){A(e,r)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),r._readableStreamController[E](),r._reader=void 0,e._ownerReadableStream=void 0}function B(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function k(e){e._closedPromise=f((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t}))}function A(e,r){k(e),z(e,r)}function z(e,r){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function L(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}var D=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},F=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function I(e,r){if(void 0!==e&&("object"!=typeof(t=e)&&"function"!=typeof t))throw new TypeError("".concat(r," is not an object."));var t}function M(e,r){if("function"!=typeof e)throw new TypeError("".concat(r," is not a function."))}function x(e,r){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError("".concat(r," is not an object."))}function Y(e,r,t){if(void 0===e)throw new TypeError("Parameter ".concat(r," is required in '").concat(t,"'."))}function Q(e,r,t){if(void 0===e)throw new TypeError("".concat(r," is required in '").concat(t,"'."))}function N(e){return Number(e)}function H(e){return 0===e?0:e}function V(e,r){var t=Number.MAX_SAFE_INTEGER,n=Number(e);if(n=H(n),!D(n))throw new TypeError("".concat(r," is not a finite number"));if((n=function(e){return H(F(e))}(n))<0||n>t)throw new TypeError("".concat(r," is outside the accepted range of ").concat(0," to ").concat(t,", inclusive"));return D(n)&&0!==n?n:0}function U(e,r){if(!xt(e))throw new TypeError("".concat(r," is not a ReadableStream."))}function G(e){return new te(e)}function X(e,r){e._reader._readRequests.push(r)}function J(e,r,t){var n=e._reader._readRequests.shift();t?n._closeSteps():n._chunkSteps(r)}function K(e){return e._reader._readRequests.length}function Z(e){var r=e._reader;return void 0!==r&&!!ne(r)}var $,ee,re,te=function(){function ReadableStreamDefaultReader(e){if(Y(e,1,"ReadableStreamDefaultReader"),U(e,"First parameter"),Yt(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");O(this,e),this._readRequests=new R}return Object.defineProperty(ReadableStreamDefaultReader.prototype,"closed",{get:function(){return ne(this)?this._closedPromise:b(ie("closed"))},enumerable:!1,configurable:!0}),ReadableStreamDefaultReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),ne(this)?void 0===this._ownerReadableStream?b(B("cancel")):W(this,e):b(ie("cancel"))},ReadableStreamDefaultReader.prototype.read=function(){if(!ne(this))return b(ie("read"));if(void 0===this._ownerReadableStream)return b(B("read from"));var e,r,t=f((function(t,n){e=t,r=n}));return oe(this,{_chunkSteps:function(r){return e({value:r,done:!1})},_closeSteps:function(){return e({value:void 0,done:!0})},_errorSteps:function(e){return r(e)}}),t},ReadableStreamDefaultReader.prototype.releaseLock=function(){if(!ne(this))throw ie("releaseLock");void 0!==this._ownerReadableStream&&function(e){j(e);var r=new TypeError("Reader was released");ae(e,r)}(this)},ReadableStreamDefaultReader}();function ne(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof te)}function oe(e,r){var t=e._ownerReadableStream;t._disturbed=!0,"closed"===t._state?r._closeSteps():"errored"===t._state?r._errorSteps(t._storedError):t._readableStreamController[q](r)}function ae(e,r){var t=e._readRequests;e._readRequests=new R,t.forEach((function(e){e._errorSteps(r)}))}function ie(e){return new TypeError("ReadableStreamDefaultReader.prototype.".concat(e," can only be used on a ReadableStreamDefaultReader"))}function le(e){return e.slice()}function ue(e,r,t,n,o){new Uint8Array(e).set(new Uint8Array(t,n,o),r)}Object.defineProperties(te.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(te.prototype.cancel,"cancel"),a(te.prototype.read,"read"),a(te.prototype.releaseLock,"releaseLock"),"symbol"==typeof r.toStringTag&&Object.defineProperty(te.prototype,r.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});var se=function(e){return(se="function"==typeof e.transfer?function(e){return e.transfer()}:"function"==typeof structuredClone?function(e){return structuredClone(e,{transfer:[e]})}:function(e){return e})(e)},ce=function(e){return(ce="boolean"==typeof e.detached?function(e){return e.detached}:function(e){return 0===e.byteLength})(e)};function fe(e,r,t){if(e.slice)return e.slice(r,t);var n=t-r,o=new ArrayBuffer(n);return ue(o,0,e,r,n),o}function de(e,r){var t=e[r];if(null!=t){if("function"!=typeof t)throw new TypeError("".concat(String(r)," is not a function"));return t}}function be(e){try{var r=e.done,t=e.value;return p(c(t),(function(e){return{done:r,value:e}}))}catch(e){return b(e)}}var pe,he=null!==(re=null!==($=r.asyncIterator)&&void 0!==$?$:null===(ee=r.for)||void 0===ee?void 0:ee.call(r,"Symbol.asyncIterator"))&&void 0!==re?re:"@@asyncIterator";function _e(e,t,o){if(void 0===t&&(t="sync"),void 0===o)if("async"===t){if(void 0===(o=de(e,he)))return function(e){var r={next:function(){var r;try{r=me(e)}catch(e){return b(e)}return be(r)},return:function(r){var t;try{var o=de(e.iterator,"return");if(void 0===o)return d({done:!0,value:r});t=S(o,e.iterator,[r])}catch(e){return b(e)}return n(t)?be(t):b(new TypeError("The iterator.return() method must return an object"))}};return{iterator:r,nextMethod:r.next,done:!1}}(_e(e,"sync",de(e,r.iterator)))}else o=de(e,r.iterator);if(void 0===o)throw new TypeError("The object is not iterable");var a=S(o,e,[]);if(!n(a))throw new TypeError("The iterator method must return an object");return{iterator:a,nextMethod:a.next,done:!1}}function me(e){var r=S(e.nextMethod,e.iterator,[]);if(!n(r))throw new TypeError("The iterator.next() method must return an object");return r}var ve=function(){function e(e,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=r}return e.prototype.next=function(){var e=this,r=function(){return e._nextSteps()};return this._ongoingPromise=this._ongoingPromise?v(this._ongoingPromise,r,r):r(),this._ongoingPromise},e.prototype.return=function(e){var r=this,t=function(){return r._returnSteps(e)};return this._ongoingPromise=this._ongoingPromise?v(this._ongoingPromise,t,t):t(),this._ongoingPromise},e.prototype._nextSteps=function(){var e=this;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});var r,t,n=this._reader,o=f((function(e,n){r=e,t=n}));return oe(n,{_chunkSteps:function(t){e._ongoingPromise=void 0,g((function(){return r({value:t,done:!1})}))},_closeSteps:function(){e._ongoingPromise=void 0,e._isFinished=!0,j(n),r({value:void 0,done:!0})},_errorSteps:function(r){e._ongoingPromise=void 0,e._isFinished=!0,j(n),t(r)}}),o},e.prototype._returnSteps=function(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;var r=this._reader;if(!this._preventCancel){var t=W(r,e);return j(r),v(t,(function(){return{value:e,done:!0}}))}return j(r),d({value:e,done:!0})},e}(),ye=((pe={next:function(){return ge(this)?this._asyncIteratorImpl.next():b(Se("next"))},return:function(e){return ge(this)?this._asyncIteratorImpl.return(e):b(Se("return"))}})[he]=function(){return this},pe);function ge(e){if(!n(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof ve}catch(e){return!1}}function Se(e){return new TypeError("ReadableStreamAsyncIterator.".concat(e," can only be used on a ReadableSteamAsyncIterator"))}Object.defineProperty(ye,he,{enumerable:!1});var we=Number.isNaN||function(e){return e!=e};function Re(e){var r=fe(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(r)}function Te(e){var r=e._queue.shift();return e._queueTotalSize-=r.size,e._queueTotalSize<0&&(e._queueTotalSize=0),r.value}function Pe(e,r,t){if("number"!=typeof(n=t)||we(n)||n<0||t===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var n;e._queue.push({value:r,size:t}),e._queueTotalSize+=t}function Ce(e){e._queue=new R,e._queueTotalSize=0}function qe(e){return e===DataView}var Ee=function(){function ReadableStreamBYOBRequest(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamBYOBRequest.prototype,"view",{get:function(){if(!je(this))throw or("view");return this._view},enumerable:!1,configurable:!0}),ReadableStreamBYOBRequest.prototype.respond=function(e){if(!je(this))throw or("respond");if(Y(e,1,"respond"),e=V(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(ce(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");rr(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest.prototype.respondWithNewView=function(e){if(!je(this))throw or("respondWithNewView");if(Y(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(ce(e.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");tr(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest}();Object.defineProperties(Ee.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),a(Ee.prototype.respond,"respond"),a(Ee.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Ee.prototype,r.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});var Oe=function(){function ReadableByteStreamController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableByteStreamController.prototype,"byobRequest",{get:function(){if(!We(this))throw ar("byobRequest");return $e(this)},enumerable:!1,configurable:!0}),Object.defineProperty(ReadableByteStreamController.prototype,"desiredSize",{get:function(){if(!We(this))throw ar("desiredSize");return er(this)},enumerable:!1,configurable:!0}),ReadableByteStreamController.prototype.close=function(){if(!We(this))throw ar("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError("The stream (in ".concat(e," state) is not in the readable state and cannot be closed"));Xe(this)},ReadableByteStreamController.prototype.enqueue=function(e){if(!We(this))throw ar("enqueue");if(Y(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");var r=this._controlledReadableByteStream._state;if("readable"!==r)throw new TypeError("The stream (in ".concat(r," state) is not in the readable state and cannot be enqueued to"));Je(this,e)},ReadableByteStreamController.prototype.error=function(e){if(void 0===e&&(e=void 0),!We(this))throw ar("error");Ke(this,e)},ReadableByteStreamController.prototype[C]=function(e){ke(this),Ce(this);var r=this._cancelAlgorithm(e);return Ge(this),r},ReadableByteStreamController.prototype[q]=function(e){var r=this._controlledReadableByteStream;if(this._queueTotalSize>0)Ze(this,e);else{var t=this._autoAllocateChunkSize;if(void 0!==t){var n=void 0;try{n=new ArrayBuffer(t)}catch(r){return void e._errorSteps(r)}var o={buffer:n,bufferByteLength:t,byteOffset:0,byteLength:t,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}X(r,e),Be(this)}},ReadableByteStreamController.prototype[E]=function(){if(this._pendingPullIntos.length>0){var e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new R,this._pendingPullIntos.push(e)}},ReadableByteStreamController}();function We(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof Oe)}function je(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof Ee)}function Be(e){var r=function(e){var r=e._controlledReadableByteStream;if("readable"!==r._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(Z(r)&&K(r)>0)return!0;if(cr(r)&&sr(r)>0)return!0;var t=er(e);if(t>0)return!0;return!1}(e);r&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,h(e._pullAlgorithm(),(function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Be(e)),null}),(function(r){return Ke(e,r),null}))))}function ke(e){Qe(e),e._pendingPullIntos=new R}function Ae(e,r){var t=!1;"closed"===e._state&&(t=!0);var n=Le(r);"default"===r.readerType?J(e,n,t):function(e,r,t){var n=e._reader,o=n._readIntoRequests.shift();t?o._closeSteps(r):o._chunkSteps(r)}(e,n,t)}function ze(e,r){for(var t=0;t<r.length;++t)Ae(e,r[t])}function Le(e){var r=e.bytesFilled,t=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,r/t)}function De(e,r,t,n){e._queue.push({buffer:r,byteOffset:t,byteLength:n}),e._queueTotalSize+=n}function Fe(e,r,t,n){var o;try{o=fe(r,t,t+n)}catch(r){throw Ke(e,r),r}De(e,o,0,n)}function Ie(e,r){r.bytesFilled>0&&Fe(e,r.buffer,r.byteOffset,r.bytesFilled),Ue(e)}function Me(e,r){var t=Math.min(e._queueTotalSize,r.byteLength-r.bytesFilled),n=r.bytesFilled+t,o=t,a=!1,i=n-n%r.elementSize;i>=r.minimumFill&&(o=i-r.bytesFilled,a=!0);for(var l=e._queue;o>0;){var u=l.peek(),s=Math.min(o,u.byteLength),c=r.byteOffset+r.bytesFilled;ue(r.buffer,c,u.buffer,u.byteOffset,s),u.byteLength===s?l.shift():(u.byteOffset+=s,u.byteLength-=s),e._queueTotalSize-=s,xe(e,s,r),o-=s}return a}function xe(e,r,t){t.bytesFilled+=r}function Ye(e){0===e._queueTotalSize&&e._closeRequested?(Ge(e),Nt(e._controlledReadableByteStream)):Be(e)}function Qe(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Ne(e){for(var r=[];e._pendingPullIntos.length>0&&0!==e._queueTotalSize;){var t=e._pendingPullIntos.peek();Me(e,t)&&(Ue(e),r.push(t))}return r}function He(e,r,t,n){var o,a=e._controlledReadableByteStream,i=r.constructor,l=function(e){return qe(e)?1:e.BYTES_PER_ELEMENT}(i),u=r.byteOffset,s=r.byteLength,c=t*l;try{o=se(r.buffer)}catch(b){return void n._errorSteps(b)}var f={buffer:o,bufferByteLength:o.byteLength,byteOffset:u,byteLength:s,bytesFilled:0,minimumFill:c,elementSize:l,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(f),void ur(a,n);if("closed"!==a._state){if(e._queueTotalSize>0){if(Me(e,f)){var d=Le(f);return Ye(e),void n._chunkSteps(d)}if(e._closeRequested){var b=new TypeError("Insufficient bytes to fill elements in the given buffer");return Ke(e,b),void n._errorSteps(b)}}e._pendingPullIntos.push(f),ur(a,n),Be(e)}else{var p=new i(f.buffer,f.byteOffset,0);n._closeSteps(p)}}function Ve(e,r){var t=e._pendingPullIntos.peek();Qe(e),"closed"===e._controlledReadableByteStream._state?function(e,r){"none"===r.readerType&&Ue(e);var t=e._controlledReadableByteStream;if(cr(t)){for(var n=[],o=0;o<sr(t);++o)n.push(Ue(e));ze(t,n)}}(e,t):function(e,r,t){if(xe(0,r,t),"none"!==t.readerType){if(!(t.bytesFilled<t.minimumFill)){Ue(e);var n=t.bytesFilled%t.elementSize;if(n>0){var o=t.byteOffset+t.bytesFilled;Fe(e,t.buffer,o-n,n)}t.bytesFilled-=n;var a=Ne(e);Ae(e._controlledReadableByteStream,t),ze(e._controlledReadableByteStream,a)}}else{Ie(e,t);var i=Ne(e);ze(e._controlledReadableByteStream,i)}}(e,r,t),Be(e)}function Ue(e){return e._pendingPullIntos.shift()}function Ge(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Xe(e){var r=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===r._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){var t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){var n=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Ke(e,n),n}}Ge(e),Nt(r)}}function Je(e,r){var t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){var n=r.buffer,o=r.byteOffset,a=r.byteLength;if(ce(n))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");var i=se(n);if(e._pendingPullIntos.length>0){var l=e._pendingPullIntos.peek();if(ce(l.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");Qe(e),l.buffer=se(l.buffer),"none"===l.readerType&&Ie(e,l)}if(Z(t))if(function(e){for(var r=e._controlledReadableByteStream._reader;r._readRequests.length>0;){if(0===e._queueTotalSize)return;Ze(e,r._readRequests.shift())}}(e),0===K(t))De(e,i,o,a);else e._pendingPullIntos.length>0&&Ue(e),J(t,new Uint8Array(i,o,a),!1);else if(cr(t)){De(e,i,o,a);var u=Ne(e);ze(e._controlledReadableByteStream,u)}else De(e,i,o,a);Be(e)}}function Ke(e,r){var t=e._controlledReadableByteStream;"readable"===t._state&&(ke(e),Ce(e),Ge(e),Ht(t,r))}function Ze(e,r){var t=e._queue.shift();e._queueTotalSize-=t.byteLength,Ye(e);var n=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);r._chunkSteps(n)}function $e(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){var r=e._pendingPullIntos.peek(),t=new Uint8Array(r.buffer,r.byteOffset+r.bytesFilled,r.byteLength-r.bytesFilled),n=Object.create(Ee.prototype);!function(e,r,t){e._associatedReadableByteStreamController=r,e._view=t}(n,e,t),e._byobRequest=n}return e._byobRequest}function er(e){var r=e._controlledReadableByteStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function rr(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===r)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(t.bytesFilled+r>t.byteLength)throw new RangeError("bytesWritten out of range")}t.buffer=se(t.buffer),Ve(e,r)}function tr(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===r.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(t.byteOffset+t.bytesFilled!==r.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(t.bufferByteLength!==r.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(t.bytesFilled+r.byteLength>t.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");var n=r.byteLength;t.buffer=se(r.buffer),Ve(e,n)}function nr(e,r,t,n,o,a,i){r._controlledReadableByteStream=e,r._pullAgain=!1,r._pulling=!1,r._byobRequest=null,r._queue=r._queueTotalSize=void 0,Ce(r),r._closeRequested=!1,r._started=!1,r._strategyHWM=a,r._pullAlgorithm=n,r._cancelAlgorithm=o,r._autoAllocateChunkSize=i,r._pendingPullIntos=new R,e._readableStreamController=r,h(d(t()),(function(){return r._started=!0,Be(r),null}),(function(e){return Ke(r,e),null}))}function or(e){return new TypeError("ReadableStreamBYOBRequest.prototype.".concat(e," can only be used on a ReadableStreamBYOBRequest"))}function ar(e){return new TypeError("ReadableByteStreamController.prototype.".concat(e," can only be used on a ReadableByteStreamController"))}function ir(e,r){if("byob"!==(e="".concat(e)))throw new TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamReaderMode"));return e}function lr(e){return new fr(e)}function ur(e,r){e._reader._readIntoRequests.push(r)}function sr(e){return e._reader._readIntoRequests.length}function cr(e){var r=e._reader;return void 0!==r&&!!dr(r)}Object.defineProperties(Oe.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),a(Oe.prototype.close,"close"),a(Oe.prototype.enqueue,"enqueue"),a(Oe.prototype.error,"error"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Oe.prototype,r.toStringTag,{value:"ReadableByteStreamController",configurable:!0});var fr=function(){function ReadableStreamBYOBReader(e){if(Y(e,1,"ReadableStreamBYOBReader"),U(e,"First parameter"),Yt(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!We(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");O(this,e),this._readIntoRequests=new R}return Object.defineProperty(ReadableStreamBYOBReader.prototype,"closed",{get:function(){return dr(this)?this._closedPromise:b(hr("closed"))},enumerable:!1,configurable:!0}),ReadableStreamBYOBReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),dr(this)?void 0===this._ownerReadableStream?b(B("cancel")):W(this,e):b(hr("cancel"))},ReadableStreamBYOBReader.prototype.read=function(e,r){if(void 0===r&&(r={}),!dr(this))return b(hr("read"));if(!ArrayBuffer.isView(e))return b(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return b(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return b(new TypeError("view's buffer must have non-zero byteLength"));if(ce(e.buffer))return b(new TypeError("view's buffer has been detached"));var t;try{t=function(e,r){var t;return I(e,r),{min:V(null!==(t=null==e?void 0:e.min)&&void 0!==t?t:1,"".concat(r," has member 'min' that"))}}(r,"options")}catch(e){return b(e)}var n,o,a=t.min;if(0===a)return b(new TypeError("options.min must be greater than 0"));if(function(e){return qe(e.constructor)}(e)){if(a>e.byteLength)return b(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(a>e.length)return b(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return b(B("read from"));var i=f((function(e,r){n=e,o=r}));return br(this,e,a,{_chunkSteps:function(e){return n({value:e,done:!1})},_closeSteps:function(e){return n({value:e,done:!0})},_errorSteps:function(e){return o(e)}}),i},ReadableStreamBYOBReader.prototype.releaseLock=function(){if(!dr(this))throw hr("releaseLock");void 0!==this._ownerReadableStream&&function(e){j(e);var r=new TypeError("Reader was released");pr(e,r)}(this)},ReadableStreamBYOBReader}();function dr(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof fr)}function br(e,r,t,n){var o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?n._errorSteps(o._storedError):He(o._readableStreamController,r,t,n)}function pr(e,r){var t=e._readIntoRequests;e._readIntoRequests=new R,t.forEach((function(e){e._errorSteps(r)}))}function hr(e){return new TypeError("ReadableStreamBYOBReader.prototype.".concat(e," can only be used on a ReadableStreamBYOBReader"))}function _r(e,r){var t=e.highWaterMark;if(void 0===t)return r;if(we(t)||t<0)throw new RangeError("Invalid highWaterMark");return t}function mr(e){var r=e.size;return r||function(){return 1}}function vr(e,r){I(e,r);var t=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===t?void 0:N(t),size:void 0===n?void 0:yr(n,"".concat(r," has member 'size' that"))}}function yr(e,r){return M(e,r),function(r){return N(e(r))}}function gr(e,r,t){return M(e,t),function(t){return w(e,r,[t])}}function Sr(e,r,t){return M(e,t),function(){return w(e,r,[])}}function wr(e,r,t){return M(e,t),function(t){return S(e,r,[t])}}function Rr(e,r,t){return M(e,t),function(t,n){return w(e,r,[t,n])}}function Tr(e,r){if(!Er(e))throw new TypeError("".concat(r," is not a WritableStream."))}Object.defineProperties(fr.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(fr.prototype.cancel,"cancel"),a(fr.prototype.read,"read"),a(fr.prototype.releaseLock,"releaseLock"),"symbol"==typeof r.toStringTag&&Object.defineProperty(fr.prototype,r.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});var Pr=function(){function WritableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:x(e,"First parameter");var t=vr(r,"Second parameter"),n=function(e,r){I(e,r);var t=null==e?void 0:e.abort,n=null==e?void 0:e.close,o=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===t?void 0:gr(t,e,"".concat(r," has member 'abort' that")),close:void 0===n?void 0:Sr(n,e,"".concat(r," has member 'close' that")),start:void 0===o?void 0:wr(o,e,"".concat(r," has member 'start' that")),write:void 0===i?void 0:Rr(i,e,"".concat(r," has member 'write' that")),type:a}}(e,"First parameter");if(qr(this),void 0!==n.type)throw new RangeError("Invalid type is specified");var o=mr(t);!function(e,r,t,n){var o,a,i,l,u=Object.create(Vr.prototype);o=void 0!==r.start?function(){return r.start(u)}:function(){};a=void 0!==r.write?function(e){return r.write(e,u)}:function(){return d(void 0)};i=void 0!==r.close?function(){return r.close()}:function(){return d(void 0)};l=void 0!==r.abort?function(e){return r.abort(e)}:function(){return d(void 0)};Gr(e,u,o,a,i,l,t,n)}(this,n,_r(t,1),o)}return Object.defineProperty(WritableStream.prototype,"locked",{get:function(){if(!Er(this))throw rt("locked");return Or(this)},enumerable:!1,configurable:!0}),WritableStream.prototype.abort=function(e){return void 0===e&&(e=void 0),Er(this)?Or(this)?b(new TypeError("Cannot abort a stream that already has a writer")):Wr(this,e):b(rt("abort"))},WritableStream.prototype.close=function(){return Er(this)?Or(this)?b(new TypeError("Cannot close a stream that already has a writer")):zr(this)?b(new TypeError("Cannot close an already-closing stream")):jr(this):b(rt("close"))},WritableStream.prototype.getWriter=function(){if(!Er(this))throw rt("getWriter");return Cr(this)},WritableStream}();function Cr(e){return new Fr(e)}function qr(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new R,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function Er(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof Pr)}function Or(e){return void 0!==e._writer}function Wr(e,r){var t;if("closed"===e._state||"errored"===e._state)return d(void 0);e._writableStreamController._abortReason=r,null===(t=e._writableStreamController._abortController)||void 0===t||t.abort(r);var n=e._state;if("closed"===n||"errored"===n)return d(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;var o=!1;"erroring"===n&&(o=!0,r=void 0);var a=f((function(t,n){e._pendingAbortRequest={_promise:void 0,_resolve:t,_reject:n,_reason:r,_wasAlreadyErroring:o}}));return e._pendingAbortRequest._promise=a,o||kr(e,r),a}function jr(e){var r=e._state;if("closed"===r||"errored"===r)return b(new TypeError("The stream (in ".concat(r," state) is not in the writable state and cannot be closed")));var t,n=f((function(r,t){var n={_resolve:r,_reject:t};e._closeRequest=n})),o=e._writer;return void 0!==o&&e._backpressure&&"writable"===r&&bt(o),Pe(t=e._writableStreamController,Hr,0),Kr(t),n}function Br(e,r){"writable"!==e._state?Ar(e):kr(e,r)}function kr(e,r){var t=e._writableStreamController;e._state="erroring",e._storedError=r;var n=e._writer;void 0!==n&&Yr(n,r),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&t._started&&Ar(e)}function Ar(e){e._state="errored",e._writableStreamController[P]();var r=e._storedError;if(e._writeRequests.forEach((function(e){e._reject(r)})),e._writeRequests=new R,void 0!==e._pendingAbortRequest){var t=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,t._wasAlreadyErroring)return t._reject(r),void Lr(e);h(e._writableStreamController[T](t._reason),(function(){return t._resolve(),Lr(e),null}),(function(r){return t._reject(r),Lr(e),null}))}else Lr(e)}function zr(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function Lr(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var r=e._writer;void 0!==r&&lt(r,e._storedError)}function Dr(e,r){var t=e._writer;void 0!==t&&r!==e._backpressure&&(r?function(e){st(e)}(t):bt(t)),e._backpressure=r}Object.defineProperties(Pr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),a(Pr.prototype.abort,"abort"),a(Pr.prototype.close,"close"),a(Pr.prototype.getWriter,"getWriter"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Pr.prototype,r.toStringTag,{value:"WritableStream",configurable:!0});var Fr=function(){function WritableStreamDefaultWriter(e){if(Y(e,1,"WritableStreamDefaultWriter"),Tr(e,"First parameter"),Or(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;var r,t=e._state;if("writable"===t)!zr(e)&&e._backpressure?st(this):ft(this),at(this);else if("erroring"===t)ct(this,e._storedError),at(this);else if("closed"===t)ft(this),at(r=this),ut(r);else{var n=e._storedError;ct(this,n),it(this,n)}}return Object.defineProperty(WritableStreamDefaultWriter.prototype,"closed",{get:function(){return Ir(this)?this._closedPromise:b(nt("closed"))},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"desiredSize",{get:function(){if(!Ir(this))throw nt("desiredSize");if(void 0===this._ownerWritableStream)throw ot("desiredSize");return function(e){var r=e._ownerWritableStream,t=r._state;if("errored"===t||"erroring"===t)return null;if("closed"===t)return 0;return Jr(r._writableStreamController)}(this)},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"ready",{get:function(){return Ir(this)?this._readyPromise:b(nt("ready"))},enumerable:!1,configurable:!0}),WritableStreamDefaultWriter.prototype.abort=function(e){return void 0===e&&(e=void 0),Ir(this)?void 0===this._ownerWritableStream?b(ot("abort")):function(e,r){return Wr(e._ownerWritableStream,r)}(this,e):b(nt("abort"))},WritableStreamDefaultWriter.prototype.close=function(){if(!Ir(this))return b(nt("close"));var e=this._ownerWritableStream;return void 0===e?b(ot("close")):zr(e)?b(new TypeError("Cannot close an already-closing stream")):Mr(this)},WritableStreamDefaultWriter.prototype.releaseLock=function(){if(!Ir(this))throw nt("releaseLock");void 0!==this._ownerWritableStream&&Qr(this)},WritableStreamDefaultWriter.prototype.write=function(e){return void 0===e&&(e=void 0),Ir(this)?void 0===this._ownerWritableStream?b(ot("write to")):Nr(this,e):b(nt("write"))},WritableStreamDefaultWriter}();function Ir(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof Fr)}function Mr(e){return jr(e._ownerWritableStream)}function xr(e,r){"pending"===e._closedPromiseState?lt(e,r):function(e,r){it(e,r)}(e,r)}function Yr(e,r){"pending"===e._readyPromiseState?dt(e,r):function(e,r){ct(e,r)}(e,r)}function Qr(e){var r=e._ownerWritableStream,t=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");Yr(e,t),xr(e,t),r._writer=void 0,e._ownerWritableStream=void 0}function Nr(e,r){var t=e._ownerWritableStream,n=t._writableStreamController,o=function(e,r){if(void 0===e._strategySizeAlgorithm)return 1;try{return e._strategySizeAlgorithm(r)}catch(r){return Zr(e,r),1}}(n,r);if(t!==e._ownerWritableStream)return b(ot("write to"));var a=t._state;if("errored"===a)return b(t._storedError);if(zr(t)||"closed"===a)return b(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return b(t._storedError);var i=function(e){return f((function(r,t){var n={_resolve:r,_reject:t};e._writeRequests.push(n)}))}(t);return function(e,r,t){try{Pe(e,r,t)}catch(r){return void Zr(e,r)}var n=e._controlledWritableStream;if(!zr(n)&&"writable"===n._state){Dr(n,$r(e))}Kr(e)}(n,r,o),i}Object.defineProperties(Fr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),a(Fr.prototype.abort,"abort"),a(Fr.prototype.close,"close"),a(Fr.prototype.releaseLock,"releaseLock"),a(Fr.prototype.write,"write"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Fr.prototype,r.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});var Hr={},Vr=function(){function WritableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(WritableStreamDefaultController.prototype,"abortReason",{get:function(){if(!Ur(this))throw tt("abortReason");return this._abortReason},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultController.prototype,"signal",{get:function(){if(!Ur(this))throw tt("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal},enumerable:!1,configurable:!0}),WritableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Ur(this))throw tt("error");"writable"===this._controlledWritableStream._state&&et(this,e)},WritableStreamDefaultController.prototype[T]=function(e){var r=this._abortAlgorithm(e);return Xr(this),r},WritableStreamDefaultController.prototype[P]=function(){Ce(this)},WritableStreamDefaultController}();function Ur(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof Vr)}function Gr(e,r,t,n,o,a,i,l){r._controlledWritableStream=e,e._writableStreamController=r,r._queue=void 0,r._queueTotalSize=void 0,Ce(r),r._abortReason=void 0,r._abortController=function(){if("function"==typeof AbortController)return new AbortController}(),r._started=!1,r._strategySizeAlgorithm=l,r._strategyHWM=i,r._writeAlgorithm=n,r._closeAlgorithm=o,r._abortAlgorithm=a;var u=$r(r);Dr(e,u),h(d(t()),(function(){return r._started=!0,Kr(r),null}),(function(t){return r._started=!0,Br(e,t),null}))}function Xr(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Jr(e){return e._strategyHWM-e._queueTotalSize}function Kr(e){var r=e._controlledWritableStream;if(e._started&&void 0===r._inFlightWriteRequest)if("erroring"!==r._state){if(0!==e._queue.length){var t=e._queue.peek().value;t===Hr?function(e){var r=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(r),Te(e);var t=e._closeAlgorithm();Xr(e),h(t,(function(){return function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&ut(r)}(r),null}),(function(e){return function(e,r){e._inFlightCloseRequest._reject(r),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(r),e._pendingAbortRequest=void 0),Br(e,r)}(r,e),null}))}(e):function(e,r){var t=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(t);var n=e._writeAlgorithm(r);h(n,(function(){!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(t);var r=t._state;if(Te(e),!zr(t)&&"writable"===r){var n=$r(e);Dr(t,n)}return Kr(e),null}),(function(r){return"writable"===t._state&&Xr(e),function(e,r){e._inFlightWriteRequest._reject(r),e._inFlightWriteRequest=void 0,Br(e,r)}(t,r),null}))}(e,t)}}else Ar(r)}function Zr(e,r){"writable"===e._controlledWritableStream._state&&et(e,r)}function $r(e){return Jr(e)<=0}function et(e,r){var t=e._controlledWritableStream;Xr(e),kr(t,r)}function rt(e){return new TypeError("WritableStream.prototype.".concat(e," can only be used on a WritableStream"))}function tt(e){return new TypeError("WritableStreamDefaultController.prototype.".concat(e," can only be used on a WritableStreamDefaultController"))}function nt(e){return new TypeError("WritableStreamDefaultWriter.prototype.".concat(e," can only be used on a WritableStreamDefaultWriter"))}function ot(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function at(e){e._closedPromise=f((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t,e._closedPromiseState="pending"}))}function it(e,r){at(e),lt(e,r)}function lt(e,r){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function ut(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function st(e){e._readyPromise=f((function(r,t){e._readyPromise_resolve=r,e._readyPromise_reject=t})),e._readyPromiseState="pending"}function ct(e,r){st(e),dt(e,r)}function ft(e){st(e),bt(e)}function dt(e,r){void 0!==e._readyPromise_reject&&(y(e._readyPromise),e._readyPromise_reject(r),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function bt(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(Vr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Vr.prototype,r.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});var pt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0;var ht,_t=(function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;if("DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(ht=null==pt?void 0:pt.DOMException)?ht:void 0)||function(){var e=function(e,r){this.message=e||"",this.name=r||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return a(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function mt(e,r,n,o,a,i){var l=G(e),u=Cr(r);e._disturbed=!0;var s=!1,c=d(void 0);return f((function(v,g){var S,w,R,T;if(void 0!==i){if(S=function(){var t=void 0!==i.reason?i.reason:new _t("Aborted","AbortError"),n=[];o||n.push((function(){return"writable"===r._state?Wr(r,t):d(void 0)})),a||n.push((function(){return"readable"===e._state?Qt(e,t):d(void 0)})),E((function(){return Promise.all(n.map((function(e){return e()})))}),!0,t)},i.aborted)return void S();i.addEventListener("abort",S)}if(q(e,l._closedPromise,(function(e){return o?O(!0,e):E((function(){return Wr(r,e)}),!0,e),null})),q(r,u._closedPromise,(function(r){return a?O(!0,r):E((function(){return Qt(e,r)}),!0,r),null})),w=e,R=l._closedPromise,T=function(){return n?O():E((function(){return function(e){var r=e._ownerWritableStream,t=r._state;return zr(r)||"closed"===t?d(void 0):"errored"===t?b(r._storedError):Mr(e)}(u)})),null},"closed"===w._state?T():_(R,T),zr(r)||"closed"===r._state){var P=new TypeError("the destination writable stream closed before all data could be piped to it");a?O(!0,P):E((function(){return Qt(e,P)}),!0,P)}function C(){var e=c;return p(c,(function(){return e!==c?C():void 0}))}function q(e,r,t){"errored"===e._state?t(e._storedError):m(r,t)}function E(e,t,n){function o(){return h(e(),(function(){return W(t,n)}),(function(e){return W(!0,e)})),null}s||(s=!0,"writable"!==r._state||zr(r)?o():_(C(),o))}function O(e,t){s||(s=!0,"writable"!==r._state||zr(r)?W(e,t):_(C(),(function(){return W(e,t)})))}function W(e,r){return Qr(u),j(l),void 0!==i&&i.removeEventListener("abort",S),e?g(r):v(void 0),null}y(f((function(e,r){!function n(o){o?e():p(s?d(!0):p(u._readyPromise,(function(){return f((function(e,r){oe(l,{_chunkSteps:function(r){c=p(Nr(u,r),void 0,t),e(!1)},_closeSteps:function(){return e(!0)},_errorSteps:r})}))})),n,r)}(!1)})))}))}var vt=function(){function ReadableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamDefaultController.prototype,"desiredSize",{get:function(){if(!yt(this))throw Ot("desiredSize");return Ct(this)},enumerable:!1,configurable:!0}),ReadableStreamDefaultController.prototype.close=function(){if(!yt(this))throw Ot("close");if(!qt(this))throw new TypeError("The stream is not in a state that permits close");Rt(this)},ReadableStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!yt(this))throw Ot("enqueue");if(!qt(this))throw new TypeError("The stream is not in a state that permits enqueue");return Tt(this,e)},ReadableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!yt(this))throw Ot("error");Pt(this,e)},ReadableStreamDefaultController.prototype[C]=function(e){Ce(this);var r=this._cancelAlgorithm(e);return wt(this),r},ReadableStreamDefaultController.prototype[q]=function(e){var r=this._controlledReadableStream;if(this._queue.length>0){var t=Te(this);this._closeRequested&&0===this._queue.length?(wt(this),Nt(r)):gt(this),e._chunkSteps(t)}else X(r,e),gt(this)},ReadableStreamDefaultController.prototype[E]=function(){},ReadableStreamDefaultController}();function yt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof vt)}function gt(e){St(e)&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,h(e._pullAlgorithm(),(function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,gt(e)),null}),(function(r){return Pt(e,r),null}))))}function St(e){var r=e._controlledReadableStream;return!!qt(e)&&(!!e._started&&(!!(Yt(r)&&K(r)>0)||Ct(e)>0))}function wt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Rt(e){if(qt(e)){var r=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(wt(e),Nt(r))}}function Tt(e,r){if(qt(e)){var t=e._controlledReadableStream;if(Yt(t)&&K(t)>0)J(t,r,!1);else{var n=void 0;try{n=e._strategySizeAlgorithm(r)}catch(r){throw Pt(e,r),r}try{Pe(e,r,n)}catch(r){throw Pt(e,r),r}}gt(e)}}function Pt(e,r){var t=e._controlledReadableStream;"readable"===t._state&&(Ce(e),wt(e),Ht(t,r))}function Ct(e){var r=e._controlledReadableStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function qt(e){var r=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===r}function Et(e,r,t,n,o,a,i){r._controlledReadableStream=e,r._queue=void 0,r._queueTotalSize=void 0,Ce(r),r._started=!1,r._closeRequested=!1,r._pullAgain=!1,r._pulling=!1,r._strategySizeAlgorithm=i,r._strategyHWM=a,r._pullAlgorithm=n,r._cancelAlgorithm=o,e._readableStreamController=r,h(d(t()),(function(){return r._started=!0,gt(r),null}),(function(e){return Pt(r,e),null}))}function Ot(e){return new TypeError("ReadableStreamDefaultController.prototype.".concat(e," can only be used on a ReadableStreamDefaultController"))}function Wt(e,r){return We(e._readableStreamController)?function(e){var r,t,n,o,a,i=G(e),l=!1,u=!1,s=!1,c=!1,b=!1,p=f((function(e){a=e}));function h(e){m(e._closedPromise,(function(r){return e!==i||(Ke(n._readableStreamController,r),Ke(o._readableStreamController,r),c&&b||a(void 0)),null}))}function _(){dr(i)&&(j(i),h(i=G(e))),oe(i,{_chunkSteps:function(r){g((function(){u=!1,s=!1;var t=r,i=r;if(!c&&!b)try{i=Re(r)}catch(r){return Ke(n._readableStreamController,r),Ke(o._readableStreamController,r),void a(Qt(e,r))}c||Je(n._readableStreamController,t),b||Je(o._readableStreamController,i),l=!1,u?y():s&&S()}))},_closeSteps:function(){l=!1,c||Xe(n._readableStreamController),b||Xe(o._readableStreamController),n._readableStreamController._pendingPullIntos.length>0&&rr(n._readableStreamController,0),o._readableStreamController._pendingPullIntos.length>0&&rr(o._readableStreamController,0),c&&b||a(void 0)},_errorSteps:function(){l=!1}})}function v(r,t){ne(i)&&(j(i),h(i=lr(e)));var f=t?o:n,d=t?n:o;br(i,r,1,{_chunkSteps:function(r){g((function(){u=!1,s=!1;var n=t?b:c;if(t?c:b)n||tr(f._readableStreamController,r);else{var o=void 0;try{o=Re(r)}catch(r){return Ke(f._readableStreamController,r),Ke(d._readableStreamController,r),void a(Qt(e,r))}n||tr(f._readableStreamController,r),Je(d._readableStreamController,o)}l=!1,u?y():s&&S()}))},_closeSteps:function(e){l=!1;var r=t?b:c,n=t?c:b;r||Xe(f._readableStreamController),n||Xe(d._readableStreamController),void 0!==e&&(r||tr(f._readableStreamController,e),!n&&d._readableStreamController._pendingPullIntos.length>0&&rr(d._readableStreamController,0)),r&&n||a(void 0)},_errorSteps:function(){l=!1}})}function y(){if(l)return u=!0,d(void 0);l=!0;var e=$e(n._readableStreamController);return null===e?_():v(e._view,!1),d(void 0)}function S(){if(l)return s=!0,d(void 0);l=!0;var e=$e(o._readableStreamController);return null===e?_():v(e._view,!0),d(void 0)}function w(n){if(c=!0,r=n,b){var o=le([r,t]),i=Qt(e,o);a(i)}return p}function R(n){if(b=!0,t=n,c){var o=le([r,t]),i=Qt(e,o);a(i)}return p}function T(){}return n=It(T,y,w),o=It(T,S,R),h(i),[n,o]}(e):function(e,r){var t,n,o,a,i,l=G(e),u=!1,s=!1,c=!1,b=!1,p=f((function(e){i=e}));function h(){return u?(s=!0,d(void 0)):(u=!0,oe(l,{_chunkSteps:function(e){g((function(){s=!1;var r=e,t=e;c||Tt(o._readableStreamController,r),b||Tt(a._readableStreamController,t),u=!1,s&&h()}))},_closeSteps:function(){u=!1,c||Rt(o._readableStreamController),b||Rt(a._readableStreamController),c&&b||i(void 0)},_errorSteps:function(){u=!1}}),d(void 0))}function _(r){if(c=!0,t=r,b){var o=le([t,n]),a=Qt(e,o);i(a)}return p}function v(r){if(b=!0,n=r,c){var o=le([t,n]),a=Qt(e,o);i(a)}return p}function y(){}return o=Ft(y,h,_),a=Ft(y,h,v),m(l._closedPromise,(function(e){return Pt(o._readableStreamController,e),Pt(a._readableStreamController,e),c&&b||i(void 0),null})),[o,a]}(e)}function jt(e){return n(r=e)&&void 0!==r.getReader?function(e){var r;function o(){var t;try{t=e.read()}catch(e){return b(e)}return v(t,(function(e){if(!n(e))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)Rt(r._readableStreamController);else{var t=e.value;Tt(r._readableStreamController,t)}}))}function a(r){try{return d(e.cancel(r))}catch(e){return b(e)}}return r=Ft(t,o,a,0),r}(e.getReader()):function(e){var r,o=_e(e,"async");function a(){var e;try{e=me(o)}catch(e){return b(e)}return v(d(e),(function(e){if(!n(e))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)Rt(r._readableStreamController);else{var t=e.value;Tt(r._readableStreamController,t)}}))}function i(e){var r,t=o.iterator;try{r=de(t,"return")}catch(e){return b(e)}return void 0===r?d(void 0):v(w(r,t,[e]),(function(e){if(!n(e))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}return r=Ft(t,a,i,0),r}(e);var r}function Bt(e,r,t){return M(e,t),function(t){return w(e,r,[t])}}function kt(e,r,t){return M(e,t),function(t){return w(e,r,[t])}}function At(e,r,t){return M(e,t),function(t){return S(e,r,[t])}}function zt(e,r){if("bytes"!==(e="".concat(e)))throw new TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamType"));return e}function Lt(e,r){I(e,r);var t=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,o=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,r){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError("".concat(r," is not an AbortSignal."))}(a,"".concat(r," has member 'signal' that")),{preventAbort:Boolean(t),preventCancel:Boolean(n),preventClose:Boolean(o),signal:a}}Object.defineProperties(vt.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),a(vt.prototype.close,"close"),a(vt.prototype.enqueue,"enqueue"),a(vt.prototype.error,"error"),"symbol"==typeof r.toStringTag&&Object.defineProperty(vt.prototype,r.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});var Dt=function(){function ReadableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:x(e,"First parameter");var t=vr(r,"Second parameter"),n=function(e,r){I(e,r);var t=e,n=null==t?void 0:t.autoAllocateChunkSize,o=null==t?void 0:t.cancel,a=null==t?void 0:t.pull,i=null==t?void 0:t.start,l=null==t?void 0:t.type;return{autoAllocateChunkSize:void 0===n?void 0:V(n,"".concat(r," has member 'autoAllocateChunkSize' that")),cancel:void 0===o?void 0:Bt(o,t,"".concat(r," has member 'cancel' that")),pull:void 0===a?void 0:kt(a,t,"".concat(r," has member 'pull' that")),start:void 0===i?void 0:At(i,t,"".concat(r," has member 'start' that")),type:void 0===l?void 0:zt(l,"".concat(r," has member 'type' that"))}}(e,"First parameter");if(Mt(this),"bytes"===n.type){if(void 0!==t.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,r,t){var n,o,a,i=Object.create(Oe.prototype);n=void 0!==r.start?function(){return r.start(i)}:function(){},o=void 0!==r.pull?function(){return r.pull(i)}:function(){return d(void 0)},a=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return d(void 0)};var l=r.autoAllocateChunkSize;if(0===l)throw new TypeError("autoAllocateChunkSize must be greater than 0");nr(e,i,n,o,a,t,l)}(this,n,_r(t,0))}else{var o=mr(t);!function(e,r,t,n){var o,a,i,l=Object.create(vt.prototype);o=void 0!==r.start?function(){return r.start(l)}:function(){},a=void 0!==r.pull?function(){return r.pull(l)}:function(){return d(void 0)},i=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return d(void 0)},Et(e,l,o,a,i,t,n)}(this,n,_r(t,1),o)}}return Object.defineProperty(ReadableStream.prototype,"locked",{get:function(){if(!xt(this))throw Vt("locked");return Yt(this)},enumerable:!1,configurable:!0}),ReadableStream.prototype.cancel=function(e){return void 0===e&&(e=void 0),xt(this)?Yt(this)?b(new TypeError("Cannot cancel a stream that already has a reader")):Qt(this,e):b(Vt("cancel"))},ReadableStream.prototype.getReader=function(e){if(void 0===e&&(e=void 0),!xt(this))throw Vt("getReader");return void 0===function(e,r){I(e,r);var t=null==e?void 0:e.mode;return{mode:void 0===t?void 0:ir(t,"".concat(r," has member 'mode' that"))}}(e,"First parameter").mode?G(this):lr(this)},ReadableStream.prototype.pipeThrough=function(e,r){if(void 0===r&&(r={}),!xt(this))throw Vt("pipeThrough");Y(e,1,"pipeThrough");var t=function(e,r){I(e,r);var t=null==e?void 0:e.readable;Q(t,"readable","ReadableWritablePair"),U(t,"".concat(r," has member 'readable' that"));var n=null==e?void 0:e.writable;return Q(n,"writable","ReadableWritablePair"),Tr(n,"".concat(r," has member 'writable' that")),{readable:t,writable:n}}(e,"First parameter"),n=Lt(r,"Second parameter");if(Yt(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Or(t.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return y(mt(this,t.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),t.readable},ReadableStream.prototype.pipeTo=function(e,r){if(void 0===r&&(r={}),!xt(this))return b(Vt("pipeTo"));if(void 0===e)return b("Parameter 1 is required in 'pipeTo'.");if(!Er(e))return b(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));var t;try{t=Lt(r,"Second parameter")}catch(e){return b(e)}return Yt(this)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Or(e)?b(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):mt(this,e,t.preventClose,t.preventAbort,t.preventCancel,t.signal)},ReadableStream.prototype.tee=function(){if(!xt(this))throw Vt("tee");return le(Wt(this))},ReadableStream.prototype.values=function(e){if(void 0===e&&(e=void 0),!xt(this))throw Vt("values");var r,t,n,o,a,i=function(e,r){I(e,r);var t=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(t)}}(e,"First parameter");return r=this,t=i.preventCancel,n=G(r),o=new ve(n,t),(a=Object.create(ye))._asyncIteratorImpl=o,a},ReadableStream.prototype[he]=function(e){return this.values(e)},ReadableStream.from=function(e){return jt(e)},ReadableStream}();function Ft(e,r,t,n,o){void 0===n&&(n=1),void 0===o&&(o=function(){return 1});var a=Object.create(Dt.prototype);return Mt(a),Et(a,Object.create(vt.prototype),e,r,t,n,o),a}function It(e,r,t){var n=Object.create(Dt.prototype);return Mt(n),nr(n,Object.create(Oe.prototype),e,r,t,0,void 0),n}function Mt(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function xt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof Dt)}function Yt(e){return void 0!==e._reader}function Qt(e,r){if(e._disturbed=!0,"closed"===e._state)return d(void 0);if("errored"===e._state)return b(e._storedError);Nt(e);var n=e._reader;if(void 0!==n&&dr(n)){var o=n._readIntoRequests;n._readIntoRequests=new R,o.forEach((function(e){e._closeSteps(void 0)}))}return v(e._readableStreamController[C](r),t)}function Nt(e){e._state="closed";var r=e._reader;if(void 0!==r&&(L(r),ne(r))){var t=r._readRequests;r._readRequests=new R,t.forEach((function(e){e._closeSteps()}))}}function Ht(e,r){e._state="errored",e._storedError=r;var t=e._reader;void 0!==t&&(z(t,r),ne(t)?ae(t,r):pr(t,r))}function Vt(e){return new TypeError("ReadableStream.prototype.".concat(e," can only be used on a ReadableStream"))}function Ut(e,r){I(e,r);var t=null==e?void 0:e.highWaterMark;return Q(t,"highWaterMark","QueuingStrategyInit"),{highWaterMark:N(t)}}Object.defineProperties(Dt,{from:{enumerable:!0}}),Object.defineProperties(Dt.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),a(Dt.from,"from"),a(Dt.prototype.cancel,"cancel"),a(Dt.prototype.getReader,"getReader"),a(Dt.prototype.pipeThrough,"pipeThrough"),a(Dt.prototype.pipeTo,"pipeTo"),a(Dt.prototype.tee,"tee"),a(Dt.prototype.values,"values"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Dt.prototype,r.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(Dt.prototype,he,{value:Dt.prototype.values,writable:!0,configurable:!0});var Gt=function(e){return e.byteLength};a(Gt,"size");var Xt=function(){function ByteLengthQueuingStrategy(e){Y(e,1,"ByteLengthQueuingStrategy"),e=Ut(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(ByteLengthQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!Kt(this))throw Jt("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(ByteLengthQueuingStrategy.prototype,"size",{get:function(){if(!Kt(this))throw Jt("size");return Gt},enumerable:!1,configurable:!0}),ByteLengthQueuingStrategy}();function Jt(e){return new TypeError("ByteLengthQueuingStrategy.prototype.".concat(e," can only be used on a ByteLengthQueuingStrategy"))}function Kt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof Xt)}Object.defineProperties(Xt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Xt.prototype,r.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});var Zt=function(){return 1};a(Zt,"size");var $t=function(){function CountQueuingStrategy(e){Y(e,1,"CountQueuingStrategy"),e=Ut(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(CountQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!rn(this))throw en("highWaterMark");return this._countQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(CountQueuingStrategy.prototype,"size",{get:function(){if(!rn(this))throw en("size");return Zt},enumerable:!1,configurable:!0}),CountQueuingStrategy}();function en(e){return new TypeError("CountQueuingStrategy.prototype.".concat(e," can only be used on a CountQueuingStrategy"))}function rn(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof $t)}function tn(e,r,t){return M(e,t),function(t){return w(e,r,[t])}}function nn(e,r,t){return M(e,t),function(t){return S(e,r,[t])}}function on(e,r,t){return M(e,t),function(t,n){return w(e,r,[t,n])}}function an(e,r,t){return M(e,t),function(t){return w(e,r,[t])}}Object.defineProperties($t.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty($t.prototype,r.toStringTag,{value:"CountQueuingStrategy",configurable:!0});var ln=function(){function TransformStream(e,r,t){void 0===e&&(e={}),void 0===r&&(r={}),void 0===t&&(t={}),void 0===e&&(e=null);var n=vr(r,"Second parameter"),o=vr(t,"Third parameter"),a=function(e,r){I(e,r);var t=null==e?void 0:e.cancel,n=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,a=null==e?void 0:e.start,i=null==e?void 0:e.transform,l=null==e?void 0:e.writableType;return{cancel:void 0===t?void 0:an(t,e,"".concat(r," has member 'cancel' that")),flush:void 0===n?void 0:tn(n,e,"".concat(r," has member 'flush' that")),readableType:o,start:void 0===a?void 0:nn(a,e,"".concat(r," has member 'start' that")),transform:void 0===i?void 0:on(i,e,"".concat(r," has member 'transform' that")),writableType:l}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");var i,l=_r(o,0),u=mr(o),s=_r(n,1),c=mr(n);!function(e,r,t,n,o,a){function i(){return r}function l(r){return function(e,r){var t=e._transformStreamController;if(e._backpressure){return v(e._backpressureChangePromise,(function(){var n=e._writable;if("erroring"===n._state)throw n._storedError;return mn(t,r)}))}return mn(t,r)}(e,r)}function u(r){return function(e,r){var t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;var n=e._readable;t._finishPromise=f((function(e,r){t._finishPromise_resolve=e,t._finishPromise_reject=r}));var o=t._cancelAlgorithm(r);return hn(t),h(o,(function(){return"errored"===n._state?gn(t,n._storedError):(Pt(n._readableStreamController,r),yn(t)),null}),(function(e){return Pt(n._readableStreamController,e),gn(t,e),null})),t._finishPromise}(e,r)}function s(){return function(e){var r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;var t=e._readable;r._finishPromise=f((function(e,t){r._finishPromise_resolve=e,r._finishPromise_reject=t}));var n=r._flushAlgorithm();return hn(r),h(n,(function(){return"errored"===t._state?gn(r,t._storedError):(Rt(t._readableStreamController),yn(r)),null}),(function(e){return Pt(t._readableStreamController,e),gn(r,e),null})),r._finishPromise}(e)}function c(){return function(e){return dn(e,!1),e._backpressureChangePromise}(e)}function d(r){return function(e,r){var t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;var n=e._writable;t._finishPromise=f((function(e,r){t._finishPromise_resolve=e,t._finishPromise_reject=r}));var o=t._cancelAlgorithm(r);return hn(t),h(o,(function(){return"errored"===n._state?gn(t,n._storedError):(Zr(n._writableStreamController,r),fn(e),yn(t)),null}),(function(r){return Zr(n._writableStreamController,r),fn(e),gn(t,r),null})),t._finishPromise}(e,r)}e._writable=function(e,r,t,n,o,a){void 0===o&&(o=1),void 0===a&&(a=function(){return 1});var i=Object.create(Pr.prototype);return qr(i),Gr(i,Object.create(Vr.prototype),e,r,t,n,o,a),i}(i,l,s,u,t,n),e._readable=Ft(i,c,d,o,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,dn(e,!0),e._transformStreamController=void 0}(this,f((function(e){i=e})),s,c,l,u),function(e,r){var t,n,o,a=Object.create(bn.prototype);t=void 0!==r.transform?function(e){return r.transform(e,a)}:function(e){try{return _n(a,e),d(void 0)}catch(e){return b(e)}};n=void 0!==r.flush?function(){return r.flush(a)}:function(){return d(void 0)};o=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return d(void 0)};!function(e,r,t,n,o){r._controlledTransformStream=e,e._transformStreamController=r,r._transformAlgorithm=t,r._flushAlgorithm=n,r._cancelAlgorithm=o,r._finishPromise=void 0,r._finishPromise_resolve=void 0,r._finishPromise_reject=void 0}(e,a,t,n,o)}(this,a),void 0!==a.start?i(a.start(this._transformStreamController)):i(void 0)}return Object.defineProperty(TransformStream.prototype,"readable",{get:function(){if(!un(this))throw Sn("readable");return this._readable},enumerable:!1,configurable:!0}),Object.defineProperty(TransformStream.prototype,"writable",{get:function(){if(!un(this))throw Sn("writable");return this._writable},enumerable:!1,configurable:!0}),TransformStream}();function un(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof ln)}function sn(e,r){Pt(e._readable._readableStreamController,r),cn(e,r)}function cn(e,r){hn(e._transformStreamController),Zr(e._writable._writableStreamController,r),fn(e)}function fn(e){e._backpressure&&dn(e,!1)}function dn(e,r){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=f((function(r){e._backpressureChangePromise_resolve=r})),e._backpressure=r}Object.defineProperties(ln.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(ln.prototype,r.toStringTag,{value:"TransformStream",configurable:!0});var bn=function(){function TransformStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(TransformStreamDefaultController.prototype,"desiredSize",{get:function(){if(!pn(this))throw vn("desiredSize");return Ct(this._controlledTransformStream._readable._readableStreamController)},enumerable:!1,configurable:!0}),TransformStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!pn(this))throw vn("enqueue");_n(this,e)},TransformStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!pn(this))throw vn("error");var r;r=e,sn(this._controlledTransformStream,r)},TransformStreamDefaultController.prototype.terminate=function(){if(!pn(this))throw vn("terminate");!function(e){var r=e._controlledTransformStream;Rt(r._readable._readableStreamController);var t=new TypeError("TransformStream terminated");cn(r,t)}(this)},TransformStreamDefaultController}();function pn(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof bn)}function hn(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function _n(e,r){var t=e._controlledTransformStream,n=t._readable._readableStreamController;if(!qt(n))throw new TypeError("Readable side is not in a state that permits enqueue");try{Tt(n,r)}catch(e){throw cn(t,e),t._readable._storedError}var o=function(e){return!St(e)}(n);o!==t._backpressure&&dn(t,!0)}function mn(e,r){return v(e._transformAlgorithm(r),void 0,(function(r){throw sn(e._controlledTransformStream,r),r}))}function vn(e){return new TypeError("TransformStreamDefaultController.prototype.".concat(e," can only be used on a TransformStreamDefaultController"))}function yn(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function gn(e,r){void 0!==e._finishPromise_reject&&(y(e._finishPromise),e._finishPromise_reject(r),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function Sn(e){return new TypeError("TransformStream.prototype.".concat(e," can only be used on a TransformStream"))}Object.defineProperties(bn.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),a(bn.prototype.enqueue,"enqueue"),a(bn.prototype.error,"error"),a(bn.prototype.terminate,"terminate"),"symbol"==typeof r.toStringTag&&Object.defineProperty(bn.prototype,r.toStringTag,{value:"TransformStreamDefaultController",configurable:!0}),e.ByteLengthQueuingStrategy=Xt,e.CountQueuingStrategy=$t,e.ReadableByteStreamController=Oe,e.ReadableStream=Dt,e.ReadableStreamBYOBReader=fr,e.ReadableStreamBYOBRequest=Ee,e.ReadableStreamDefaultController=vt,e.ReadableStreamDefaultReader=te,e.TransformStream=ln,e.TransformStreamDefaultController=bn,e.WritableStream=Pr,e.WritableStreamDefaultController=Vr,e.WritableStreamDefaultWriter=Fr}));
