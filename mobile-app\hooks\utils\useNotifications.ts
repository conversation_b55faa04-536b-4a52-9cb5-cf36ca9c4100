// =====================================================
// HOOK NOTIFICATIONS PUSH
// =====================================================

import { useEffect, useState, useRef } from 'react';
import { Platform, Alert } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';

// Configuration des notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

interface NotificationData {
  title: string;
  body: string;
  data?: any;
  sound?: boolean;
  badge?: number;
}

interface UseNotificationsReturn {
  expoPushToken: string | null;
  notification: Notifications.Notification | null;
  sendNotification: (data: NotificationData) => Promise<void>;
  scheduleNotification: (data: NotificationData, trigger: Notifications.NotificationTriggerInput) => Promise<string>;
  cancelNotification: (identifier: string) => Promise<void>;
  cancelAllNotifications: () => Promise<void>;
  getBadgeCount: () => Promise<number>;
  setBadgeCount: (count: number) => Promise<void>;
  requestPermissions: () => Promise<boolean>;
}

export const useNotifications = (): UseNotificationsReturn => {
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
  const [notification, setNotification] = useState<Notifications.Notification | null>(null);
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  useEffect(() => {
    registerForPushNotificationsAsync().then(token => {
      setExpoPushToken(token || null);
    });

    // Écouter les notifications reçues
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      setNotification(notification);
      console.log('📱 Notification reçue:', notification);
    });

    // Écouter les interactions avec les notifications
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('👆 Notification cliquée:', response);
      handleNotificationResponse(response);
    });

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, []);

  const requestPermissions = async (): Promise<boolean> => {
    try {
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      if (Device.isDevice) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;
        
        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }
        
        if (finalStatus !== 'granted') {
          Alert.alert(
            'Permissions requises',
            'Les notifications sont nécessaires pour vous tenir informé de vos commandes.',
            [
              { text: 'Plus tard', style: 'cancel' },
              { text: 'Paramètres', onPress: () => Notifications.requestPermissionsAsync() },
            ]
          );
          return false;
        }
        
        return true;
      } else {
        console.log('Les notifications push ne fonctionnent que sur un appareil physique');
        return false;
      }
    } catch (error) {
      console.error('Erreur permissions notifications:', error);
      return false;
    }
  };

  const sendNotification = async (data: NotificationData): Promise<void> => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: data.title,
          body: data.body,
          data: data.data || {},
          sound: data.sound !== false,
          badge: data.badge,
        },
        trigger: null, // Immédiat
      });
      
      console.log('✅ Notification envoyée:', data.title);
    } catch (error) {
      console.error('❌ Erreur envoi notification:', error);
    }
  };

  const scheduleNotification = async (
    data: NotificationData,
    trigger: Notifications.NotificationTriggerInput
  ): Promise<string> => {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: data.title,
          body: data.body,
          data: data.data || {},
          sound: data.sound !== false,
          badge: data.badge,
        },
        trigger,
      });
      
      console.log('⏰ Notification programmée:', identifier);
      return identifier;
    } catch (error) {
      console.error('❌ Erreur programmation notification:', error);
      throw error;
    }
  };

  const cancelNotification = async (identifier: string): Promise<void> => {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
      console.log('🚫 Notification annulée:', identifier);
    } catch (error) {
      console.error('❌ Erreur annulation notification:', error);
    }
  };

  const cancelAllNotifications = async (): Promise<void> => {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('🚫 Toutes les notifications annulées');
    } catch (error) {
      console.error('❌ Erreur annulation notifications:', error);
    }
  };

  const getBadgeCount = async (): Promise<number> => {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error('❌ Erreur récupération badge:', error);
      return 0;
    }
  };

  const setBadgeCount = async (count: number): Promise<void> => {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('❌ Erreur mise à jour badge:', error);
    }
  };

  return {
    expoPushToken,
    notification,
    sendNotification,
    scheduleNotification,
    cancelNotification,
    cancelAllNotifications,
    getBadgeCount,
    setBadgeCount,
    requestPermissions,
  };
};

// Fonction utilitaire pour obtenir le token push
async function registerForPushNotificationsAsync(): Promise<string | undefined> {
  let token;

  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      console.log('Permission notifications refusée');
      return;
    }
    
    try {
      const projectId = Constants.expoConfig?.extra?.eas?.projectId ?? Constants.easConfig?.projectId;
      if (!projectId) {
        throw new Error('Project ID not found');
      }
      
      token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
      console.log('🔑 Token push obtenu:', token);
    } catch (error) {
      console.error('❌ Erreur obtention token push:', error);
    }
  } else {
    console.log('Les notifications push ne fonctionnent que sur un appareil physique');
  }

  return token;
}

// Fonction pour gérer les réponses aux notifications
function handleNotificationResponse(response: Notifications.NotificationResponse) {
  const data = response.notification.request.content.data;
  
  // Router vers l'écran approprié selon le type de notification
  if (data.type === 'order_update') {
    // Naviguer vers l'écran de suivi de commande
    console.log('Navigation vers commande:', data.orderId);
  } else if (data.type === 'new_order') {
    // Naviguer vers l'écran des commandes (pour les marchands/livreurs)
    console.log('Navigation vers nouvelle commande:', data.orderId);
  } else if (data.type === 'delivery_update') {
    // Naviguer vers l'écran de livraison
    console.log('Navigation vers livraison:', data.deliveryId);
  }
}

// Fonctions utilitaires pour les notifications courantes
export const NotificationTemplates = {
  orderConfirmed: (orderNumber: string): NotificationData => ({
    title: '✅ Commande confirmée',
    body: `Votre commande #${orderNumber} a été confirmée et est en préparation.`,
    data: { type: 'order_update', orderNumber },
  }),

  orderReady: (orderNumber: string): NotificationData => ({
    title: '🍽️ Commande prête',
    body: `Votre commande #${orderNumber} est prête pour la livraison.`,
    data: { type: 'order_update', orderNumber },
  }),

  deliveryStarted: (orderNumber: string, delivererName: string): NotificationData => ({
    title: '🚚 Livraison en cours',
    body: `${delivererName} a récupéré votre commande #${orderNumber} et se dirige vers vous.`,
    data: { type: 'delivery_update', orderNumber },
  }),

  deliveryCompleted: (orderNumber: string): NotificationData => ({
    title: '🎉 Livraison terminée',
    body: `Votre commande #${orderNumber} a été livrée avec succès !`,
    data: { type: 'order_update', orderNumber },
  }),

  newOrderForMerchant: (orderNumber: string, customerName: string): NotificationData => ({
    title: '🔔 Nouvelle commande',
    body: `Nouvelle commande #${orderNumber} de ${customerName}`,
    data: { type: 'new_order', orderNumber },
  }),

  newDeliveryRequest: (orderNumber: string, distance: string): NotificationData => ({
    title: '📦 Nouvelle livraison',
    body: `Livraison disponible #${orderNumber} à ${distance}`,
    data: { type: 'new_delivery', orderNumber },
  }),
};

export default useNotifications;
