// =====================================================
// HOME SCREEN DATA SERVICE - FALLBACK VERSION
// =====================================================

export interface Category {
  id: string;
  name: string;
  emoji: string | null;
  color: string | null;
  count: number;
}

export interface Merchant {
  id: string;
  name: string;
  image_url: string | null;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  category: string | null;
  isPromoted: boolean;
  description: string | null;
  address: string | null;
  is_open: boolean;
}

export interface Promotion {
  id: string;
  title: string;
  description: string | null;
  image_url: string | null;
  discount: string;
  merchantId: string | null;
  merchant_name?: string;
}

export class HomeServiceFallback {
  /**
   * Fetch all active categories with merchant count
   */
  static async getCategories(): Promise<Category[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return [
      { id: '1', name: 'Restaurants', emoji: '🍽️', color: '#FF6B6B', count: 12 },
      { id: '2', name: 'Pharmacie', emoji: '💊', color: '#4ECDC4', count: 8 },
      { id: '3', name: 'Épicerie', emoji: '🛒', color: '#45B7D1', count: 15 },
      { id: '4', name: 'Beauté', emoji: '💄', color: '#F7DC6F', count: 6 },
      { id: '5', name: 'Électronique', emoji: '📱', color: '#BB8FCE', count: 4 },
      { id: '6', name: 'Vêtements', emoji: '👕', color: '#85C1E9', count: 9 },
    ];
  }

  /**
   * Fetch active promotions with merchant info
   */
  static async getPromotions(): Promise<Promotion[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    return [
      {
        id: '1',
        title: 'Livraison Gratuite',
        description: 'Sur votre première commande de plus de 2000 FCFA',
        image_url: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400',
        discount: 'Gratuit',
        merchantId: '1',
        merchant_name: 'Chez Fatou Restaurant',
      },
      {
        id: '2',
        title: 'Menu du Jour -20%',
        description: 'Réduction sur tous les plats principaux',
        image_url: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400',
        discount: '-20%',
        merchantId: '2',
        merchant_name: 'Le Lagon Restaurant',
      },
      {
        id: '3',
        title: 'Pharmacie Express',
        description: 'Livraison en 15 minutes pour les urgences',
        image_url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400',
        discount: 'Express',
        merchantId: '3',
        merchant_name: 'Pharmacie du Plateau',
      },
    ];
  }

  /**
   * Fetch nearby merchants
   */
  static async getNearbyMerchants(limit: number = 10): Promise<Merchant[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));
    
    const allMerchants = [
      {
        id: '1',
        name: 'Chez Fatou Restaurant',
        image_url: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400',
        rating: 4.8,
        deliveryTime: '25-35 min',
        deliveryFee: 500,
        category: 'Restaurants',
        isPromoted: true,
        description: 'Cuisine sénégalaise authentique avec les meilleurs thieboudienne et yassa de Dakar',
        address: 'Plateau, Dakar, Sénégal',
        is_open: true,
      },
      {
        id: '2',
        name: 'Le Lagon Restaurant',
        image_url: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400',
        rating: 4.6,
        deliveryTime: '30-45 min',
        deliveryFee: 750,
        category: 'Restaurants',
        isPromoted: false,
        description: 'Restaurant de fruits de mer avec vue sur l\'océan Atlantique',
        address: 'Almadies, Dakar, Sénégal',
        is_open: true,
      },
      {
        id: '3',
        name: 'Pharmacie du Plateau',
        image_url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400',
        rating: 4.7,
        deliveryTime: '15-25 min',
        deliveryFee: 300,
        category: 'Pharmacie',
        isPromoted: false,
        description: 'Pharmacie moderne avec service de livraison rapide 24h/24',
        address: 'Avenue Léopold Sédar Senghor, Dakar',
        is_open: true,
      },
      {
        id: '4',
        name: 'Marché Bio Dakar',
        image_url: 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=400',
        rating: 4.5,
        deliveryTime: '30-45 min',
        deliveryFee: 400,
        category: 'Épicerie',
        isPromoted: false,
        description: 'Produits biologiques et locaux, fruits et légumes frais du Sénégal',
        address: 'Marché Sandaga, Dakar',
        is_open: true,
      },
      {
        id: '5',
        name: 'Beauty Palace Dakar',
        image_url: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400',
        rating: 4.4,
        deliveryTime: '20-30 min',
        deliveryFee: 600,
        category: 'Beauté',
        isPromoted: false,
        description: 'Cosmétiques africains et internationaux, soins capillaires spécialisés',
        address: 'Rue Parchappe, Dakar',
        is_open: true,
      },
    ];

    return allMerchants.slice(0, limit);
  }

  /**
   * Search merchants by category
   */
  static async getMerchantsByCategory(categoryId: string): Promise<Merchant[]> {
    const allMerchants = await this.getNearbyMerchants(20);
    
    // Simple category filtering based on category name
    const categoryMap: { [key: string]: string } = {
      '1': 'Restaurants',
      '2': 'Pharmacie',
      '3': 'Épicerie',
      '4': 'Beauté',
      '5': 'Électronique',
      '6': 'Vêtements',
    };

    const targetCategory = categoryMap[categoryId];
    if (!targetCategory) return allMerchants;

    return allMerchants.filter(merchant => merchant.category === targetCategory);
  }

  /**
   * Get merchant details by ID
   */
  static async getMerchantById(merchantId: string) {
    const merchants = await this.getNearbyMerchants(20);
    const merchant = merchants.find(m => m.id === merchantId);
    
    if (!merchant) return null;

    // Add sample products
    return {
      ...merchant,
      products: [
        {
          id: '1',
          name: 'Thieboudienne Rouge',
          description: 'Plat national sénégalais avec riz, poisson et légumes dans une sauce tomate savoureuse',
          price: 2500,
          image_url: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400',
          is_available: true,
          is_featured: true,
          preparation_time: 30,
        },
        {
          id: '2',
          name: 'Yassa Poulet',
          description: 'Poulet mariné aux oignons et citron, accompagné de riz blanc parfumé',
          price: 2000,
          image_url: 'https://images.unsplash.com/photo-1598515214211-89d3c73ae83b?w=400',
          is_available: true,
          is_featured: true,
          preparation_time: 25,
        },
      ],
    };
  }
}
