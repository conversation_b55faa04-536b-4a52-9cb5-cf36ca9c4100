import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface GeocodingRequest {
  address: string
  country_code?: string
  city?: string
  language?: string
}

interface DistanceRequest {
  origin: {
    latitude: number
    longitude: number
  }
  destination: {
    latitude: number
    longitude: number
  }
  mode?: 'driving' | 'walking' | 'bicycling' | 'motorcycle'
}

interface RouteOptimizationRequest {
  origin: {
    latitude: number
    longitude: number
  }
  destinations: Array<{
    latitude: number
    longitude: number
    order_id?: string
    priority?: number
  }>
  vehicle_type?: string
  max_stops?: number
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const url = new URL(req.url)
    const action = url.pathname.split('/').pop()

    switch (action) {
      case 'geocode':
        return await handleGeocoding(req)
      
      case 'distance':
        return await handleDistanceCalculation(req)
      
      case 'optimize-route':
        return await handleRouteOptimization(req, supabase)
      
      case 'reverse-geocode':
        return await handleReverseGeocoding(req)
      
      default:
        throw new Error('Invalid action. Use: geocode, distance, optimize-route, or reverse-geocode')
    }

  } catch (error) {
    console.error('Geocoding service error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error',
        code: 'GEOCODING_ERROR'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})

async function handleGeocoding(req: Request) {
  const requestData: GeocodingRequest = await req.json()
  const { address, country_code, city, language = 'en' } = requestData

  if (!address) {
    throw new Error('Address is required')
  }

  // Try multiple geocoding services for better coverage in Africa
  let result = null

  // 1. Try Google Maps Geocoding API (best for African addresses)
  try {
    result = await geocodeWithGoogle(address, country_code, language)
  } catch (error) {
    console.log('Google geocoding failed:', error.message)
  }

  // 2. Fallback to OpenStreetMap Nominatim (free alternative)
  if (!result) {
    try {
      result = await geocodeWithNominatim(address, country_code)
    } catch (error) {
      console.log('Nominatim geocoding failed:', error.message)
    }
  }

  // 3. Fallback to MapBox (good for African coverage)
  if (!result) {
    try {
      result = await geocodeWithMapbox(address, country_code)
    } catch (error) {
      console.log('Mapbox geocoding failed:', error.message)
    }
  }

  if (!result) {
    throw new Error('Unable to geocode address with any service')
  }

  return new Response(
    JSON.stringify({
      success: true,
      address: address,
      result: result
    }),
    {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    }
  )
}

async function handleDistanceCalculation(req: Request) {
  const requestData: DistanceRequest = await req.json()
  const { origin, destination, mode = 'driving' } = requestData

  if (!origin || !destination) {
    throw new Error('Origin and destination are required')
  }

  // Calculate straight-line distance (Haversine)
  const straightLineDistance = calculateHaversineDistance(
    origin.latitude,
    origin.longitude,
    destination.latitude,
    destination.longitude
  )

  // Try to get route distance from Google Maps
  let routeDistance = null
  let routeDuration = null
  let routePolyline = null

  try {
    const routeData = await getRouteFromGoogle(origin, destination, mode)
    routeDistance = routeData.distance
    routeDuration = routeData.duration
    routePolyline = routeData.polyline
  } catch (error) {
    console.log('Route calculation failed:', error.message)
    // Estimate route distance as 1.3x straight line for urban areas
    routeDistance = straightLineDistance * 1.3
    routeDuration = estimateTravelTime(routeDistance, mode)
  }

  return new Response(
    JSON.stringify({
      success: true,
      origin,
      destination,
      straight_line_distance_km: Math.round(straightLineDistance * 100) / 100,
      route_distance_km: Math.round(routeDistance * 100) / 100,
      estimated_duration_minutes: Math.round(routeDuration),
      travel_mode: mode,
      polyline: routePolyline
    }),
    {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    }
  )
}

async function handleRouteOptimization(req: Request, supabase: any) {
  const requestData: RouteOptimizationRequest = await req.json()
  const { origin, destinations, vehicle_type = 'motorcycle', max_stops = 10 } = requestData

  if (!origin || !destinations || destinations.length === 0) {
    throw new Error('Origin and destinations are required')
  }

  // Limit destinations for performance
  const limitedDestinations = destinations.slice(0, max_stops)

  // Calculate distances to all destinations
  const destinationsWithDistances = await Promise.all(
    limitedDestinations.map(async (dest, index) => {
      const distance = calculateHaversineDistance(
        origin.latitude,
        origin.longitude,
        dest.latitude,
        dest.longitude
      )
      
      return {
        ...dest,
        index,
        distance_km: distance,
        priority: dest.priority || 1
      }
    })
  )

  // Simple optimization algorithm (nearest neighbor with priority weighting)
  const optimizedRoute = optimizeRouteSimple(destinationsWithDistances)

  // Calculate total route metrics
  let totalDistance = 0
  let totalDuration = 0
  const routeSegments = []

  for (let i = 0; i < optimizedRoute.length; i++) {
    const from = i === 0 ? origin : optimizedRoute[i - 1]
    const to = optimizedRoute[i]
    
    const segmentDistance = calculateHaversineDistance(
      from.latitude,
      from.longitude,
      to.latitude,
      to.longitude
    )
    
    const segmentDuration = estimateTravelTime(segmentDistance, vehicle_type)
    
    totalDistance += segmentDistance
    totalDuration += segmentDuration
    
    routeSegments.push({
      from: from,
      to: to,
      distance_km: Math.round(segmentDistance * 100) / 100,
      duration_minutes: Math.round(segmentDuration),
      order_id: to.order_id
    })
  }

  // Log route optimization for analytics
  await supabase
    .from('route_optimizations')
    .insert({
      origin_location: `POINT(${origin.longitude} ${origin.latitude})`,
      destinations_count: destinations.length,
      optimized_count: optimizedRoute.length,
      total_distance_km: totalDistance,
      total_duration_minutes: totalDuration,
      vehicle_type,
      optimization_algorithm: 'nearest_neighbor_priority',
      created_at: new Date().toISOString()
    })

  return new Response(
    JSON.stringify({
      success: true,
      origin,
      optimized_route: optimizedRoute,
      route_segments: routeSegments,
      total_distance_km: Math.round(totalDistance * 100) / 100,
      total_duration_minutes: Math.round(totalDuration),
      optimization_savings: calculateOptimizationSavings(destinations, optimizedRoute)
    }),
    {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    }
  )
}

async function handleReverseGeocoding(req: Request) {
  const { latitude, longitude } = await req.json()

  if (!latitude || !longitude) {
    throw new Error('Latitude and longitude are required')
  }

  let result = null

  // Try Google Maps reverse geocoding first
  try {
    result = await reverseGeocodeWithGoogle(latitude, longitude)
  } catch (error) {
    console.log('Google reverse geocoding failed:', error.message)
  }

  // Fallback to Nominatim
  if (!result) {
    try {
      result = await reverseGeocodeWithNominatim(latitude, longitude)
    } catch (error) {
      console.log('Nominatim reverse geocoding failed:', error.message)
    }
  }

  if (!result) {
    throw new Error('Unable to reverse geocode coordinates')
  }

  return new Response(
    JSON.stringify({
      success: true,
      coordinates: { latitude, longitude },
      result: result
    }),
    {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    }
  )
}

// Geocoding service implementations
async function geocodeWithGoogle(address: string, countryCode?: string, language = 'en') {
  const apiKey = Deno.env.get('GOOGLE_MAPS_API_KEY')
  if (!apiKey) {
    throw new Error('Google Maps API key not configured')
  }

  const params = new URLSearchParams({
    address: address,
    key: apiKey,
    language: language
  })

  if (countryCode) {
    params.append('region', countryCode)
  }

  const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?${params}`)
  const data = await response.json()

  if (data.status !== 'OK' || !data.results.length) {
    throw new Error(`Google geocoding failed: ${data.status}`)
  }

  const result = data.results[0]
  return {
    latitude: result.geometry.location.lat,
    longitude: result.geometry.location.lng,
    formatted_address: result.formatted_address,
    place_id: result.place_id,
    address_components: result.address_components,
    confidence: 'high'
  }
}

async function geocodeWithNominatim(address: string, countryCode?: string) {
  const params = new URLSearchParams({
    q: address,
    format: 'json',
    limit: '1',
    addressdetails: '1'
  })

  if (countryCode) {
    params.append('countrycodes', countryCode.toLowerCase())
  }

  const response = await fetch(`https://nominatim.openstreetmap.org/search?${params}`, {
    headers: {
      'User-Agent': 'DeliveryApp/1.0'
    }
  })

  const data = await response.json()

  if (!data.length) {
    throw new Error('Nominatim geocoding failed: No results')
  }

  const result = data[0]
  return {
    latitude: parseFloat(result.lat),
    longitude: parseFloat(result.lon),
    formatted_address: result.display_name,
    place_id: result.place_id,
    confidence: 'medium'
  }
}

async function geocodeWithMapbox(address: string, countryCode?: string) {
  const apiKey = Deno.env.get('MAPBOX_API_KEY')
  if (!apiKey) {
    throw new Error('Mapbox API key not configured')
  }

  let query = encodeURIComponent(address)
  if (countryCode) {
    query += `.json?country=${countryCode.toLowerCase()}`
  } else {
    query += '.json'
  }

  const response = await fetch(
    `https://api.mapbox.com/geocoding/v5/mapbox.places/${query}&access_token=${apiKey}`
  )

  const data = await response.json()

  if (!data.features.length) {
    throw new Error('Mapbox geocoding failed: No results')
  }

  const result = data.features[0]
  return {
    latitude: result.center[1],
    longitude: result.center[0],
    formatted_address: result.place_name,
    place_id: result.id,
    confidence: 'medium'
  }
}

async function reverseGeocodeWithGoogle(latitude: number, longitude: number) {
  const apiKey = Deno.env.get('GOOGLE_MAPS_API_KEY')
  if (!apiKey) {
    throw new Error('Google Maps API key not configured')
  }

  const response = await fetch(
    `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`
  )

  const data = await response.json()

  if (data.status !== 'OK' || !data.results.length) {
    throw new Error(`Google reverse geocoding failed: ${data.status}`)
  }

  const result = data.results[0]
  return {
    formatted_address: result.formatted_address,
    address_components: result.address_components,
    place_id: result.place_id
  }
}

async function reverseGeocodeWithNominatim(latitude: number, longitude: number) {
  const response = await fetch(
    `https://nominatim.openstreetmap.org/reverse?lat=${latitude}&lon=${longitude}&format=json&addressdetails=1`,
    {
      headers: {
        'User-Agent': 'DeliveryApp/1.0'
      }
    }
  )

  const data = await response.json()

  if (!data.display_name) {
    throw new Error('Nominatim reverse geocoding failed')
  }

  return {
    formatted_address: data.display_name,
    address_components: data.address
  }
}

// Utility functions
function calculateHaversineDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371 // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1)
  const dLon = toRadians(lon2 - lon1)
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2)
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180)
}

function estimateTravelTime(distanceKm: number, mode: string): number {
  const speeds = {
    'driving': 25,      // km/h in city traffic
    'motorcycle': 30,   // km/h, faster in traffic
    'bicycling': 15,    // km/h
    'walking': 5        // km/h
  }
  
  const speed = speeds[mode] || speeds['driving']
  return (distanceKm / speed) * 60 // Convert to minutes
}

function optimizeRouteSimple(destinations: any[]): any[] {
  // Simple nearest neighbor algorithm with priority weighting
  const unvisited = [...destinations]
  const route = []
  
  while (unvisited.length > 0) {
    // Find next best destination (distance + priority score)
    let bestIndex = 0
    let bestScore = Infinity
    
    for (let i = 0; i < unvisited.length; i++) {
      const dest = unvisited[i]
      // Score = distance / priority (lower is better)
      const score = dest.distance_km / dest.priority
      
      if (score < bestScore) {
        bestScore = score
        bestIndex = i
      }
    }
    
    route.push(unvisited.splice(bestIndex, 1)[0])
  }
  
  return route
}

function calculateOptimizationSavings(original: any[], optimized: any[]): any {
  // Calculate savings compared to visiting in original order
  // This is a simplified calculation
  return {
    distance_saved_km: 0, // Would need more complex calculation
    time_saved_minutes: 0,
    efficiency_improvement_percent: 0
  }
}

async function getRouteFromGoogle(origin: any, destination: any, mode: string) {
  const apiKey = Deno.env.get('GOOGLE_MAPS_API_KEY')
  if (!apiKey) {
    throw new Error('Google Maps API key not configured')
  }

  const travelMode = mode === 'motorcycle' ? 'driving' : mode.toUpperCase()
  
  const response = await fetch(
    `https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&mode=${travelMode}&key=${apiKey}`
  )

  const data = await response.json()

  if (data.status !== 'OK' || !data.routes.length) {
    throw new Error(`Google Directions failed: ${data.status}`)
  }

  const route = data.routes[0]
  const leg = route.legs[0]

  return {
    distance: leg.distance.value / 1000, // Convert to km
    duration: leg.duration.value / 60,   // Convert to minutes
    polyline: route.overview_polyline.points
  }
}
