// =====================================================
// ÉCRAN ACCUEIL CLIENT - US-C004
// =====================================================

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  FlatList,
  Image,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '../../../store/authStore';
import { useOrderStore } from '../../../store/orderStore';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

interface Category {
  id: string;
  name: string;
  emoji: string;
  color: string;
  count: number;
}

interface Merchant {
  id: string;
  name: string;
  image: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  category: string;
  isPromoted?: boolean;
}

interface Promotion {
  id: string;
  title: string;
  description: string;
  image: string;
  discount: string;
  merchantId: string;
}

const categories: Category[] = [
  { id: '1', name: 'Restaurants', emoji: '🍽️', color: '#FF6B6B', count: 120 },
  { id: '2', name: 'Pharmacie', emoji: '💊', color: '#4ECDC4', count: 45 },
  { id: '3', name: 'Épicerie', emoji: '🛒', color: '#45B7D1', count: 89 },
  { id: '4', name: 'Beauté', emoji: '💄', color: '#F7DC6F', count: 67 },
  { id: '5', name: 'Électronique', emoji: '📱', color: '#BB8FCE', count: 34 },
  { id: '6', name: 'Vêtements', emoji: '👕', color: '#85C1E9', count: 78 },
];

const promotions: Promotion[] = [
  {
    id: '1',
    title: 'Livraison Gratuite',
    description: 'Sur votre première commande',
    image: 'https://via.placeholder.com/300x150',
    discount: '-100%',
    merchantId: '1',
  },
  {
    id: '2',
    title: 'Menu du Jour',
    description: 'Chez Chez Fatou Restaurant',
    image: 'https://via.placeholder.com/300x150',
    discount: '-20%',
    merchantId: '2',
  },
];

const nearbyMerchants: Merchant[] = [
  {
    id: '1',
    name: 'Chez Fatou Restaurant',
    image: 'https://via.placeholder.com/100x100',
    rating: 4.8,
    deliveryTime: '25-35 min',
    deliveryFee: 500,
    category: 'Restaurant',
    isPromoted: true,
  },
  {
    id: '2',
    name: 'Pharmacie du Plateau',
    image: 'https://via.placeholder.com/100x100',
    rating: 4.6,
    deliveryTime: '15-25 min',
    deliveryFee: 300,
    category: 'Pharmacie',
  },
  {
    id: '3',
    name: 'Marché Bio Dakar',
    image: 'https://via.placeholder.com/100x100',
    rating: 4.7,
    deliveryTime: '30-45 min',
    deliveryFee: 400,
    category: 'Épicerie',
  },
];

export default function HomeScreen() {
  const { user } = useAuthStore();
  const { cart } = useOrderStore();
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const onRefresh = async () => {
    setRefreshing(true);
    // Simuler le rechargement des données
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleCategoryPress = (category: Category) => {
    router.push({
      pathname: '/(client)/(tabs)/search',
      params: { category: category.id },
    });
  };

  const handleMerchantPress = (merchant: Merchant) => {
    router.push(`/(client)/restaurant/${merchant.id}`);
  };

  const handlePromotionPress = (promotion: Promotion) => {
    router.push(`/(client)/restaurant/${promotion.merchantId}`);
  };

  const handleSearchPress = () => {
    router.push('/(client)/(tabs)/search');
  };

  const handleCartPress = () => {
    router.push('/(client)/cart');
  };

  const renderCategory = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={[styles.categoryCard, { backgroundColor: item.color }]}
      onPress={() => handleCategoryPress(item)}
    >
      <Text style={styles.categoryEmoji}>{item.emoji}</Text>
      <Text style={styles.categoryName}>{item.name}</Text>
      <Text style={styles.categoryCount}>{item.count} options</Text>
    </TouchableOpacity>
  );

  const renderPromotion = ({ item }: { item: Promotion }) => (
    <TouchableOpacity
      style={styles.promotionCard}
      onPress={() => handlePromotionPress(item)}
    >
      <Image source={{ uri: item.image }} style={styles.promotionImage} />
      <View style={styles.promotionOverlay}>
        <View style={styles.promotionBadge}>
          <Text style={styles.promotionDiscount}>{item.discount}</Text>
        </View>
        <View style={styles.promotionContent}>
          <Text style={styles.promotionTitle}>{item.title}</Text>
          <Text style={styles.promotionDescription}>{item.description}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderMerchant = ({ item }: { item: Merchant }) => (
    <TouchableOpacity
      style={styles.merchantCard}
      onPress={() => handleMerchantPress(item)}
    >
      <Image source={{ uri: item.image }} style={styles.merchantImage} />
      <View style={styles.merchantInfo}>
        <View style={styles.merchantHeader}>
          <Text style={styles.merchantName}>{item.name}</Text>
          {item.isPromoted && (
            <View style={styles.promotedBadge}>
              <Text style={styles.promotedText}>Sponsorisé</Text>
            </View>
          )}
        </View>
        <View style={styles.merchantMeta}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color="#FFD700" />
            <Text style={styles.rating}>{item.rating}</Text>
          </View>
          <Text style={styles.deliveryTime}>{item.deliveryTime}</Text>
          <Text style={styles.deliveryFee}>{item.deliveryFee} FCFA</Text>
        </View>
        <Text style={styles.merchantCategory}>{item.category}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.greeting}>Bonjour,</Text>
          <Text style={styles.userName}>{user?.profile?.firstName || 'Utilisateur'} 👋</Text>
        </View>
        <View style={styles.headerRight}>
          {cart && cart.items.length > 0 && (
            <TouchableOpacity style={styles.cartButton} onPress={handleCartPress}>
              <Ionicons name="bag" size={24} color="#007AFF" />
              <View style={styles.cartBadge}>
                <Text style={styles.cartBadgeText}>{cart.items.length}</Text>
              </View>
            </TouchableOpacity>
          )}
          <TouchableOpacity style={styles.notificationButton}>
            <Ionicons name="notifications" size={24} color="#8E8E93" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Barre de recherche */}
        <TouchableOpacity style={styles.searchContainer} onPress={handleSearchPress}>
          <Input
            placeholder="Rechercher restaurants, produits..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            leftIcon="search"
            editable={false}
            pointerEvents="none"
          />
        </TouchableOpacity>

        {/* Promotions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔥 Offres du moment</Text>
          <FlatList
            data={promotions}
            renderItem={renderPromotion}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.promotionsList}
          />
        </View>

        {/* Catégories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🏪 Catégories</Text>
          <FlatList
            data={categories}
            renderItem={renderCategory}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>

        {/* Marchands à proximité */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>📍 Près de chez vous</Text>
            <Button
              title="Voir tout"
              variant="ghost"
              size="small"
              onPress={() => router.push('/(client)/(tabs)/search')}
            />
          </View>
          <FlatList
            data={nearbyMerchants}
            renderItem={renderMerchant}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    fontSize: 14,
    color: '#8E8E93',
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1C1E',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  cartButton: {
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#FF3B30',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  notificationButton: {
    padding: 4,
  },
  content: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  section: {
    marginTop: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1C1C1E',
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  promotionsList: {
    paddingHorizontal: 24,
    gap: 16,
  },
  promotionCard: {
    width: 280,
    height: 140,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  promotionImage: {
    width: '100%',
    height: '100%',
  },
  promotionOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'space-between',
    padding: 16,
  },
  promotionBadge: {
    alignSelf: 'flex-start',
    backgroundColor: '#FF3B30',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  promotionDiscount: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  promotionContent: {
    alignSelf: 'flex-start',
  },
  promotionTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  promotionDescription: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  categoriesList: {
    paddingHorizontal: 24,
    gap: 12,
  },
  categoryCard: {
    width: 100,
    height: 100,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
  },
  categoryEmoji: {
    fontSize: 24,
    marginBottom: 8,
  },
  categoryName: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  categoryCount: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 10,
    textAlign: 'center',
  },
  merchantCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 24,
    marginBottom: 12,
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  merchantImage: {
    width: 60,
    height: 60,
    borderRadius: 12,
    marginRight: 16,
  },
  merchantInfo: {
    flex: 1,
  },
  merchantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  merchantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    flex: 1,
  },
  promotedBadge: {
    backgroundColor: '#FF9500',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  promotedText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '500',
  },
  merchantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  rating: {
    fontSize: 12,
    color: '#8E8E93',
  },
  deliveryTime: {
    fontSize: 12,
    color: '#8E8E93',
  },
  deliveryFee: {
    fontSize: 12,
    color: '#34C759',
    fontWeight: '500',
  },
  merchantCategory: {
    fontSize: 12,
    color: '#8E8E93',
  },
});
