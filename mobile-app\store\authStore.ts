// =====================================================
// STORE AUTHENTIFICATION - ZUSTAND
// =====================================================

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthStore, User, AuthSession, LoginCredentials, RegisterCredentials } from '../types/auth';

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // État initial
      user: null,
      session: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions d'authentification
      login: async (credentials: LoginCredentials) => {
        try {
          set({ isLoading: true, error: null });

          // Simulation de connexion pour le développement
          const mockUser: User = {
            id: `user_${Date.now()}`,
            email: credentials.email,
            role: 'client',
            profile: {
              firstName: 'Utilisateur',
              lastName: 'Test',
              phone: '+221701234567',
              language: 'fr',
              country: 'SN',
              city: 'Dakar',
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isActive: true,
            isVerified: true,
          };

          const mockSession: AuthSession = {
            user: mockUser,
            accessToken: `token_${Date.now()}`,
            refreshToken: `refresh_${Date.now()}`,
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          };

          set({
            user: mockUser,
            session: mockSession,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          console.log('✅ Connexion simulée réussie');
        } catch (error) {
          console.error('❌ Erreur login store:', error);
          set({
            error: 'Erreur lors de la connexion',
            isLoading: false,
          });
        }
      },

      register: async (credentials: RegisterCredentials) => {
        try {
          set({ isLoading: true, error: null });

          // Validation des mots de passe
          if (credentials.password !== credentials.confirmPassword) {
            set({ error: 'Les mots de passe ne correspondent pas', isLoading: false });
            return;
          }

          // Simulation d'inscription pour le développement
          const mockUser: User = {
            id: `user_${Date.now()}`,
            email: credentials.email,
            role: credentials.role,
            profile: {
              firstName: credentials.profile.firstName || 'Nouvel',
              lastName: credentials.profile.lastName || 'Utilisateur',
              phone: credentials.profile.phone || '+221701234567',
              language: 'fr',
              country: 'SN',
              city: 'Dakar',
              ...credentials.profile,
            },
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isActive: true,
            isVerified: false,
          };

          const mockSession: AuthSession = {
            user: mockUser,
            accessToken: `token_${Date.now()}`,
            refreshToken: `refresh_${Date.now()}`,
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          };

          set({
            user: mockUser,
            session: mockSession,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          console.log('✅ Inscription simulée réussie');
        } catch (error) {
          console.error('❌ Erreur register store:', error);
          set({
            error: 'Erreur lors de l\'inscription',
            isLoading: false,
          });
        }
      },

      logout: async () => {
        try {
          set({ isLoading: true });

          // Simulation de déconnexion
          set({
            user: null,
            session: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });

          console.log('✅ Déconnexion simulée réussie');
        } catch (error) {
          console.error('❌ Erreur logout store:', error);
          set({
            error: 'Erreur lors de la déconnexion',
            isLoading: false,
          });
        }
      },

      refreshToken: async () => {
        try {
          // Simulation de refresh token
          const currentUser = get().user;
          const currentSession = get().session;

          if (currentUser && currentSession) {
            // Prolonger la session
            const updatedSession: AuthSession = {
              ...currentSession,
              expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            };

            set({
              session: updatedSession,
              isAuthenticated: true,
            });
          }
        } catch (error) {
          console.error('❌ Erreur refresh token:', error);
          set({
            user: null,
            session: null,
            isAuthenticated: false,
          });
        }
      },

      updateProfile: async (profileUpdates) => {
        try {
          set({ isLoading: true, error: null });

          const currentUser = get().user;
          if (!currentUser) {
            set({ error: 'Utilisateur non connecté', isLoading: false });
            return;
          }

          // Simuler la mise à jour du profil
          const updatedUser: User = {
            ...currentUser,
            profile: {
              ...currentUser.profile,
              ...profileUpdates,
            },
            updatedAt: new Date().toISOString(),
          };

          set({
            user: updatedUser,
            isLoading: false,
          });

          console.log('✅ Profil mis à jour:', profileUpdates);
        } catch (error) {
          console.error('❌ Erreur update profile:', error);
          set({
            error: 'Erreur lors de la mise à jour du profil',
            isLoading: false,
          });
        }
      },

      resetPassword: async (email: string) => {
        try {
          set({ isLoading: true, error: null });

          // Simuler l'envoi d'email de réinitialisation
          console.log('📧 Email de réinitialisation envoyé à:', email);

          set({ isLoading: false });
        } catch (error) {
          console.error('❌ Erreur reset password:', error);
          set({
            error: 'Erreur lors de l\'envoi de l\'email',
            isLoading: false,
          });
        }
      },

      verifyEmail: async (token: string) => {
        try {
          set({ isLoading: true, error: null });

          // Simuler la vérification d'email
          console.log('✅ Email vérifié avec le token:', token);

          const currentUser = get().user;
          if (currentUser) {
            set({
              user: {
                ...currentUser,
                isVerified: true,
              },
            });
          }

          set({ isLoading: false });
        } catch (error) {
          console.error('❌ Erreur verify email:', error);
          set({
            error: 'Erreur lors de la vérification',
            isLoading: false,
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        session: state.session,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Sélecteurs pour optimiser les re-renders
export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useAuthLoading = () => useAuthStore((state) => state.isLoading);
export const useAuthError = () => useAuthStore((state) => state.error);

// Actions
export const useAuthActions = () => useAuthStore((state) => ({
  login: state.login,
  register: state.register,
  logout: state.logout,
  refreshToken: state.refreshToken,
  updateProfile: state.updateProfile,
  resetPassword: state.resetPassword,
  verifyEmail: state.verifyEmail,
  clearError: state.clearError,
}));

export default useAuthStore;
