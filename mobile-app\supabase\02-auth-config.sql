-- =====================================================
-- CONFIGURATION AUTHENTIFICATION SUPABASE
-- =====================================================

-- Configuration des providers d'authentification
-- À exécuter via le dashboard Supabase ou l'API

-- =====================================================
-- TRIGGERS POUR GESTION AUTOMATIQUE DES PROFILS
-- =====================================================

-- Fonction pour créer automatiquement un profil lors de l'inscription
CREATE OR REPLACE FUNCTION public.handle_new_user()
R<PERSON>URNS trigger AS $$
DECLARE
    default_country_id UUID;
BEGIN
    -- Obtenir le pays par défaut (Sénégal par exemple)
    SELECT id INTO default_country_id 
    FROM countries 
    WHERE code = 'SEN' 
    LIMIT 1;

    -- Créer le profil utilisateur
    INSERT INTO public.profiles (
        id,
        role,
        phone_number,
        country_id,
        first_name,
        last_name,
        preferred_language,
        is_verified,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        'customer', -- Rôle par défaut
        COALESCE(NEW.phone, ''), -- Numéro de téléphone depuis auth
        default_country_id,
        COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'preferred_language', 'fr')::supported_language,
        false, -- Non vérifié par défaut
        NOW(),
        NOW()
    );

    -- Créer un portefeuille par défaut
    INSERT INTO public.user_wallets (
        user_id,
        balance,
        currency_code,
        is_active,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        0, -- Solde initial à 0
        COALESCE(NEW.raw_user_meta_data->>'currency_code', 'XOF'),
        true,
        NOW(),
        NOW()
    );

    -- Créer les préférences de notification par défaut
    INSERT INTO public.notification_preferences (
        user_id,
        notification_type,
        enabled_channels,
        is_enabled,
        created_at,
        updated_at
    ) VALUES 
    (NEW.id, 'order_confirmed', ARRAY['push', 'sms'], true, NOW(), NOW()),
    (NEW.id, 'order_preparing', ARRAY['push'], true, NOW(), NOW()),
    (NEW.id, 'order_ready', ARRAY['push', 'sms'], true, NOW(), NOW()),
    (NEW.id, 'order_delivered', ARRAY['push', 'sms'], true, NOW(), NOW()),
    (NEW.id, 'payment_successful', ARRAY['push'], true, NOW(), NOW()),
    (NEW.id, 'payment_failed', ARRAY['push', 'sms'], true, NOW(), NOW()),
    (NEW.id, 'rider_assigned', ARRAY['push'], true, NOW(), NOW()),
    (NEW.id, 'promotion_available', ARRAY['push'], false, NOW(), NOW());

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger pour créer automatiquement un profil
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- FONCTION POUR MISE À JOUR DU PROFIL
-- =====================================================

CREATE OR REPLACE FUNCTION public.update_user_profile(
    p_first_name TEXT DEFAULT NULL,
    p_last_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_preferred_language supported_language DEFAULT NULL,
    p_avatar_url TEXT DEFAULT NULL,
    p_default_address TEXT DEFAULT NULL,
    p_default_location GEOMETRY DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    updated_profile profiles%ROWTYPE;
BEGIN
    -- Vérifier que l'utilisateur est authentifié
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;

    -- Mettre à jour le profil
    UPDATE profiles SET
        first_name = COALESCE(p_first_name, first_name),
        last_name = COALESCE(p_last_name, last_name),
        phone_number = COALESCE(p_phone_number, phone_number),
        preferred_language = COALESCE(p_preferred_language, preferred_language),
        avatar_url = COALESCE(p_avatar_url, avatar_url),
        default_address = COALESCE(p_default_address, default_address),
        default_location = COALESCE(p_default_location, default_location),
        updated_at = NOW()
    WHERE id = auth.uid()
    RETURNING * INTO updated_profile;

    -- Retourner le profil mis à jour
    RETURN row_to_json(updated_profile);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FONCTION POUR CHANGER DE RÔLE (ADMIN SEULEMENT)
-- =====================================================

CREATE OR REPLACE FUNCTION public.change_user_role(
    p_user_id UUID,
    p_new_role user_role
)
RETURNS JSON AS $$
DECLARE
    current_user_role user_role;
    updated_profile profiles%ROWTYPE;
BEGIN
    -- Vérifier que l'utilisateur actuel est admin
    SELECT role INTO current_user_role
    FROM profiles
    WHERE id = auth.uid();

    IF current_user_role NOT IN ('admin', 'super_admin') THEN
        RAISE EXCEPTION 'Insufficient permissions';
    END IF;

    -- Mettre à jour le rôle
    UPDATE profiles SET
        role = p_new_role,
        updated_at = NOW()
    WHERE id = p_user_id
    RETURNING * INTO updated_profile;

    -- Si le nouveau rôle est delivery_rider, créer l'entrée correspondante
    IF p_new_role = 'delivery_rider' THEN
        INSERT INTO delivery_riders (
            id,
            service_city_id,
            current_status,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            p_user_id,
            (SELECT default_city_id FROM profiles WHERE id = p_user_id),
            'offline',
            true,
            NOW(),
            NOW()
        ) ON CONFLICT (id) DO NOTHING;
    END IF;

    RETURN row_to_json(updated_profile);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FONCTION POUR VÉRIFICATION OTP SMS
-- =====================================================

CREATE OR REPLACE FUNCTION public.verify_phone_otp(
    p_phone_number TEXT,
    p_otp_code TEXT
)
RETURNS JSON AS $$
DECLARE
    is_valid BOOLEAN := false;
    user_profile profiles%ROWTYPE;
BEGIN
    -- Ici vous intégreriez avec votre service SMS (Twilio, Africa's Talking, etc.)
    -- Pour la démo, on accepte le code "123456"
    IF p_otp_code = '123456' THEN
        is_valid := true;
    END IF;

    IF NOT is_valid THEN
        RAISE EXCEPTION 'Invalid OTP code';
    END IF;

    -- Marquer le numéro comme vérifié
    UPDATE profiles SET
        is_verified = true,
        phone_number = p_phone_number,
        updated_at = NOW()
    WHERE id = auth.uid()
    RETURNING * INTO user_profile;

    RETURN json_build_object(
        'success', true,
        'message', 'Phone number verified successfully',
        'profile', row_to_json(user_profile)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FONCTION POUR GESTION DES SESSIONS
-- =====================================================

CREATE OR REPLACE FUNCTION public.create_user_session(
    p_device_id TEXT,
    p_device_type TEXT,
    p_device_name TEXT DEFAULT NULL,
    p_os_name TEXT DEFAULT NULL,
    p_os_version TEXT DEFAULT NULL,
    p_app_version TEXT DEFAULT NULL,
    p_location GEOMETRY DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    session_record user_sessions%ROWTYPE;
    session_token TEXT;
    refresh_token TEXT;
BEGIN
    -- Vérifier que l'utilisateur est authentifié
    IF auth.uid() IS NULL THEN
        RAISE EXCEPTION 'Not authenticated';
    END IF;

    -- Générer des tokens
    session_token := encode(gen_random_bytes(32), 'base64');
    refresh_token := encode(gen_random_bytes(32), 'base64');

    -- Désactiver les anciennes sessions du même device
    UPDATE user_sessions SET
        is_active = false,
        updated_at = NOW()
    WHERE user_id = auth.uid() 
    AND device_id = p_device_id;

    -- Créer une nouvelle session
    INSERT INTO user_sessions (
        user_id,
        session_token,
        refresh_token,
        device_id,
        device_type,
        device_name,
        os_name,
        os_version,
        app_version,
        ip_address,
        location,
        expires_at,
        created_at,
        updated_at
    ) VALUES (
        auth.uid(),
        session_token,
        refresh_token,
        p_device_id,
        p_device_type,
        p_device_name,
        p_os_name,
        p_os_version,
        p_app_version,
        inet_client_addr(),
        p_location,
        NOW() + INTERVAL '30 days',
        NOW(),
        NOW()
    ) RETURNING * INTO session_record;

    RETURN row_to_json(session_record);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FONCTION POUR NETTOYAGE DES SESSIONS EXPIRÉES
-- =====================================================

CREATE OR REPLACE FUNCTION public.cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Supprimer les sessions expirées
    DELETE FROM user_sessions
    WHERE expires_at < NOW()
    OR (last_activity_at < NOW() - INTERVAL '7 days');

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- FONCTION POUR VALIDATION DES DONNÉES
-- =====================================================

CREATE OR REPLACE FUNCTION public.validate_phone_number(phone TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Validation format international
    RETURN phone ~ '^\+[1-9]\d{1,14}$';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

CREATE OR REPLACE FUNCTION public.validate_email(email TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Validation email basique
    RETURN email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- =====================================================
-- VUES POUR L'AUTHENTIFICATION
-- =====================================================

-- Vue pour obtenir le profil complet de l'utilisateur connecté
CREATE OR REPLACE VIEW public.current_user_profile AS
SELECT 
    p.*,
    c.name_en as country_name,
    c.currency_code,
    c.currency_symbol,
    c.timezone,
    city.name as city_name
FROM profiles p
LEFT JOIN countries c ON p.country_id = c.id
LEFT JOIN cities city ON p.default_city_id = city.id
WHERE p.id = auth.uid();

-- Vue pour les statistiques utilisateur
CREATE OR REPLACE VIEW public.user_stats AS
SELECT 
    p.id,
    p.role,
    COUNT(DISTINCT o.id) as total_orders,
    COUNT(DISTINCT CASE WHEN o.status = 'delivered' THEN o.id END) as completed_orders,
    COALESCE(AVG(CASE WHEN o.status = 'delivered' THEN o.total_amount END), 0) as avg_order_value,
    COALESCE(SUM(CASE WHEN o.status = 'delivered' THEN o.total_amount END), 0) as total_spent,
    MAX(o.created_at) as last_order_date,
    w.balance as wallet_balance
FROM profiles p
LEFT JOIN orders o ON p.id = o.customer_id
LEFT JOIN user_wallets w ON p.id = w.user_id
WHERE p.id = auth.uid()
GROUP BY p.id, p.role, w.balance;

-- =====================================================
-- GRANTS ET PERMISSIONS
-- =====================================================

-- Accorder les permissions nécessaires
GRANT USAGE ON SCHEMA public TO authenticated, anon;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Permissions spécifiques pour les vues
GRANT SELECT ON public.current_user_profile TO authenticated;
GRANT SELECT ON public.user_stats TO authenticated;
