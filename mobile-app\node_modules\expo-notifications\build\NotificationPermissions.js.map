{"version": 3, "file": "NotificationPermissions.js", "sourceRoot": "", "sources": ["../src/NotificationPermissions.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAGlE,OAAO,6BAA6B,MAAM,iCAAiC,CAAC;AAE5E;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB;IACvC,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,EAAE,CAAC;QACvD,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,qBAAqB,CAAC,CAAC;IACxE,CAAC;IAED,OAAO,MAAM,6BAA6B,CAAC,mBAAmB,EAAE,CAAC;AACnE,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAAC,WAA4C;IACxF,IAAI,CAAC,6BAA6B,CAAC,uBAAuB,EAAE,CAAC;QAC3D,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,yBAAyB,CAAC,CAAC;IAC5E,CAAC;IAED,MAAM,oBAAoB,GAAG,WAAW,IAAI;QAC1C,GAAG,EAAE;YACH,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,IAAI;SACjB;KACF,CAAC;IACF,MAAM,4BAA4B,GAChC,oBAAoB,CAAC,QAAQ,CAAC,EAAuC,CAAC,CAAC;IACzE,iFAAiF;IACjF,OAAO,MAAM,6BAA6B,CAAC,uBAAuB,CAAC,4BAA6B,CAAC,CAAC;AACpG,CAAC", "sourcesContent": ["import { Platform, UnavailabilityError } from 'expo-modules-core';\n\nimport { NotificationPermissionsRequest } from './NotificationPermissions.types';\nimport NotificationPermissionsModule from './NotificationPermissionsModule';\n\n/**\n * Calling this function checks current permissions settings related to notifications.\n * It lets you verify whether the app is currently allowed to display alerts, play sounds, etc.\n * There is no user-facing effect of calling this.\n * @return It returns a `Promise` resolving to an object represents permission settings ([`NotificationPermissionsStatus`](#notificationpermissionsstatus)).\n * On iOS, make sure you [properly interpret the permissions response](#interpret-the-ios-permissions-response).\n * @example Check if the app is allowed to send any type of notifications (interrupting and non-interrupting–provisional on iOS).\n * ```ts\n * import * as Notifications from 'expo-notifications';\n *\n * export async function allowsNotificationsAsync() {\n *   const settings = await Notifications.getPermissionsAsync();\n *   return (\n *     settings.granted || settings.ios?.status === Notifications.IosAuthorizationStatus.PROVISIONAL\n *   );\n * }\n * ```\n * @header permissions\n */\nexport async function getPermissionsAsync() {\n  if (!NotificationPermissionsModule.getPermissionsAsync) {\n    throw new UnavailabilityError('Notifications', 'getPermissionsAsync');\n  }\n\n  return await NotificationPermissionsModule.getPermissionsAsync();\n}\n\n/**\n * Prompts the user for notification permissions according to request. **Request defaults to asking the user to allow displaying alerts,\n * setting badge count and playing sounds**.\n * @param permissions An object representing configuration for the request scope.\n * @return It returns a Promise resolving to an object represents permission settings ([`NotificationPermissionsStatus`](#notificationpermissionsstatus)).\n * On iOS, make sure you [properly interpret the permissions response](#interpret-the-ios-permissions-response).\n * @example Prompts the user to allow the app to show alerts, play sounds, set badge count and let Siri read out messages through AirPods.\n * ```ts\n * import * as Notifications from 'expo-notifications';\n *\n * export function requestPermissionsAsync() {\n *   return await Notifications.requestPermissionsAsync({\n *     ios: {\n *       allowAlert: true,\n *       allowBadge: true,\n *       allowSound: true,\n *     },\n *   });\n * }\n * ```\n * @header permissions\n */\nexport async function requestPermissionsAsync(permissions?: NotificationPermissionsRequest) {\n  if (!NotificationPermissionsModule.requestPermissionsAsync) {\n    throw new UnavailabilityError('Notifications', 'requestPermissionsAsync');\n  }\n\n  const requestedPermissions = permissions ?? {\n    ios: {\n      allowAlert: true,\n      allowBadge: true,\n      allowSound: true,\n    },\n  };\n  const requestedPlatformPermissions =\n    requestedPermissions[Platform.OS as keyof typeof requestedPermissions];\n  // TODO(@kitten): This never checks whether the configuration object is undefined\n  return await NotificationPermissionsModule.requestPermissionsAsync(requestedPlatformPermissions!);\n}\n"]}