{"version": 3, "file": "NotificationsHandlerModule.types.js", "sourceRoot": "", "sources": ["../src/NotificationsHandlerModule.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\nimport { NotificationBehavior } from './Notifications.types';\n\nexport interface NotificationsHandlerModule extends ProxyNativeModule {\n  handleNotificationAsync?: (\n    notificationId: string,\n    notificationBehavior: NotificationBehavior\n  ) => Promise<void>;\n}\n"]}