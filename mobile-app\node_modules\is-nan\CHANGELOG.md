1.3.2 / 2020-12-23
=================
  * [meta] do not publish github action workflow files

1.3.1 / 2020-12-23
=================
  * [Fix] avoid mutating `Number.isNaN` when present
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `es5-shim`, `tape`, `functions-have-names`; add `safe-publish-latest`
  * [readme] fix repo URLs, remove defunct badges
  * [Tests] migrate tests to Github Actions
  * [Tests] add `implementation` est; run `es-shim-api` in postlint; use `tape` runner
  * [actions] add "Allow Edits" workflow
  * [actions] switch Automatic Rebase workflow to `pull_request_target` event

1.3.0 / 2019-12-15
=================
  * [New] add "auto" entry point
  * [readme] fix repo URLs
  * [docs] clean up README links/formatting
  * [Deps] update `define-properties`
  * [meta] add `funding` field; FUNDING.yml
  * [meta] Only apps should have lockfiles
  * [<PERSON>] update `eslint`, `@ljharb/eslint-config`, `covert`, `es5-shim`, `tape`, `@es-shims/api`
  * [Tests] use shared travis-ci configs
  * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops
  * [Tests] use `functions-have-names`
  * [Tests] remove `jscs`
  * [actions] add automatic rebasing / merge commit blocking

1.2.1 / 2015-08-16
=================
  * [Docs] Update readme

1.2.0 / 2015-08-16
=================
  * [New] Implement the [es-shim API](es-shims/api) interface
  * [Dev Deps] update `eslint`, `tape`, `es5-shim`, `@ljharb/eslint-config`
  * [Tests] up to `io.js` `v3.0`
  * [Docs] Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG
  * [Security] Add `npm run security`

1.1.0 / 2015-06-24
=================
  * Add a "shim" method
  * Add `npm run eslint`
  * Test latest `node` and `io.js` on `travis-ci`
  * Add license and download badges to README
  * Update `tape`, `covert`, `jscs`

1.0.1 / 2014-07-05
=================
  * Oops, jscs should be a devDependency

1.0.0 / 2014-07-05
=================
  * Initial release.
